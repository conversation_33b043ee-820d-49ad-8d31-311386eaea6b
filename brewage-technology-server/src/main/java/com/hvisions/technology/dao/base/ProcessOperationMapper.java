package com.hvisions.technology.dao.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.technology.dto.base.operationcard.ProcessOperationExcel;
import com.hvisions.technology.dto.base.operationcard.ProcessOperationExportExcel;
import com.hvisions.technology.dto.base.operationcard.ProcessOperationQueryDTO;
import com.hvisions.technology.entity.base.ProcessOperation;
import com.hvisions.technology.vo.base.operationcard.OperationCardVO;
import com.hvisions.technology.vo.base.operationcard.ProcessOperationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProcessOperationMapper extends BaseMapper<ProcessOperation> {

    /**
     * 查询工艺操作列表
     * @param page
     * @param queryDTO
     * @return
     */
    List<ProcessOperationVO> selectPageList(Page page, @Param("query") ProcessOperationQueryDTO queryDTO);

    /**
     * 导出-工艺操作列表
     * @param exportExcel
     * @return
     */
    List<ProcessOperationExcel> selectExportExcel(@Param("query") ProcessOperationExportExcel exportExcel);
}