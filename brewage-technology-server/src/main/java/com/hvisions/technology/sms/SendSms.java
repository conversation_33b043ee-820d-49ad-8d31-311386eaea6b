package com.hvisions.technology.sms;

import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;


/**
 * Tencent Cloud Sms Sendsms
 */
@Slf4j
public class SendSms {
    /**
     * 发送短信
     *
     * @param phoneNumberSet
     */
    public static void sendNote(String templateId,String[] templateParamSet, String[] phoneNumberSet) {
        try {
            String secretId = "AKIDCD3hCdwKYaQIxPm4DBwzoQC0Pgrz0pfV";
            String secretKey = "jNxMYMwJSN3XnjT3QpkmhR1TuBFCtCNJ";
            Credential cred = new Credential(secretId, secretKey);

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setReqMethod("POST");
            httpProfile.setConnTimeout(60);
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();

            clientProfile.setSignMethod("HmacSHA256");
            clientProfile.setHttpProfile(httpProfile);
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);

            SendSmsRequest req = new SendSmsRequest();

            String sdkAppId = "1400747897";
            req.setSmsSdkAppId(sdkAppId);

            String signName = "泸州老窖";
            req.setSignName(signName);

            req.setTemplateId(templateId);
            req.setTemplateParamSet(templateParamSet);
            req.setPhoneNumberSet(phoneNumberSet);

            String sessionContext = "";
            req.setSessionContext(sessionContext);

            String extendCode = "";
            req.setExtendCode(extendCode);

            String senderid = "";
            req.setSenderId(senderid);
            log.info("调用短信接口，传入参数：{}",JSONObject.toJSONString(req));
            SendSmsResponse res = client.SendSms(req);
            // 输出json格式的字符串回包
            String response = SendSmsResponse.toJsonString(res);
            SmsVo smsVo = JSONObject.parseObject(response, SmsVo.class);
            Set<SmsVo.SendStatusSet> sets = smsVo.getSendStatusSet();
            for (SmsVo.SendStatusSet set : sets) {
                String code = set.getCode();
                log.info("收到短信返回接口，内容如下:{}",JSONObject.toJSONString(set));
            }
        } catch (TencentCloudSDKException e) {
            log.info("发送短信异常，异常内容如下:{}",e.getMessage());
            e.printStackTrace();
        }
    }
}
