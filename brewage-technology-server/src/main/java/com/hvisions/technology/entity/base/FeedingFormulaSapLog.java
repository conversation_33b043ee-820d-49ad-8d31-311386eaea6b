package com.hvisions.technology.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.technology.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
    * 投料配方-SAP同步日志
    */
@ApiModel(value="投料配方-SAP同步日志")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "base_feeding_formula_sap_log")
public class FeedingFormulaSapLog extends SysBase {
    /**
     * 糟源类型
     */
    @TableField(value = "bad_source_type")
    @ApiModelProperty(value="糟源类型")
    private String badSourceType;

    /**
     * 同步时间
     */
    @TableField(value = "sync_time")
    @ApiModelProperty(value="同步时间")
    private Date syncTime;

    /**
     * 同步内容
     */
    @TableField(value = "sync_content")
    @ApiModelProperty(value="同步内容")
    private String syncContent;
}