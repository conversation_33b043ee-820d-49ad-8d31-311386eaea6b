package com.hvisions.technology.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.technology.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工艺操作参数
 */
@ApiModel(value = "工艺操作参数")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "base_process_operation_parameter")
public class ProcessOperationParameter extends SysBase {
    /**
     * 工艺操作主键id
     */
    @TableField(value = "process_operation_id")
    @ApiModelProperty(value = "工艺操作主键id")
    private Integer processOperationId;

    /**
     * 参数编码
     */
    @TableField(value = "param_code")
    @ApiModelProperty(value = "参数编码")
    private String paramCode;

    /**
     * 参数名称
     */
    @TableField(value = "param_name")
    @ApiModelProperty(value = "参数名称")
    private String paramName;

    /**
     * 采集方式
     */
    @TableField(value = "collection_method")
    @ApiModelProperty(value = "采集方式")
    private String collectionMethod;

    /**
     * 用途
     */
    @TableField(value = "purpose")
    @ApiModelProperty(value = "用途")
    private String purpose;

    /**
     * 单位
     */
    @TableField(value = "unit")
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 参数类型
     */
    @TableField(value = "param_type")
    @ApiModelProperty(value = "参数类型")
    private String paramType;

    /**
     * 是否运行超差
     */
    @TableField(value = "is_out_of_tolerance")
    @ApiModelProperty(value = "是否运行超差")
    private String isOutOfTolerance;

    /**
     * 数据类型
     */
    @TableField(value = "data_type")
    @ApiModelProperty(value = "数据类型")
    private String dataType;
}