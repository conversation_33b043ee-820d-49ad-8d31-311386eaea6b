package com.hvisions.technology.service.proc;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.role.RoleDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.technology.advice.UserAuditorAware;
import com.hvisions.technology.dao.proc.FormulaAdjustmentMapper;
import com.hvisions.technology.dto.appr.processapproval.ProcessApprovalDTO;
import com.hvisions.technology.dto.appr.processapproval.ProcessApprovalExecuteDTO;
import com.hvisions.technology.dto.proc.formulaadjustment.*;
import com.hvisions.technology.entity.proc.FormulaAdjustment;
import com.hvisions.technology.enums.*;
import com.hvisions.technology.service.appr.ProcessApprovalService;
import com.hvisions.technology.utils.BaseWrapper;
import com.hvisions.technology.utils.FlowGenerationUtil;
import com.hvisions.technology.vo.appr.processapproval.ProcessApprovalVO;
import com.hvisions.technology.vo.proc.formulaadjustment.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: FormulaAdjustmentService
 * @description: 配方调整Service
 * @date 2025/6/11 19:47
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class FormulaAdjustmentService {
    @Resource
    private FormulaAdjustmentMapper formulaAdjustmentMapper;

    @Resource
    private ProcessApprovalService processApprovalService;

    @Resource
    private BaseWrapper baseWrapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private UserClient userClient;

    /**
     * 查询配方调整
     *
     * @param queryDTO
     * @return
     */
    public Page<FormulaAdjustmentVO> findFormulaAdjustmentList(FormulaAdjustmentQueryDTO queryDTO) {
        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FormulaAdjustment::getDeleted, 0)
                .like(StringUtils.isNotBlank(queryDTO.getApprovalNo()), FormulaAdjustment::getApprovalNo, queryDTO.getApprovalNo())
                .eq(StringUtils.isNotBlank(queryDTO.getWorkshop()), FormulaAdjustment::getWorkshop, queryDTO.getWorkshop())
                .eq(StringUtils.isNotBlank(queryDTO.getCenter()), FormulaAdjustment::getCenter, queryDTO.getCenter())
                .eq(StringUtils.isNotBlank(queryDTO.getRouteName()), FormulaAdjustment::getRouteName, queryDTO.getRouteName())
                .like(StringUtils.isNotBlank(queryDTO.getOperationName()), FormulaAdjustment::getOperationName, queryDTO.getOperationName())
                .like(StringUtils.isNotBlank(queryDTO.getSubmitter()), FormulaAdjustment::getSubmitter, queryDTO.getSubmitter())
                .apply(null != queryDTO.getSubmitStartTime(), "DATE_FORMAT( submit_time, '%Y-%m-%d' ) BETWEEN DATE_FORMAT( {0}, '%Y-%m-%d' ) and DATE_FORMAT( {1}, '%Y-%m-%d' )", queryDTO.getSubmitStartTime(), queryDTO.getSubmitEndTime());
        IPage<FormulaAdjustment> page = formulaAdjustmentMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);

        return baseWrapper.convertToPage(page, FormulaAdjustmentVO.class);
    }


    /**
     * 查询配方调整-数量统计
     *
     * @param queryDTO
     * @return
     */
    public AdjustmentNumCountVO findNumCount(FormulaAdjustmentQueryCountDTO queryDTO) {

        AdjustmentNumCountVO adjustmentNumCountVO = new AdjustmentNumCountVO();

        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FormulaAdjustment::getDeleted, 0)
                .like(StringUtils.isNotBlank(queryDTO.getApprovalNo()), FormulaAdjustment::getApprovalNo, queryDTO.getApprovalNo())
                .eq(StringUtils.isNotBlank(queryDTO.getWorkshop()), FormulaAdjustment::getWorkshop, queryDTO.getWorkshop())
                .eq(StringUtils.isNotBlank(queryDTO.getCenter()), FormulaAdjustment::getCenter, queryDTO.getCenter())
                .eq(StringUtils.isNotBlank(queryDTO.getRouteName()), FormulaAdjustment::getRouteName, queryDTO.getRouteName())
                .like(StringUtils.isNotBlank(queryDTO.getOperationName()), FormulaAdjustment::getOperationName, queryDTO.getOperationName())
                .like(StringUtils.isNotBlank(queryDTO.getSubmitter()), FormulaAdjustment::getSubmitter, queryDTO.getSubmitter())
                .apply(null != queryDTO.getSubmitStartTime(), "DATE_FORMAT( submit_time, '%Y-%m-%d' ) BETWEEN DATE_FORMAT( {0}, '%Y-%m-%d' ) and DATE_FORMAT( {1}, '%Y-%m-%d' )", queryDTO.getSubmitStartTime(), queryDTO.getSubmitEndTime());
        List<FormulaAdjustment> adjustmentList = formulaAdjustmentMapper.selectList(wrapper);

        if (!CollectionUtils.isEmpty(adjustmentList)) {

            //审批状态
            List<AdjustmentNumCountApprovalStatusVO> approvalStatusVOList = new ArrayList<>();

            //调整参数
            List<AdjustmentNumCountParamVO> paramVOList = new ArrayList<>();

            //调整参数比例
            List<AdjustmentNumCountParamRatioVO> paramRatioVOList = new ArrayList<>();

            //中心比例
            List<AdjustmentNumCountCenterRatioVO> centerRatioVOList = new ArrayList<>();


            //审批状态-求和
            Map<String, Integer> collect = adjustmentList.stream().collect(Collectors.groupingBy(FormulaAdjustment::getApprovalStatus, Collectors.summingInt(e -> 1)));
            collect.forEach((k, v) -> {
                AdjustmentNumCountApprovalStatusVO approvalStatusVO = new AdjustmentNumCountApprovalStatusVO();
                approvalStatusVO.setApprovalStatus(k);
                approvalStatusVO.setNumCount(v);
                approvalStatusVOList.add(approvalStatusVO);
            });


            //调整参数-求和 and 调整参数-比例
            Map<String, Integer> collect1 = adjustmentList.stream().collect(Collectors.groupingBy(FormulaAdjustment::getParamName, Collectors.summingInt(e -> 1)));
            collect1.forEach((k, v) -> {
                //调整参数-求和
                AdjustmentNumCountParamVO paramVO = new AdjustmentNumCountParamVO();
                paramVO.setParamName(k);
                paramVO.setNumCount(v);
                paramVOList.add(paramVO);

                //调整参数-比例
                AdjustmentNumCountParamRatioVO paramRatioVO = new AdjustmentNumCountParamRatioVO();
                paramRatioVO.setParamName(k);
                String numRatio = calculatePercentage(v, adjustmentList.size());
                paramRatioVO.setNumRatio(numRatio);
                paramRatioVOList.add(paramRatioVO);
            });

            //中心-比例
            Map<String, Integer> collect2 = adjustmentList.stream().collect(Collectors.groupingBy(FormulaAdjustment::getCenter, Collectors.summingInt(e -> 1)));
            collect2.forEach((k, v) -> {
                AdjustmentNumCountCenterRatioVO countCenterRatioVO = new AdjustmentNumCountCenterRatioVO();
                countCenterRatioVO.setCenter(k);
                String numRatio = calculatePercentage(v, adjustmentList.size());
                countCenterRatioVO.setNumRatio(numRatio);
                centerRatioVOList.add(countCenterRatioVO);
            });

            adjustmentNumCountVO.setApprovalStatus(approvalStatusVOList);
            adjustmentNumCountVO.setParamVOList(paramVOList);
            adjustmentNumCountVO.setParamRatioVOList(paramRatioVOList);
            adjustmentNumCountVO.setCenterRatioVOList(centerRatioVOList);
        }

        return adjustmentNumCountVO;
    }

    /**
     * 计算百分比
     *
     * @param part  计算值
     * @param total 总数
     * @return
     */
    public static String calculatePercentage(int part, int total) {
        if (total == 0) {
            return "0.00%"; // 避免除以零
        }
        double percentage = (part * 100.0) / total;
        return String.format("%.2f%%", percentage);
    }

    /**
     * 新增配方调整
     *
     * @param addDTO
     * @return
     */
    public String addFormulaAdjustment(FormulaAdjustmentAddDTO addDTO) {
        log.info("调用新增配方调整，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增配方调整，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FormulaAdjustment::getDeleted, 0)
                .notIn(FormulaAdjustment::getApprovalStatus, ApprovalStatus.CLOSED.getName(), ApprovalStatus.REJECTED.getName(), ApprovalStatus.WITHDRAWN.getName())
                .eq(FormulaAdjustment::getWorkshop, addDTO.getWorkshop())
                .eq(FormulaAdjustment::getCenter, addDTO.getCenter())
                .eq(FormulaAdjustment::getRouteCode, addDTO.getRouteCode())
                .eq(FormulaAdjustment::getRouteName, addDTO.getRouteName())
                .eq(FormulaAdjustment::getOperationCode, addDTO.getOperationCode())
                .eq(FormulaAdjustment::getParamCode, addDTO.getParamCode())
                .le(FormulaAdjustment::getEffectiveStartTime, addDTO.getEffectiveStartTime())
                .ge(FormulaAdjustment::getExpiryTime, addDTO.getExpiryTime());
        Integer count = formulaAdjustmentMapper.selectCount(wrapper);
        if (count > 0) {
            FailureCode addFailedDataExists = FailureCode.ADD_FAILED_DATA_EXISTS_MSG;
            throw new BaseKnownException(addFailedDataExists.getCode(), addFailedDataExists.getDescription("当前中心+工艺参数下生效的时间范围已存在"));
        }

        FormulaAdjustment adjustment = DtoMapper.convert(addDTO, FormulaAdjustment.class);
        adjustment.setCreateTime(new Date());
        adjustment.setCreatorId(userId);
        adjustment.setApprovalStatus(ApprovalStatus.NOT_STARTED.getName());

        //查询当日最大的流水号
        Integer maxSerialNumber = formulaAdjustmentMapper.findMaxSerialNumber(ApprovalBusinessTypeEnum.PFTZ);
        //生成审批流单号
        String approvalNo = FlowGenerationUtil.generation(ApprovalBusinessTypeEnum.PFTZ, maxSerialNumber);
        adjustment.setApprovalNo(approvalNo);
        formulaAdjustmentMapper.insert(adjustment);

        //新增审批步骤
        ProcessApprovalDTO approvalDTO = new ProcessApprovalDTO();
        approvalDTO.setApprovalNo(approvalNo);
        approvalDTO.setUserAddDTOList(addDTO.getUserDTOList());
        approvalDTO.setProcessBusiness(MqExchangelEnum.FORMULA_ADJUSTMENT.getType());
        processApprovalService.addProcessApproval(approvalDTO);

        //直接发起
        if (addDTO.getIsType()) {
            initiateApproval(approvalNo);
        }

        return OperationResult.ADD_SUCCESS_MSG.getDescription("审批单号=" + approvalNo);
    }

    /**
     * 修改配方调整
     *
     * @param updateDTO
     * @return
     */
    public String updateFormulaAdjustment(FormulaAdjustmentUpdateDTO updateDTO) {
        log.info("调用修改配方调整，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改配方调整，获取到当前登录用户，用户id：{}", userId);

        FormulaAdjustment formulaAdjustment = formulaAdjustmentMapper.selectById(updateDTO.getId());
        if (null == formulaAdjustment || formulaAdjustment.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }

        //流程不是未发起状态
        if (!formulaAdjustment.getApprovalStatus().equals(ApprovalStatus.NOT_STARTED.getName())) {
            FailureCode addFailedDataExists = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(addFailedDataExists.getCode(), addFailedDataExists.getDescription("修改", "当前流程不是未发起状态"));
        }

        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FormulaAdjustment::getDeleted, 0)
                .ne(FormulaAdjustment::getId, updateDTO.getId())
                .notIn(FormulaAdjustment::getApprovalStatus, ApprovalStatus.CLOSED.getName(), ApprovalStatus.REJECTED.getName(), ApprovalStatus.WITHDRAWN.getName())
                .eq(FormulaAdjustment::getWorkshop, updateDTO.getWorkshop())
                .eq(FormulaAdjustment::getCenter, updateDTO.getCenter())
                .eq(FormulaAdjustment::getRouteCode, updateDTO.getRouteCode())
                .eq(FormulaAdjustment::getRouteName, updateDTO.getRouteName())
                .eq(FormulaAdjustment::getOperationCode, updateDTO.getOperationCode())
                .eq(FormulaAdjustment::getParamCode, updateDTO.getParamCode())
                .le(FormulaAdjustment::getEffectiveStartTime, updateDTO.getEffectiveStartTime())
                .ge(FormulaAdjustment::getExpiryTime, updateDTO.getExpiryTime());
        Integer count = formulaAdjustmentMapper.selectCount(wrapper);

        FormulaAdjustment convert = DtoMapper.convert(updateDTO, FormulaAdjustment.class);
        convert.setUpdaterId(userId);
        convert.setUpdateTime(new Date());
        convert.setApprovalStatus(ApprovalStatus.NOT_STARTED.getName());
        if (count == 0) {
            formulaAdjustmentMapper.updateById(convert);
        } else {
            FailureCode addFailedDataExists = FailureCode.UPDATE_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailedDataExists.getCode(), addFailedDataExists.getDescription("当前中心+工艺参数下生效的时间范围"));
        }

        //修改审批步骤
        ProcessApprovalDTO approvalDTO = new ProcessApprovalDTO();
        approvalDTO.setApprovalNo(updateDTO.getApprovalNo());
        approvalDTO.setProcessBusiness(MqExchangelEnum.FORMULA_ADJUSTMENT.getType());
        approvalDTO.setUserAddDTOList(updateDTO.getUserDTOList());
        processApprovalService.updateProcessApproval(approvalDTO);

        //直接发起
        if (updateDTO.getIsType()) {
            initiateApproval(updateDTO.getApprovalNo());
        }
        return OperationResult.UPDATE_SUCCESS_MSG.getDescription("审批单号=" + updateDTO.getApprovalNo());

    }

    /**
     * 删除配方调整
     *
     * @param idList
     * @return
     */
    public String deleteFormulaAdjustment(List<Integer> idList) {
        log.info("删除配方调整，传入参数：{}", JSONObject.toJSONString(idList));
        Integer userId = userAuditorAware.getUserId();
        log.info("删除配方调整，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FormulaAdjustment::getId, idList)
                .eq(FormulaAdjustment::getApprovalStatus, ApprovalStatus.IN_REVIEW.getName())
                .eq(FormulaAdjustment::getDeleted, 0);
        List<FormulaAdjustment> adjustmentList = formulaAdjustmentMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(adjustmentList)) {
            //正在进行中的单号
            List<String> approvalNoList = adjustmentList.stream().map(FormulaAdjustment::getApprovalNo).collect(Collectors.toList());

            FailureCode deleteFailed = FailureCode.DELETE_FAILED_MSG;
            throw new BaseKnownException(deleteFailed.getCode(), deleteFailed.getDescription("审批单号=" + String.join(", ", approvalNoList) + "正在审批中，请先关闭流程"));
        }
        formulaAdjustmentMapper.deleteBatchIds(idList);
        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 发起审批
     *
     * @param approvalNo 审批单号
     * @return
     */
    public String initiateApproval(String approvalNo) {
        log.info("配方调整-发起审批传入审批单号:{}", approvalNo);
        Integer userId = userAuditorAware.getUserId();
        log.info("配方调整-获取到当前登录用户，用户id：{}", userId);
        String userName = userAuditorAware.getUserNameById(userId);
        log.info("配方调整-获取到当前登录用户名称，用户名称：{}", userName);

        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FormulaAdjustment::getDeleted, 0)
                .eq(FormulaAdjustment::getApprovalNo, approvalNo);
        FormulaAdjustment formulaAdjustment = formulaAdjustmentMapper.selectOne(wrapper);
        if (null == formulaAdjustment || formulaAdjustment.getDeleted()) {
            FailureCode otherFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(otherFailed.getCode(), otherFailed.getDescription("发起审批", "审批单号=" + approvalNo + "不存在"));
        }

        //不是未发起状态
        if (!formulaAdjustment.getApprovalStatus().equals(ApprovalStatus.NOT_STARTED.getName())) {
            FailureCode otherFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(otherFailed.getCode(), otherFailed.getDescription("发起审批", "当前流程不是未发起状态"));
        }

        //判断当前任务是否是本人创建，是本人创建则可以发起，不是则不能发起
        if (!formulaAdjustment.getCreatorId().equals(userId)) {
            FailureCode otherFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(otherFailed.getCode(), otherFailed.getDescription("发起审批", "不允许发起非本人创建的任务"));
        }

        formulaAdjustment.setApprovalStatus(ApprovalStatus.IN_REVIEW.getName());
        formulaAdjustment.setSubmitter(userName);
        formulaAdjustment.setSubmitterId(userId);
        formulaAdjustment.setSubmitTime(new Date());
        formulaAdjustment.setUpdaterId(userId);
        formulaAdjustment.setUpdateTime(new Date());

        //找到下一步的审批人|审批角色
        ProcessApprovalVO processApprovalVO = processApprovalService.currentApprover(approvalNo);
        if (processApprovalVO.getApproverType()) {
            //角色审批
            formulaAdjustment.setApproverId(processApprovalVO.getApproverRoleId());
            formulaAdjustment.setApproverName(processApprovalVO.getApproverRoleName());
        } else {
            //人员审批
            formulaAdjustment.setApproverId(processApprovalVO.getApproverId());
            formulaAdjustment.setApproverName(processApprovalVO.getApproverName());
        }
        formulaAdjustment.setApproverType(processApprovalVO.getApproverType());
        formulaAdjustmentMapper.updateById(formulaAdjustment);

        //调用审批接口
        ProcessApprovalExecuteDTO executeDTO = new ProcessApprovalExecuteDTO();
        executeDTO.setApprovalNo(approvalNo);
        executeDTO.setApprovalResult(ApprovalStatus.APPROVED.getName());
        executeDTO.setComments("流程初始化");
        processApprovalService.approveExecute(executeDTO);
        return OperationResult.OTHER_SUCCESS.getDescription("发起审批");
    }

    /**
     * 关闭审批
     *
     * @param id 配方调整主键id
     * @return
     */
    public String closeApproval(Integer id) {
        log.info("配方调整-关闭审批传入id:{}", id);
        Integer userId = userAuditorAware.getUserId();
        log.info("配方调整-关闭审批,获取到当前登录用户，用户id：{}", userId);

        FormulaAdjustment formulaAdjustment = formulaAdjustmentMapper.selectById(id);
        if (null == formulaAdjustment || formulaAdjustment.getDeleted()) {
            FailureCode otherFailed = FailureCode.OTHER_FAILED_NOT_FOUND;
            throw new BaseKnownException(otherFailed.getCode(), otherFailed.getDescription("关闭审批", id));
        }

        //不是未发起或审批中状态
        boolean flag = !formulaAdjustment.getApprovalStatus().equals(ApprovalStatus.NOT_STARTED.getName()) && !formulaAdjustment.getApprovalStatus().equals(ApprovalStatus.IN_REVIEW.getName());
        if (flag) {
            FailureCode otherFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(otherFailed.getCode(), otherFailed.getDescription("关闭审批", "当前流程不是未发起或审批中状态"));
        }

        formulaAdjustment.setApprovalStatus(ApprovalStatus.CLOSED.getName());
        formulaAdjustment.setUpdaterId(userId);
        formulaAdjustment.setUpdateTime(new Date());
        formulaAdjustmentMapper.updateById(formulaAdjustment);

        //关闭审批
        processApprovalService.closeApprove(formulaAdjustment.getApprovalNo());

        return OperationResult.OTHER_SUCCESS.getDescription("关闭审批");
    }

    /**
     * 查询登录人相关的审批列表
     *
     * @param userQueryDTO
     * @return
     */
    public Page<FormulaAdjustmentVO> findUserList(FormulaAdjustmentUserQueryDTO userQueryDTO) {
        Integer userId = userAuditorAware.getUserId();

        //获取当前登录人的角色
        List<RoleDTO> roleDTOList = userClient.getUserRole(userId).getData();
        //获取角色id
        List<Integer> roleListId = roleDTOList.stream().map(RoleDTO::getId).distinct().collect(Collectors.toList());

        Page<FormulaAdjustmentVO> page = new Page<>(userQueryDTO.getPage(), userQueryDTO.getPageSize());

        List<FormulaAdjustmentVO> voList = new ArrayList<>();
        //完成
        if (userQueryDTO.getStates()) {
            //查询登录人相关已完成的审批列表
            List<FormulaAdjustment> adjustmentList = formulaAdjustmentMapper.selectPageUserCompleteList(page, userId, roleListId, userQueryDTO);
            voList = DtoMapper.convertList(adjustmentList, FormulaAdjustmentVO.class);
        } else {
            //查询登录人相关未完成的审批列表
            List<FormulaAdjustment> adjustmentList = formulaAdjustmentMapper.selectPageUserInCompleteList(page, userId, roleListId, userQueryDTO);
            voList = DtoMapper.convertList(adjustmentList, FormulaAdjustmentVO.class);
        }
        page.setRecords(voList);
        return page;
    }

    /**
     * 查询生效配方调整-最新通过的
     *
     * @param queryDTO
     * @return
     */
    public FormulaAdjustmentVO findEffectiveList(FormulaAdjustmentEffectiveDTO queryDTO) {
        LambdaQueryWrapper<FormulaAdjustment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FormulaAdjustment::getDeleted, 0)
                .eq(FormulaAdjustment::getWorkshop, queryDTO.getWorkshop())
                .eq(FormulaAdjustment::getApprovalStatus, ApprovalStatus.APPROVED.getName())
                .eq(FormulaAdjustment::getCenter, queryDTO.getCenter())
                .eq(FormulaAdjustment::getRouteCode, queryDTO.getRouteCode())
                .eq(FormulaAdjustment::getRouteName, queryDTO.getRouteName())
                .eq(FormulaAdjustment::getOperationCode, queryDTO.getOperationCode())
                .eq(FormulaAdjustment::getParamCode, queryDTO.getParamCode())
                .le(FormulaAdjustment::getEffectiveStartTime, queryDTO.getTakeEffectTime())
                .ge(FormulaAdjustment::getExpiryTime, queryDTO.getTakeEffectTime())
                .orderByDesc(FormulaAdjustment::getSubmitTime)
                .last("limit 1");
        FormulaAdjustment formulaAdjustment = formulaAdjustmentMapper.selectOne(wrapper);

        return DtoMapper.convert(formulaAdjustment, FormulaAdjustmentVO.class);
    }

    /**
     * 批量发起审批
     *
     * @param approvalNoList
     * @return
     */
    public String initiateApprovalList(List<String> approvalNoList) {
        log.info("配方调整-批量发起审批传入审批单号:{}", approvalNoList);
        Integer userId = userAuditorAware.getUserId();
        log.info("配方调整-批量发起审批-获取到当前登录用户，用户id：{}", userId);

        approvalNoList.forEach(this::initiateApproval);

        return OperationResult.OTHER_SUCCESS.getDescription("发起审批");
    }
}
