package com.hvisions.technology.dto.base.processparameter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ProcessOperationParameterAddDTO
 * @description: 工艺参数列表新增DTO
 * @date 2025/6/10 16:56
 */
@ApiModel(value = "工艺参数列表新增DTO")
@Data
public class ProcessOperationParameterAddDTO {
    @ApiModelProperty(value="工艺操作主键id",required = true)
    @NotNull(message = "工艺操作主键id不能为空")
    private Integer processOperationId;

    @ApiModelProperty(value="参数编码",required = true)
    @NotBlank(message = "参数编码不能为空")
    private String paramCode;

    @ApiModelProperty(value="参数名称",required = true)
    @NotBlank(message = "参数名称不能为空")
    private String paramName;

    @ApiModelProperty(value="采集方式",required = true)
    @NotBlank(message = "采集方式不能为空")
    private String collectionMethod;

    @ApiModelProperty(value="用途")
    private String purpose;

    @ApiModelProperty(value="单位",required = true)
    @NotBlank(message = "单位不能为空")
    private String unit;

    @ApiModelProperty(value="参数类型")
    private String paramType;

    @ApiModelProperty(value="是否运行超差",required = true)
    @NotBlank(message = "是否运行超差不能为空")
    private String isOutOfTolerance;

    @ApiModelProperty(value="数据类型")
    private String dataType;
}
