package com.hvisions.technology.dto.appr.approvaltype;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ApprovalTypeAddDTO
 * @description: 审批类型新增DTO
 * @date 2025/6/12 18:07
 */
@ApiModel(value = "审批类型新增DTO")
@Data
public class ApprovalTypeAddDTO {
    @ApiModelProperty(value="分类编码",required = true)
    @NotEmpty(message = "分类编码不能为空")
    private String typeCode;

    @ApiModelProperty(value="分类名称",required = true)
    @NotEmpty(message = "分类名称不能为空")
    private String typeName;

    @ApiModelProperty(value="分类",required = true)
    @NotEmpty(message = "分类不能为空")
    private String className;

    @ApiModelProperty(value="说明")
    private String remarks;

    @ApiModelProperty(value="处置措施")
    private String handlingMeasures;
}
