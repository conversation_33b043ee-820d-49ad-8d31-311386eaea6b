package com.hvisions.technology.dto.appr.approvaltype;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ApprovalTypeUpdateDTO
 * @description: 审批类型修改DTO
 * @date 2025/6/12 18:07
 */
@ApiModel(value = "审批类型修改DTO")
@Data
public class ApprovalTypeUpdateDTO {
    @ApiModelProperty(value="审批类型主键id",required = true)
    @NotNull(message = "id不能为空")
    private Integer id;

    @ApiModelProperty(value="分类编码",required = true)
    @NotEmpty(message = "分类编码不能为空")
    private String typeCode;

    @ApiModelProperty(value="分类名称",required = true)
    @NotEmpty(message = "分类名称不能为空")
    private String typeName;

    @ApiModelProperty(value="分类",required = true)
    @NotEmpty(message = "分类不能为空")
    private String className;

    @ApiModelProperty(value="说明")
    private String remarks;

    @ApiModelProperty(value="处置措施")
    private String handlingMeasures;
}
