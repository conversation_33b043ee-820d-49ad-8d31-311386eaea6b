package com.hvisions.auth.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ResultType</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/5/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum ResultType implements IKeyValueObject {

    /**
     * 返回的数据类型
     */
    XML(1, "xml"),
    JSON(2, "json");


    private Integer code;
    private String name;

    ResultType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override

    public String getName() {
        return this.name;
    }
}
