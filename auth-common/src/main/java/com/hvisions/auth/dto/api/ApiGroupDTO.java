package com.hvisions.auth.dto.api;

import com.hvisions.auth.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>Title: ApiGroup</p>
 * <p>Description: Api分组对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/6/15</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiGroupDTO extends SysBaseDTO {
    public ApiGroupDTO() {
        description = "";
    }

    /**
     * 编码，唯一
     */
    @ApiModelProperty(value = "Api分组编码", required = true, notes = "不能修改")
    @NotBlank(message = "Api分组编码不能为空")
    private String code;
    /**
     * 分组描述
     */
    @ApiModelProperty(value = "描述")
    @NotNull(message = "描述不能为空")
    private String description;
    /**
     * 父级分组
     */
    @ApiModelProperty(value = "父级分组id", required = true)
    @NotNull(message = "父级分组不能为空")
    private Integer parentGroupId;
}









