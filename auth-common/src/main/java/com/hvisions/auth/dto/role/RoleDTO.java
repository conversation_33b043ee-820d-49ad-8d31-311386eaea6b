package com.hvisions.auth.dto.role;

import com.hvisions.auth.dto.module.ModuleDTO;
import com.hvisions.common.interfaces.IObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: RoleDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleDTO extends RoleBaseDTO implements IObjectType {

    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id")
    private Integer siteId = 1;
    /**
     * 是否可用
     */
    @ApiModelProperty(value = "角色是否可用")
    private Boolean enableMark = true;

    /**
     * 用户是否拥有角色
     */
    @ApiModelProperty(value = "用户是否拥有角色权限")
    private Boolean userPrivilege = true;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortCode = 1;
    /**
     * 权限信息
     */
    @ApiModelProperty(value = "权限信息")
    private List<ModuleDTO> sysModuleDTOS = new ArrayList<>();

    /**
     * 首页模块信息
     */
    @ApiModelProperty(value = "首页模块信息")
    private ModuleDTO sysModuleDTO;

    /**
     * 首页模块信息
     */
    @ApiModelProperty(value = "客户端首页模块信息")
    private ModuleDTO sysClientModuleDTO;

}
