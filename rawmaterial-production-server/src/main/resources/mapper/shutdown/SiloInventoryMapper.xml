<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.shutdown.SiloInventoryMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.shutdown.TMpdSiloInventory">
        <id column="id" property="id"/>
        <result column="silo_code" property="siloCode"/>
        <result column="silo_name" property="siloName"/>
        <result column="silo_type" property="siloType"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="stock_quantity" property="stockQuantity"/>
        <result column="unit" property="unit"/>
        <result column="data_update_time" property="dataUpdateTime"/>
        <result column="data_source" property="dataSource"/>
        <result column="workshop_code" property="workshopCode"/>
        <result column="workshop_name" property="workshopName"/>
        <result column="center_code" property="centerCode"/>
        <result column="center_name" property="centerName"/>
        <result column="enabled" property="enabled"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, silo_code, silo_name, silo_type, material_code, material_name,
        stock_quantity, unit, data_update_time, data_source, workshop_code, workshop_name,
        center_code, center_name, enabled, sort_order, remark,
        create_time, update_time, creator_id, updater_id, site_num, deleted
    </sql>

    <!-- 根据物料类型查询筒仓库存 -->
    <select id="selectByMaterialType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND material_name = #{materialType}
        AND enabled = 1
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 根据筒仓编码查询库存 -->
    <select id="selectBySiloCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND silo_code = #{siloCode}
        LIMIT 1
    </select>

    <!-- 根据筒仓类型查询库存列表 -->
    <select id="selectBySiloType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND silo_type = #{siloType}
        AND enabled = 1
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 根据车间编码查询筒仓库存 -->
    <select id="selectByWorkshopCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND workshop_code = #{workshopCode}
        AND enabled = 1
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 根据物料类型和筒仓类型查询库存 -->
    <select id="selectByMaterialAndSiloType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND material_name = #{materialType}
        AND silo_type = #{siloType}
        AND enabled = 1
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 根据物料类型和车间编码查询库存 -->
    <select id="selectByMaterialTypeAndWorkshop" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND material_name = #{materialType}
        AND workshop_code = #{workshopCode}
        AND enabled = 1
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 查询高粱物料的筒仓库存（按层级排序） -->
    <select id="selectSorghumSiloInventoryByHierarchy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND material_name = '高粱'
        AND enabled = 1
        ORDER BY 
            CASE silo_type
                WHEN '中心碎料斗' THEN 1
                WHEN '中心碎料仓' THEN 2
                WHEN '中心缓存仓' THEN 3
                WHEN '后处理暂存仓' THEN 4
                WHEN '前处理存储仓' THEN 5
                ELSE 99
            END,
            sort_order ASC, silo_code ASC
    </select>

    <!-- 查询稻壳物料的筒仓库存（按层级排序） -->
    <select id="selectRiceHuskSiloInventoryByHierarchy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND material_name = '稻壳'
        AND enabled = 1
        ORDER BY 
            CASE silo_type
                WHEN '中心缓存仓' THEN 1
                WHEN '熟稻壳缓存仓' THEN 2
                WHEN '后处理暂存仓' THEN 3
                WHEN '前处理存储仓' THEN 4
                ELSE 99
            END,
            sort_order ASC, silo_code ASC
    </select>

    <!-- 根据筒仓编码列表查询库存 -->
    <select id="selectBySiloCodes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND silo_code IN
        <foreach collection="siloCodes" item="siloCode" open="(" separator="," close=")">
            #{siloCode}
        </foreach>
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 查询最新更新时间的库存记录 -->
    <select id="selectLatestBySiloCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND silo_code = #{siloCode}
        ORDER BY data_update_time DESC, update_time DESC
        LIMIT 1
    </select>

    <!-- 批量更新库存量 -->
    <update id="batchUpdateStockQuantity">
        <foreach collection="inventoryList" item="item" separator=";">
            UPDATE t_mpd_silo_inventory
            SET stock_quantity = #{item.stockQuantity},
                data_update_time = #{item.dataUpdateTime},
                update_time = NOW()
            WHERE silo_code = #{item.siloCode}
            AND deleted = 0
        </foreach>
    </update>

    <!-- 根据数据来源查询库存 -->
    <select id="selectByDataSource" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND data_source = #{dataSource}
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 查询指定时间范围内更新的库存记录 -->
    <select id="selectByUpdateTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        <if test="startTime != null">
            AND data_update_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND data_update_time &lt;= #{endTime}
        </if>
        ORDER BY data_update_time DESC, sort_order ASC
    </select>

    <!-- 根据启用状态查询库存 -->
    <select id="selectByEnabled" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND enabled = #{enabled}
        ORDER BY sort_order ASC, silo_code ASC
    </select>

    <!-- 查询所有启用的筒仓库存（按排序序号排序） -->
    <select id="selectAllEnabledOrderBySortOrder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_inventory
        WHERE deleted = 0
        AND enabled = 1
        ORDER BY sort_order ASC, silo_code ASC
    </select>

</mapper>