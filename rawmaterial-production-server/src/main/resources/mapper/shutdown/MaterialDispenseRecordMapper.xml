<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.shutdown.MaterialDispenseRecordMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.shutdown.TMpdMaterialDispenseRecord">
        <id column="id" property="id"/>
        <result column="dispense_no" property="dispenseNo"/>
        <result column="workshop_code" property="workshopCode"/>
        <result column="workshop_name" property="workshopName"/>
        <result column="center_code" property="centerCode"/>
        <result column="center_name" property="centerName"/>
        <result column="silo_code" property="siloCode"/>
        <result column="silo_name" property="siloName"/>
        <result column="silo_type" property="siloType"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="material_type" property="materialType"/>
        <result column="dispense_quantity" property="dispenseQuantity"/>
        <result column="unit" property="unit"/>
        <result column="dispense_time" property="dispenseTime"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_id" property="operatorId"/>
        <result column="dispense_status" property="dispenseStatus"/>
        <result column="business_type" property="businessType"/>
        <result column="related_doc_no" property="relatedDocNo"/>
        <result column="data_source" property="dataSource"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, dispense_no, workshop_code, workshop_name, center_code, center_name,
        silo_code, silo_name, silo_type, material_code, material_name, material_type,
        dispense_quantity, unit, dispense_time, operator_name, operator_id,
        dispense_status, business_type, related_doc_no, data_source, remark,
        create_time, update_time, creator_id, updater_id, site_num, deleted
    </sql>

    <!-- 根据发放单号查询记录 -->
    <select id="selectByDispenseNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND dispense_no = #{dispenseNo}
        LIMIT 1
    </select>

    <!-- 根据车间编码查询发放记录 -->
    <select id="selectByWorkshopCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND workshop_code = #{workshopCode}
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据筒仓编码查询发放记录 -->
    <select id="selectBySiloCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND silo_code = #{siloCode}
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据物料类型查询发放记录 -->
    <select id="selectByMaterialType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND material_type = #{materialType}
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据发放时间范围查询记录 -->
    <select id="selectByDispenseTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据车间和时间范围查询发放记录 -->
    <select id="selectByWorkshopAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND workshop_code = #{workshopCode}
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据筒仓和时间范围查询发放记录 -->
    <select id="selectBySiloAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND silo_code = #{siloCode}
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
        ORDER BY dispense_time DESC
    </select>

    <!-- 统计车间在指定时间范围内的发放量 -->
    <select id="sumDispenseQuantityByWorkshopAndTime" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(dispense_quantity), 0)
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND workshop_code = #{workshopCode}
        AND material_type = #{materialType}
        AND dispense_status = 2
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 统计筒仓在指定时间范围内的发放量 -->
    <select id="sumDispenseQuantityBySiloAndTime" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(dispense_quantity), 0)
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND silo_code = #{siloCode}
        AND material_type = #{materialType}
        AND dispense_status = 2
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 根据物料类型和时间范围统计发放量 -->
    <select id="sumDispenseQuantityByMaterialAndTime" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(dispense_quantity), 0)
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND material_type = #{materialType}
        AND dispense_status = 2
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询高粱物料的中心碎料斗发放记录 -->
    <select id="selectSorghumCenterCrushedDispenseRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND workshop_code = #{workshopCode}
        AND material_type = '高粱'
        AND silo_type = '中心碎料斗'
        AND dispense_status = 2
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
        ORDER BY dispense_time DESC
    </select>

    <!-- 查询稻壳物料的中心缓存仓发放记录 -->
    <select id="selectRiceHuskCenterCacheDispenseRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND workshop_code = #{workshopCode}
        AND material_type = '稻壳'
        AND silo_type = '中心缓存仓'
        AND dispense_status = 2
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据业务类型查询发放记录 -->
    <select id="selectByBusinessType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND business_type = #{businessType}
        ORDER BY dispense_time DESC
    </select>

    <!-- 根据发放状态查询记录 -->
    <select id="selectByDispenseStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND dispense_status = #{dispenseStatus}
        ORDER BY dispense_time DESC
    </select>

    <!-- 批量插入发放记录 -->
    <insert id="batchInsert">
        INSERT INTO t_mpd_material_dispense_record (
            dispense_no, workshop_code, workshop_name, center_code, center_name,
            silo_code, silo_name, silo_type, material_code, material_name, material_type,
            dispense_quantity, unit, dispense_time, operator_name, operator_id,
            dispense_status, business_type, related_doc_no, data_source, remark,
            create_time, creator_id, site_num, deleted
        ) VALUES
        <foreach collection="recordList" item="item" separator=",">
            (
                #{item.dispenseNo}, #{item.workshopCode}, #{item.workshopName},
                #{item.centerCode}, #{item.centerName}, #{item.siloCode}, #{item.siloName},
                #{item.siloType}, #{item.materialCode}, #{item.materialName}, #{item.materialType},
                #{item.dispenseQuantity}, #{item.unit}, #{item.dispenseTime},
                #{item.operatorName}, #{item.operatorId}, #{item.dispenseStatus},
                #{item.businessType}, #{item.relatedDocNo}, #{item.dataSource}, #{item.remark},
                NOW(), #{item.creatorId}, #{item.siteNum}, 0
            )
        </foreach>
    </insert>

    <!-- 根据关联单据号查询发放记录 -->
    <select id="selectByRelatedDocNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND related_doc_no = #{relatedDocNo}
        ORDER BY dispense_time DESC
    </select>

    <!-- 查询指定筒仓类型的发放记录汇总 -->
    <select id="sumDispenseQuantityBySiloTypeAndTime" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(dispense_quantity), 0)
        FROM t_mpd_material_dispense_record
        WHERE deleted = 0
        AND silo_type = #{siloType}
        AND material_type = #{materialType}
        AND dispense_status = 2
        <if test="startTime != null">
            AND dispense_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dispense_time &lt;= #{endTime}
        </if>
    </select>

</mapper>