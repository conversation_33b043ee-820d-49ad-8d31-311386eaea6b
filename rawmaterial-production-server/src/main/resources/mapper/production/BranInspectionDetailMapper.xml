<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.BranInspectionDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.TMpdBranInspectionDetail">
        <id column="id" property="id" />
        <result column="creator_id" property="creatorId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
        <result column="deleted" property="deleted" />
        <result column="site_num" property="siteNum" />
        <result column="inspection_id" property="inspectionId" />
        <result column="whether_run" property="whetherRun" />
        <result column="detail_result" property="detailResult" />
        <result column="abnormal_description" property="abnormalDescription" />
        <result column="improvement_measures" property="improvementMeasures" />
        <result column="machine" property="machine" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, creator_id, create_time, update_time, updater_id, deleted, site_num, inspection_id, whether_run, detail_result, abnormal_description, improvement_measures, machine
    </sql>

</mapper>
