<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.HuskInspectionItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.TMpdHuskInspectionItem">
        <id column="id" property="id" />
        <result column="creator_id" property="creatorId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="updater_id" property="updaterId" />
        <result column="deleted" property="deleted" />
        <result column="site_num" property="siteNum" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="standard" property="standard" />
        <result column="sort" property="sort" />
        <result column="effect_state" property="effectState" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, creator_id, create_time, update_time, updater_id, deleted, site_num, code, name, standard, sort, effect_state, remark
    </sql>

</mapper>
