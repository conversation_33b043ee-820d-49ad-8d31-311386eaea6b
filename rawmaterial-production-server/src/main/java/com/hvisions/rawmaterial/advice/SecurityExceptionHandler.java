package com.hvisions.rawmaterial.advice;

import com.hvisions.rawmaterial.security.exception.SecurityException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全异常处理器
 * 统一处理权限验证失败等安全相关异常
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RestControllerAdvice
public class SecurityExceptionHandler {
    
    /**
     * 处理安全异常
     */
    @ExceptionHandler(SecurityException.class)
    public ResponseEntity<Map<String, Object>> handleSecurityException(
            SecurityException e, HttpServletRequest request) {
        
        log.warn("安全异常：{}，请求URL：{}，用户：{}", 
            e.getMessage(), request.getRequestURI(), request.getHeader("User-Id"));
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", e.getErrorCode());
        response.put("message", e.getMessage());
        response.put("timestamp", System.currentTimeMillis());
        response.put("path", request.getRequestURI());
        
        // 根据错误类型返回不同的HTTP状态码
        HttpStatus status;
        switch (e.getErrorCode()) {
            case "AUTHENTICATION_REQUIRED":
                status = HttpStatus.UNAUTHORIZED;
                break;
            case "PERMISSION_DENIED":
            case "ROLE_DENIED":
                status = HttpStatus.FORBIDDEN;
                break;
            default:
                status = HttpStatus.BAD_REQUEST;
                break;
        }
        
        return ResponseEntity.status(status).body(response);
    }
    
    /**
     * 处理一般的访问拒绝异常
     */
    @ExceptionHandler(org.springframework.security.access.AccessDeniedException.class)
    public ResponseEntity<Map<String, Object>> handleAccessDeniedException(
            org.springframework.security.access.AccessDeniedException e, HttpServletRequest request) {
        
        log.warn("访问拒绝异常：{}，请求URL：{}，用户：{}", 
            e.getMessage(), request.getRequestURI(), request.getHeader("User-Id"));
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "ACCESS_DENIED");
        response.put("message", "访问被拒绝：" + e.getMessage());
        response.put("timestamp", System.currentTimeMillis());
        response.put("path", request.getRequestURI());
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
}