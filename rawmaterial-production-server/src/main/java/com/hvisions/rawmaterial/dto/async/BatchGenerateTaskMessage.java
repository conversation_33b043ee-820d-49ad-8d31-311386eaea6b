package com.hvisions.rawmaterial.dto.async;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 批量生成差异记录任务消息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatchGenerateTaskMessage extends AsyncTaskMessage {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 统计开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statisticsStartDate;
    
    /**
     * 统计结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statisticsEndDate;
    
    /**
     * 物料编码列表（为空则处理所有物料）
     */
    private List<String> materialCodes;
    
    /**
     * 仓库编码列表（为空则处理所有仓库）
     */
    private List<String> warehouseCodes;
    
    /**
     * 部门列表（为空则处理所有部门）
     */
    private List<String> departments;
    
    /**
     * 是否覆盖已存在的记录
     */
    private Boolean overrideExisting = false;
    
    /**
     * 批次大小
     */
    private Integer batchSize = 100;
    
    /**
     * 预计处理数量
     */
    private Integer estimatedCount;
    
    public BatchGenerateTaskMessage() {
        super("BATCH_GENERATE", "批量生成差异记录任务");
    }
    
    public BatchGenerateTaskMessage(Date statisticsStartDate, Date statisticsEndDate) {
        this();
        this.statisticsStartDate = statisticsStartDate;
        this.statisticsEndDate = statisticsEndDate;
    }
    
    /**
     * 获取处理范围描述
     */
    public String getProcessScope() {
        StringBuilder scope = new StringBuilder();
        
        if (materialCodes != null && !materialCodes.isEmpty()) {
            scope.append(String.format("物料:%d个 ", materialCodes.size()));
        } else {
            scope.append("物料:全部 ");
        }
        
        if (warehouseCodes != null && !warehouseCodes.isEmpty()) {
            scope.append(String.format("仓库:%d个 ", warehouseCodes.size()));
        } else {
            scope.append("仓库:全部 ");
        }
        
        if (departments != null && !departments.isEmpty()) {
            scope.append(String.format("部门:%d个", departments.size()));
        } else {
            scope.append("部门:全部");
        }
        
        return scope.toString();
    }
}