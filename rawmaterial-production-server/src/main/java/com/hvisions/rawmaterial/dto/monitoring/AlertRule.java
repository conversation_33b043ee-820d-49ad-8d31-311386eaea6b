package com.hvisions.rawmaterial.dto.monitoring;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警规则DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class AlertRule {
    
    /**
     * 规则ID
     */
    private Integer id;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则类型: SAP_INTERFACE, PERFORMANCE, BUSINESS
     */
    private String ruleType;
    
    /**
     * 指标名称
     */
    private String metricName;
    
    /**
     * 阈值
     */
    private Double thresholdValue;
    
    /**
     * 比较操作符: GT(大于), LT(小于), EQ(等于), GTE(大于等于), LTE(小于等于)
     */
    private String operator;
    
    /**
     * 告警级别: INFO, WARNING, ERROR, CRITICAL
     */
    private String alertLevel;
    
    /**
     * 检查间隔(分钟)
     */
    private Integer checkInterval;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}