package com.hvisions.rawmaterial.dto.async;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 异步任务消息基类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class AsyncTaskMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 创建人ID
     */
    private Integer creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 0;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 任务描述
     */
    private String description;
    
    public AsyncTaskMessage() {
        this.createTime = new Date();
        this.taskId = generateTaskId();
    }
    
    public AsyncTaskMessage(String taskType, String description) {
        this();
        this.taskType = taskType;
        this.description = description;
    }
    
    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "TASK_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }
    
    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }
}