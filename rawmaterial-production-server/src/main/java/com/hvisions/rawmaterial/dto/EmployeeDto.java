package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @Description: TODO
 * @Date: 2025/6/18 09:38
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class EmployeeDto {


    /**
     * 班次：1-一班，2-二班，3-三班
     */
    @ApiModelProperty(value = "班次：1-一班，2-二班，3-三班")
    private Integer shiftType;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Integer employeeId;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号")
    private String employeeCode;
}
