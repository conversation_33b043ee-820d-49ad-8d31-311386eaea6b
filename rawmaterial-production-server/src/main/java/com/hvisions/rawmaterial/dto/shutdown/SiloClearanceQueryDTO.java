package com.hvisions.rawmaterial.dto.shutdown;

import com.hvisions.rawmaterial.dto.validation.DateRange;
import com.hvisions.rawmaterial.dto.validation.MaterialType;
import com.hvisions.rawmaterial.dto.validation.ShutdownTimeRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 筒仓清仓查询条件DTO
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(description = "筒仓清仓查询条件")
@DateRange(startField = "shutdownStartTime", endField = "statisticsTime", message = "停产开始时间不能晚于统计时间")
@ShutdownTimeRange(shutdownStartField = "shutdownStartTime", statisticsTimeField = "statisticsTime", maxDays = 365)
public class SiloClearanceQueryDTO {

    @ApiModelProperty(value = "物料类型", example = "高粱", notes = "物料类型：高粱、稻壳", required = true)
    @NotBlank(message = "物料类型不能为空")
    @MaterialType(allowedTypes = {"高粱", "稻壳"})
    private String materialType;

    @ApiModelProperty(value = "统计时间", example = "2024-01-15 10:00:00", notes = "清仓统计的截止时间", required = true)
    @NotNull(message = "统计时间不能为空")
    private Date statisticsTime;

    @ApiModelProperty(value = "停产统计开始时间", example = "2024-01-01 00:00:00", notes = "停产统计的开始时间")
    private Date shutdownStartTime;

    @ApiModelProperty(value = "车间名称列表", example = "[\"一车间\", \"二车间\"]", notes = "要查询的车间名称列表，为空则查询全部")
    private List<@Size(max = 50, message = "车间名称长度不能超过50个字符") String> workshopNames;

    @ApiModelProperty(value = "筒仓类型列表", example = "[\"中心碎料斗\", \"中心碎料仓\"]", notes = "要查询的筒仓类型列表，为空则查询全部")
    private List<@Size(max = 50, message = "筒仓类型长度不能超过50个字符") String> siloTypes;

    @ApiModelProperty(value = "筒仓编码列表", example = "[\"SILO001\", \"SILO002\"]", notes = "要查询的筒仓编码列表，为空则查询全部")
    private List<@Size(max = 50, message = "筒仓编码长度不能超过50个字符") String> siloCodes;

    @ApiModelProperty(value = "是否包含库存为零的筒仓", example = "false", notes = "是否在结果中包含库存为零的筒仓")
    private Boolean includeZeroStock = false;

    @ApiModelProperty(value = "是否包含已完成发放的车间", example = "false", notes = "是否在结果中包含已完成发放的车间")
    private Boolean includeCompletedWorkshops = false;

    @ApiModelProperty(value = "是否实时刷新数据", example = "true", notes = "是否从中控系统实时获取最新数据")
    private Boolean realTimeRefresh = true;
}