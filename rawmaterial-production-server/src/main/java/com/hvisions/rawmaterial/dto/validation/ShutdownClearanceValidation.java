package com.hvisions.rawmaterial.dto.validation;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 停产物料清仓验证工具类
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class ShutdownClearanceValidation {

    /**
     * 支持的物料类型
     */
    public static final Set<String> SUPPORTED_MATERIAL_TYPES = new HashSet<>(Arrays.asList("高粱", "稻壳"));

    /**
     * 高粱物料支持的筒仓类型
     */
    public static final Set<String> SORGHUM_SILO_TYPES = new HashSet<>(Arrays.asList(
        "中心碎料斗", "中心碎料仓", "中心缓存仓", "后处理暂存仓", "前处理存储仓"
    ));

    /**
     * 稻壳物料支持的筒仓类型
     */
    public static final Set<String> RICE_HUSK_SILO_TYPES = new HashSet<>(Arrays.asList(
        "中心缓存仓", "熟稻壳缓存仓", "后处理暂存仓", "前处理存储仓"
    ));

    /**
     * 支持的导出类型
     */
    public static final Set<String> SUPPORTED_EXPORT_TYPES = new HashSet<>(Arrays.asList("1", "2", "3"));

    /**
     * 支持的导出格式
     */
    public static final Set<String> SUPPORTED_EXPORT_FORMATS = new HashSet<>(Arrays.asList("excel", "csv"));

    /**
     * 验证物料类型是否有效
     * 
     * @param materialType 物料类型
     * @return 是否有效
     */
    public static boolean isValidMaterialType(String materialType) {
        return StringUtils.hasText(materialType) && SUPPORTED_MATERIAL_TYPES.contains(materialType.trim());
    }

    /**
     * 验证筒仓类型是否对指定物料类型有效
     * 
     * @param siloType 筒仓类型
     * @param materialType 物料类型
     * @return 是否有效
     */
    public static boolean isValidSiloType(String siloType, String materialType) {
        if (!StringUtils.hasText(siloType) || !StringUtils.hasText(materialType)) {
            return false;
        }

        String trimmedSiloType = siloType.trim();
        String trimmedMaterialType = materialType.trim();

        switch (trimmedMaterialType) {
            case "高粱":
                return SORGHUM_SILO_TYPES.contains(trimmedSiloType);
            case "稻壳":
                return RICE_HUSK_SILO_TYPES.contains(trimmedSiloType);
            default:
                return false;
        }
    }

    /**
     * 验证筒仓类型列表是否对指定物料类型有效
     * 
     * @param siloTypes 筒仓类型列表
     * @param materialType 物料类型
     * @return 是否有效
     */
    public static boolean isValidSiloTypes(List<String> siloTypes, String materialType) {
        if (CollectionUtils.isEmpty(siloTypes)) {
            return true; // 空列表认为有效
        }

        return siloTypes.stream().allMatch(siloType -> isValidSiloType(siloType, materialType));
    }

    /**
     * 验证导出类型是否有效
     * 
     * @param exportType 导出类型
     * @return 是否有效
     */
    public static boolean isValidExportType(String exportType) {
        return StringUtils.hasText(exportType) && SUPPORTED_EXPORT_TYPES.contains(exportType.trim());
    }

    /**
     * 验证导出格式是否有效
     * 
     * @param exportFormat 导出格式
     * @return 是否有效
     */
    public static boolean isValidExportFormat(String exportFormat) {
        return StringUtils.hasText(exportFormat) && SUPPORTED_EXPORT_FORMATS.contains(exportFormat.trim());
    }

    /**
     * 验证时间范围是否有效
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxDays 最大天数
     * @return 是否有效
     */
    public static boolean isValidTimeRange(Date startTime, Date endTime, int maxDays) {
        if (startTime == null || endTime == null) {
            return true; // 空值由其他验证器处理
        }

        // 检查时间顺序
        if (startTime.after(endTime)) {
            return false;
        }

        // 检查时间跨度
        long diffInMillis = endTime.getTime() - startTime.getTime();
        long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
        
        if (diffInDays > maxDays) {
            return false;
        }

        // 检查结束时间不能是未来时间
        Date now = new Date();
        return !endTime.after(now);
    }

    /**
     * 获取指定物料类型支持的筒仓类型
     * 
     * @param materialType 物料类型
     * @return 支持的筒仓类型集合
     */
    public static Set<String> getSupportedSiloTypes(String materialType) {
        if (!StringUtils.hasText(materialType)) {
            return new HashSet<>();
        }

        switch (materialType.trim()) {
            case "高粱":
                return new HashSet<>(SORGHUM_SILO_TYPES);
            case "稻壳":
                return new HashSet<>(RICE_HUSK_SILO_TYPES);
            default:
                return new HashSet<>();
        }
    }

    /**
     * 验证车间名称是否有效
     * 
     * @param workshopName 车间名称
     * @return 是否有效
     */
    public static boolean isValidWorkshopName(String workshopName) {
        return StringUtils.hasText(workshopName) && workshopName.trim().length() <= 50;
    }

    /**
     * 验证筒仓编码是否有效
     * 
     * @param siloCode 筒仓编码
     * @return 是否有效
     */
    public static boolean isValidSiloCode(String siloCode) {
        return StringUtils.hasText(siloCode) && siloCode.trim().length() <= 50;
    }

    /**
     * 验证文件名是否有效
     * 
     * @param fileName 文件名
     * @return 是否有效
     */
    public static boolean isValidFileName(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return true; // 空文件名可以由系统生成默认名称
        }
        
        String trimmedFileName = fileName.trim();
        
        // 检查长度
        if (trimmedFileName.length() > 100) {
            return false;
        }
        
        // 检查是否包含非法字符
        String[] illegalChars = {"\\", "/", ":", "*", "?", "\"", "<", ">", "|"};
        for (String illegalChar : illegalChars) {
            if (trimmedFileName.contains(illegalChar)) {
                return false;
            }
        }
        
        return true;
    }
}