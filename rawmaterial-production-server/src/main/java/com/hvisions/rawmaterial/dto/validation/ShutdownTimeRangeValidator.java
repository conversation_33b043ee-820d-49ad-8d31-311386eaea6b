package com.hvisions.rawmaterial.dto.validation;

import org.springframework.util.ReflectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 停产时间范围验证器
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class ShutdownTimeRangeValidator implements ConstraintValidator<ShutdownTimeRange, Object> {

    private String shutdownStartField;
    private String statisticsTimeField;
    private int maxDays;

    @Override
    public void initialize(ShutdownTimeRange constraintAnnotation) {
        this.shutdownStartField = constraintAnnotation.shutdownStartField();
        this.statisticsTimeField = constraintAnnotation.statisticsTimeField();
        this.maxDays = constraintAnnotation.maxDays();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        try {
            Field shutdownStartFieldObj = ReflectionUtils.findField(value.getClass(), shutdownStartField);
            Field statisticsTimeFieldObj = ReflectionUtils.findField(value.getClass(), statisticsTimeField);

            if (shutdownStartFieldObj == null || statisticsTimeFieldObj == null) {
                return true;
            }

            ReflectionUtils.makeAccessible(shutdownStartFieldObj);
            ReflectionUtils.makeAccessible(statisticsTimeFieldObj);

            Date shutdownStartTime = (Date) ReflectionUtils.getField(shutdownStartFieldObj, value);
            Date statisticsTime = (Date) ReflectionUtils.getField(statisticsTimeFieldObj, value);

            // 如果任一时间为空，由其他验证器处理
            if (shutdownStartTime == null || statisticsTime == null) {
                return true;
            }

            // 检查时间顺序
            if (shutdownStartTime.after(statisticsTime)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("停产开始时间不能晚于统计时间")
                        .addConstraintViolation();
                return false;
            }

            // 检查时间跨度
            long diffInMillis = statisticsTime.getTime() - shutdownStartTime.getTime();
            long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);
            
            if (diffInDays > maxDays) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("停产统计时间跨度不能超过" + maxDays + "天")
                        .addConstraintViolation();
                return false;
            }

            // 检查统计时间不能是未来时间
            Date now = new Date();
            if (statisticsTime.after(now)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("统计时间不能是未来时间")
                        .addConstraintViolation();
                return false;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }
}