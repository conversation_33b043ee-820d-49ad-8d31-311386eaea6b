package com.hvisions.rawmaterial.dto.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 筒仓类型验证注解
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = SiloTypeValidator.class)
@Documented
public @interface SiloType {
    
    String message() default "筒仓类型不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 物料类型，用于确定允许的筒仓类型
     */
    String materialType() default "";
    
    /**
     * 高粱物料允许的筒仓类型
     */
    String[] sorghumSiloTypes() default {
        "中心碎料斗", "中心碎料仓", "中心缓存仓", "后处理暂存仓", "前处理存储仓"
    };
    
    /**
     * 稻壳物料允许的筒仓类型
     */
    String[] riceHuskSiloTypes() default {
        "中心缓存仓", "熟稻壳缓存仓", "后处理暂存仓", "前处理存储仓"
    };
}