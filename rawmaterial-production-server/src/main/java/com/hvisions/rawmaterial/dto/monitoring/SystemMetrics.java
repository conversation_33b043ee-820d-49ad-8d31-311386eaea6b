package com.hvisions.rawmaterial.dto.monitoring;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统指标DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class SystemMetrics {
    
    /**
     * 已使用内存(字节)
     */
    private Long usedMemory;
    
    /**
     * 最大内存(字节)
     */
    private Long maxMemory;
    
    /**
     * 内存使用率(%)
     */
    private Double memoryUsagePercent;
    
    /**
     * CPU使用率(%)
     */
    private Double cpuUsagePercent;
    
    /**
     * 可用处理器数量
     */
    private Integer availableProcessors;
    
    /**
     * 活跃线程数
     */
    private Integer activeThreadCount;
    
    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
}