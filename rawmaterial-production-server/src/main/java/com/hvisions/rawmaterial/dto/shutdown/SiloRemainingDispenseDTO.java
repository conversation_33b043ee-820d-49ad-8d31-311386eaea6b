package com.hvisions.rawmaterial.dto.shutdown;

import com.hvisions.rawmaterial.dto.validation.MaterialType;
import com.hvisions.rawmaterial.dto.validation.ShutdownTimeRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 筒仓剩余发放量DTO
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(description = "筒仓剩余发放量信息")
@ShutdownTimeRange(shutdownStartField = "shutdownStartTime", statisticsTimeField = "statisticsTime", maxDays = 365)
public class SiloRemainingDispenseDTO {

    @ApiModelProperty(value = "物料类型", example = "高粱", notes = "物料类型：高粱、稻壳等")
    @NotBlank(message = "物料类型不能为空")
    @MaterialType(allowedTypes = {"高粱", "稻壳"})
    private String materialType;

    @ApiModelProperty(value = "统计时间", example = "2024-01-15 10:00:00")
    @NotNull(message = "统计时间不能为空")
    private Date statisticsTime;

    @ApiModelProperty(value = "停产统计开始时间", example = "2024-01-01 00:00:00")
    private Date shutdownStartTime;

    @ApiModelProperty(value = "车间需求统计列表")
    @Valid
    private List<WorkshopDemandDTO> workshopDemands;

    @ApiModelProperty(value = "筒仓信息列表")
    @Valid
    private List<SiloInfoDTO> siloInfos;

    /**
     * 车间需求DTO
     */
    @Data
    @ApiModel(description = "车间需求信息")
    public static class WorkshopDemandDTO {

        @ApiModelProperty(value = "车间名称", example = "一车间")
        @NotBlank(message = "车间名称不能为空")
        @Size(max = 50, message = "车间名称长度不能超过50个字符")
        private String workshopName;

        @ApiModelProperty(value = "需求量", example = "1000.000", notes = "各车间提报的最新停产物料需求量+提报的更新时间之前到统计时间之间的已发放量")
        @DecimalMin(value = "0", message = "需求量不能为负数")
        @Digits(integer = 10, fraction = 3, message = "需求量格式不正确，整数部分最多10位，小数部分最多3位")
        private BigDecimal demandQuantity;

        @ApiModelProperty(value = "已发放量", example = "800.000", notes = "统计该车间在停产统计开始时间之后中心碎料斗仓的发放量汇总")
        @DecimalMin(value = "0", message = "已发放量不能为负数")
        @Digits(integer = 10, fraction = 3, message = "已发放量格式不正确，整数部分最多10位，小数部分最多3位")
        private BigDecimal dispensedQuantity;

        @ApiModelProperty(value = "库存量", example = "200.000", notes = "采集的中控实时库存量")
        @DecimalMin(value = "0", message = "库存量不能为负数")
        @Digits(integer = 10, fraction = 3, message = "库存量格式不正确，整数部分最多10位，小数部分最多3位")
        private BigDecimal stockQuantity;

        @ApiModelProperty(value = "剩余发放量", example = "200.000", notes = "需求量-已发放量")
        @Digits(integer = 10, fraction = 3, message = "剩余发放量格式不正确，整数部分最多10位，小数部分最多3位")
        private BigDecimal remainingDispenseQuantity;

        @ApiModelProperty(value = "发放进度", example = "0.80", notes = "已发放量/需求量")
        @DecimalMin(value = "0", message = "发放进度不能为负数")
        @DecimalMax(value = "1", message = "发放进度不能超过1")
        @Digits(integer = 1, fraction = 4, message = "发放进度格式不正确，整数部分最多1位，小数部分最多4位")
        private BigDecimal dispenseProgress;

        @ApiModelProperty(value = "需求提报时间", example = "2024-01-10 09:00:00")
        private Date demandReportTime;

        @ApiModelProperty(value = "单位", example = "吨")
        @NotBlank(message = "单位不能为空")
        @Size(max = 10, message = "单位长度不能超过10个字符")
        private String unit;
    }

    /**
     * 筒仓信息DTO
     */
    @Data
    @ApiModel(description = "筒仓信息")
    public static class SiloInfoDTO {

        @ApiModelProperty(value = "筒仓类型", example = "中心碎料斗", notes = "筒仓类型：中心碎料斗、中心碎料仓、中心缓存仓、后处理暂存仓、前处理存储仓")
        @NotBlank(message = "筒仓类型不能为空")
        @Size(max = 50, message = "筒仓类型长度不能超过50个字符")
        private String siloType;

        @ApiModelProperty(value = "筒仓编码", example = "SILO001")
        @NotBlank(message = "筒仓编码不能为空")
        @Size(max = 50, message = "筒仓编码长度不能超过50个字符")
        private String siloCode;

        @ApiModelProperty(value = "筒仓名称", example = "中心碎料斗1号仓")
        @NotBlank(message = "筒仓名称不能为空")
        @Size(max = 100, message = "筒仓名称长度不能超过100个字符")
        private String siloName;

        @ApiModelProperty(value = "库存量", example = "500.000", notes = "采集的中控实时库存量")
        @DecimalMin(value = "0", message = "库存量不能为负数")
        @Digits(integer = 10, fraction = 3, message = "库存量格式不正确，整数部分最多10位，小数部分最多3位")
        private BigDecimal stockQuantity;

        @ApiModelProperty(value = "剩余发放量", example = "300.000", notes = "根据不同筒仓类型计算")
        @Digits(integer = 10, fraction = 3, message = "剩余发放量格式不正确，整数部分最多10位，小数部分最多3位")
        private BigDecimal remainingDispenseQuantity;

        @ApiModelProperty(value = "计算说明", example = "中心碎料斗剩余发放量-中心碎料斗库存")
        @Size(max = 200, message = "计算说明长度不能超过200个字符")
        private String calculationDescription;

        @ApiModelProperty(value = "单位", example = "吨")
        @NotBlank(message = "单位不能为空")
        @Size(max = 10, message = "单位长度不能超过10个字符")
        private String unit;

        @ApiModelProperty(value = "排序序号", example = "1", notes = "用于筒仓显示排序")
        @Min(value = 1, message = "排序序号必须大于0")
        @Max(value = 999, message = "排序序号不能超过999")
        private Integer sortOrder;

        @ApiModelProperty(value = "是否启用", example = "true")
        @NotNull(message = "是否启用不能为空")
        private Boolean enabled;
    }
}