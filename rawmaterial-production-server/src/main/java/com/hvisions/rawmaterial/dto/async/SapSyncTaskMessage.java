package com.hvisions.rawmaterial.dto.async;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * SAP库存同步任务消息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SapSyncTaskMessage extends AsyncTaskMessage {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 同步类型：FULL-全量同步，PARTIAL-部分同步
     */
    private String syncType;
    
    /**
     * 物料编码列表（部分同步时使用）
     */
    private List<String> materialCodes;
    
    /**
     * 仓库编码列表（部分同步时使用）
     */
    private List<String> warehouseCodes;
    
    /**
     * 是否强制同步
     */
    private Boolean forceSync = false;
    
    /**
     * 同步范围描述
     */
    private String syncScope;
    
    public SapSyncTaskMessage() {
        super("SAP_SYNC", "SAP库存同步任务");
    }
    
    public SapSyncTaskMessage(String syncType, List<String> materialCodes) {
        this();
        this.syncType = syncType;
        this.materialCodes = materialCodes;
        this.syncScope = buildSyncScope();
    }
    
    /**
     * 构建同步范围描述
     */
    private String buildSyncScope() {
        if ("FULL".equals(syncType)) {
            return "全量同步所有物料库存";
        } else if (materialCodes != null && !materialCodes.isEmpty()) {
            return String.format("同步指定物料库存，共%d个物料", materialCodes.size());
        } else {
            return "部分同步";
        }
    }
}