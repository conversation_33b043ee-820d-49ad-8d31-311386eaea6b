package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 班组人员分配DTO
 * @Date: 2024/06/13
 */
@Data
@ApiModel(value = "班组人员分配信息")
public class WorkGroupAssignDTO {

    @ApiModelProperty(value = "班组类型：1-高粱组，2-稻壳组")
    @NotNull(message = "班组类型不能为空")
    private Integer groupType;

    @ApiModelProperty(value = "班次：1-一班，2-二班，3-三班")
    @NotNull(message = "班次不能为空")
    private Integer shift;

    @ApiModelProperty(value = "用户ID列表")
    private List<Integer> userIds;
} 