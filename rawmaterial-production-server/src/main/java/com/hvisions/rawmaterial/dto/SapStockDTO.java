package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: SAP库存数据DTO
 * @Date: 2024/07/28
 */
@Data
@ApiModel(value = "SAP库存数据信息")
public class SapStockDTO {

    @ApiModelProperty(value = "物料编码", example = "MAT001", notes = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称", example = "原料A", notes = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "仓库编码", example = "WH001", notes = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称", example = "原料仓库", notes = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库存数量", example = "1000.500", notes = "SAP系统中的库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "可用库存", example = "950.500", notes = "可用库存数量")
    private BigDecimal availableQuantity;

    @ApiModelProperty(value = "冻结库存", example = "50.000", notes = "冻结库存数量")
    private BigDecimal frozenQuantity;

    @ApiModelProperty(value = "计量单位", example = "吨", notes = "计量单位")
    private String unit;

    @ApiModelProperty(value = "库存价值", example = "50000.00", notes = "库存价值")
    private BigDecimal stockValue;

    @ApiModelProperty(value = "货币单位", example = "CNY", notes = "货币单位")
    private String currency;

    @ApiModelProperty(value = "最后更新时间", example = "2024-01-15 10:30:00", notes = "SAP中的最后更新时间")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "批次号", example = "BATCH001", notes = "批次号")
    private String batchNumber;

    @ApiModelProperty(value = "库存类型", example = "1", notes = "库存类型：1-正常库存，2-质检库存，3-冻结库存")
    private Integer stockType;

    @ApiModelProperty(value = "库存类型描述", example = "正常库存", notes = "库存类型描述")
    private String stockTypeDesc;

    @ApiModelProperty(value = "工厂编码", example = "1000", notes = "工厂编码")
    private String plantCode;

    @ApiModelProperty(value = "工厂名称", example = "主工厂", notes = "工厂名称")
    private String plantName;

    @ApiModelProperty(value = "存储位置", example = "A001", notes = "存储位置")
    private String storageLocation;

    @ApiModelProperty(value = "供应商编码", example = "SUP001", notes = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "供应商A", notes = "供应商名称")
    private String supplierName;
}