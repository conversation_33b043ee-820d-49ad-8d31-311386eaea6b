package com.hvisions.rawmaterial.dto.monitoring;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SAP接口指标DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class SapInterfaceMetrics {
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 总调用次数
     */
    private Integer totalCalls;
    
    /**
     * 成功调用次数
     */
    private Integer successCalls;
    
    /**
     * 失败调用次数
     */
    private Integer failedCalls;
    
    /**
     * 成功率(%)
     */
    private Double successRate;
    
    /**
     * 平均响应时间(毫秒)
     */
    private Double averageResponseTime;
    
    /**
     * 最大响应时间(毫秒)
     */
    private Long maxResponseTime;
    
    /**
     * 最小响应时间(毫秒)
     */
    private Long minResponseTime;
}