package com.hvisions.rawmaterial.dto.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 物料类型验证注解
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MaterialTypeValidator.class)
@Documented
public @interface MaterialType {
    
    String message() default "物料类型不正确，只能是：高粱、稻壳";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 允许的物料类型
     */
    String[] allowedTypes() default {"高粱", "稻壳"};
}