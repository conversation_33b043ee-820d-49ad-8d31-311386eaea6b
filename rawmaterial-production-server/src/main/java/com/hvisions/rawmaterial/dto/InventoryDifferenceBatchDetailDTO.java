package com.hvisions.rawmaterial.dto;

import com.hvisions.rawmaterial.consts.ValidateType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异批量处理明细DTO
 * @Date: 2024/07/14
 */
@Data
@ApiModel(value = "库存差异批量处理明细")
public class InventoryDifferenceBatchDetailDTO {

    @ApiModelProperty(value = "差异处理ID", required = true, example = "1001", notes = "差异处理主表ID")
    @NotNull(message = "差异处理ID不能为空", groups = {ValidateType.Add.class, ValidateType.Update.class})
    @Min(value = 1, message = "差异处理ID必须大于0")
    private Integer differenceId;

    @ApiModelProperty(value = "处理备注", example = "批量处理明细备注", notes = "处理备注，长度不超过500个字符")
    @Size(max = 500, message = "处理备注长度不能超过500个字符")
    private String processRemark;

    @ApiModelProperty(value = "差异处理明细列表", required = true, notes = "差异处理明细列表")
    @NotEmpty(message = "差异处理明细列表不能为空")
    @Valid
    private List<InventoryDifferenceDetailDTO> detailList;
}