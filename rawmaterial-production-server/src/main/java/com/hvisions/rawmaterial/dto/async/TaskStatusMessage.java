package com.hvisions.rawmaterial.dto.async;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务状态消息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class TaskStatusMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 进度百分比（0-100）
     */
    private Integer progress = 0;
    
    /**
     * 状态更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 状态描述
     */
    private String statusDescription;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 处理结果
     */
    private Object result;
    
    /**
     * 已处理数量
     */
    private Integer processedCount = 0;
    
    /**
     * 总数量
     */
    private Integer totalCount = 0;
    
    /**
     * 成功数量
     */
    private Integer successCount = 0;
    
    /**
     * 失败数量
     */
    private Integer failureCount = 0;
    
    public TaskStatusMessage() {
        this.updateTime = new Date();
    }
    
    public TaskStatusMessage(String taskId, String taskType, TaskStatus status) {
        this();
        this.taskId = taskId;
        this.taskType = taskType;
        this.status = status;
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待处理"),
        RUNNING("执行中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消"),
        TIMEOUT("超时");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 计算进度百分比
     */
    public void calculateProgress() {
        if (totalCount > 0) {
            this.progress = (int) ((double) processedCount / totalCount * 100);
        }
    }
    
    /**
     * 更新处理统计
     */
    public void updateProcessStats(int processed, int success, int failure) {
        this.processedCount = processed;
        this.successCount = success;
        this.failureCount = failure;
        calculateProgress();
    }
}