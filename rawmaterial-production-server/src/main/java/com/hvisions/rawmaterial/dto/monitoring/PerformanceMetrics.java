package com.hvisions.rawmaterial.dto.monitoring;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 性能指标DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PerformanceMetrics {
    
    /**
     * 指标名称
     */
    private String metricName;
    
    /**
     * 总次数
     */
    private Integer totalCount;
    
    /**
     * 成功次数
     */
    private Integer successCount;
    
    /**
     * 失败次数
     */
    private Integer failureCount;
    
    /**
     * 成功率(%)
     */
    private Double successRate;
    
    /**
     * 平均执行时间(毫秒)
     */
    private Double averageExecutionTime;
    
    /**
     * 最大执行时间(毫秒)
     */
    private Long maxExecutionTime;
    
    /**
     * 最小执行时间(毫秒)
     */
    private Long minExecutionTime;
    
    /**
     * 执行时间(单次记录)
     */
    private Double executionTime;
    
    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
    
    /**
     * 参数信息
     */
    private String parameters;
    
    /**
     * SQL语句
     */
    private String sqlStatement;
}