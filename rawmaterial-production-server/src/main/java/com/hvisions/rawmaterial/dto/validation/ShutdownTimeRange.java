package com.hvisions.rawmaterial.dto.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 停产时间范围验证注解
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ShutdownTimeRangeValidator.class)
@Documented
public @interface ShutdownTimeRange {
    
    String message() default "停产时间范围不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 停产开始时间字段名
     */
    String shutdownStartField() default "shutdownStartTime";
    
    /**
     * 统计时间字段名
     */
    String statisticsTimeField() default "statisticsTime";
    
    /**
     * 最大时间跨度（天）
     */
    int maxDays() default 365;
}