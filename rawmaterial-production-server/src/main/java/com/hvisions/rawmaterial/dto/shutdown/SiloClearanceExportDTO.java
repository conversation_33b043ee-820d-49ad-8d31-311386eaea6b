package com.hvisions.rawmaterial.dto.shutdown;

import com.hvisions.rawmaterial.dto.validation.MaterialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 筒仓清仓数据导出DTO
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(description = "筒仓清仓数据导出")
public class SiloClearanceExportDTO {

    @ApiModelProperty(value = "导出类型", example = "1", notes = "导出类型：1-全部数据，2-车间需求统计，3-筒仓库存统计", allowableValues = "1,2,3")
    @Pattern(regexp = "^[1-3]$", message = "导出类型只能是：1-全部数据，2-车间需求统计，3-筒仓库存统计")
    private String exportType = "1";

    @ApiModelProperty(value = "导出格式", example = "excel", notes = "导出格式：excel-Excel格式，csv-CSV格式", allowableValues = "excel,csv")
    @Pattern(regexp = "^(excel|csv)$", message = "导出格式只能是：excel、csv")
    private String exportFormat = "excel";

    @ApiModelProperty(value = "文件名", example = "停产物料清仓统计_20240115", notes = "导出文件名，不包含扩展名")
    @Size(max = 100, message = "文件名长度不能超过100个字符")
    private String fileName;

    @ApiModelProperty(value = "物料类型", example = "高粱", notes = "物料类型：高粱、稻壳", required = true)
    @NotBlank(message = "物料类型不能为空")
    @MaterialType(allowedTypes = {"高粱", "稻壳"})
    private String materialType;

    @ApiModelProperty(value = "统计时间", example = "2024-01-15 10:00:00", notes = "清仓统计的截止时间")
    private Date statisticsTime;

    @ApiModelProperty(value = "停产统计开始时间", example = "2024-01-01 00:00:00", notes = "停产统计的开始时间")
    private Date shutdownStartTime;

    @ApiModelProperty(value = "车间名称列表", example = "[\"一车间\", \"二车间\"]", notes = "要导出的车间名称列表，为空则导出全部")
    private List<@Size(max = 50, message = "车间名称长度不能超过50个字符") String> workshopNames;

    @ApiModelProperty(value = "筒仓类型列表", example = "[\"中心碎料斗\", \"中心碎料仓\"]", notes = "要导出的筒仓类型列表，为空则导出全部")
    private List<@Size(max = 50, message = "筒仓类型长度不能超过50个字符") String> siloTypes;

    @ApiModelProperty(value = "是否包含计算说明", example = "true", notes = "是否在导出文件中包含计算说明")
    private Boolean includeCalculationDescription = true;

    @ApiModelProperty(value = "是否包含统计汇总", example = "true", notes = "是否在导出文件中包含统计汇总信息")
    private Boolean includeSummary = true;

    @ApiModelProperty(value = "是否包含库存为零的筒仓", example = "false", notes = "是否在导出中包含库存为零的筒仓")
    private Boolean includeZeroStock = false;

    // 以下字段用于导出数据的实际内容
    @ApiModelProperty(value = "物料类型（导出数据）", example = "高粱")
    private String exportMaterialType;

    @ApiModelProperty(value = "筒仓类型（导出数据）", example = "中心碎料斗")
    private String exportSiloType;

    @ApiModelProperty(value = "筒仓编码（导出数据）", example = "SILO001")
    private String exportSiloCode;

    @ApiModelProperty(value = "筒仓名称（导出数据）", example = "中心碎料斗1号仓")
    private String exportSiloName;

    @ApiModelProperty(value = "车间名称（导出数据）", example = "一车间")
    private String exportWorkshopName;

    @ApiModelProperty(value = "需求量（导出数据）", example = "1000.000")
    private BigDecimal exportDemandQuantity;

    @ApiModelProperty(value = "已发放量（导出数据）", example = "800.000")
    private BigDecimal exportDispensedQuantity;

    @ApiModelProperty(value = "库存量（导出数据）", example = "200.000")
    private BigDecimal exportStockQuantity;

    @ApiModelProperty(value = "剩余发放量（导出数据）", example = "200.000")
    private BigDecimal exportRemainingDispenseQuantity;

    @ApiModelProperty(value = "发放进度（导出数据）", example = "0.80")
    private BigDecimal exportDispenseProgress;

    @ApiModelProperty(value = "计算说明（导出数据）", example = "需求量-已发放量")
    private String exportCalculationDescription;

    @ApiModelProperty(value = "单位（导出数据）", example = "吨")
    private String exportUnit;

    @ApiModelProperty(value = "统计时间（导出数据）", example = "2024-01-15 10:00:00")
    private Date exportStatisticsTime;

    @ApiModelProperty(value = "需求提报时间（导出数据）", example = "2024-01-10 09:00:00")
    private Date exportDemandReportTime;
}