package com.hvisions.rawmaterial.dto.validation;

import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 筒仓类型验证器
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class SiloTypeValidator implements ConstraintValidator<SiloType, String> {

    private String materialType;
    private Set<String> sorghumSiloTypes;
    private Set<String> riceHuskSiloTypes;

    @Override
    public void initialize(SiloType constraintAnnotation) {
        this.materialType = constraintAnnotation.materialType();
        this.sorghumSiloTypes = new HashSet<>(Arrays.asList(constraintAnnotation.sorghumSiloTypes()));
        this.riceHuskSiloTypes = new HashSet<>(Arrays.asList(constraintAnnotation.riceHuskSiloTypes()));
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 空值由其他注解处理
        if (!StringUtils.hasText(value)) {
            return true;
        }
        
        String trimmedValue = value.trim();
        
        // 如果没有指定物料类型，则检查是否在所有允许的筒仓类型中
        if (!StringUtils.hasText(materialType)) {
            Set<String> allSiloTypes = new HashSet<>();
            allSiloTypes.addAll(sorghumSiloTypes);
            allSiloTypes.addAll(riceHuskSiloTypes);
            return allSiloTypes.contains(trimmedValue);
        }
        
        // 根据物料类型验证筒仓类型
        switch (materialType.trim()) {
            case "高粱":
                return sorghumSiloTypes.contains(trimmedValue);
            case "稻壳":
                return riceHuskSiloTypes.contains(trimmedValue);
            default:
                return false;
        }
    }
}