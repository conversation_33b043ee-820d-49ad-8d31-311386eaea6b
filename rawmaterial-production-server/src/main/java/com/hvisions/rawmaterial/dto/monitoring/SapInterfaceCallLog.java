package com.hvisions.rawmaterial.dto.monitoring;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SAP接口调用日志DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class SapInterfaceCallLog {
    
    /**
     * 调用ID
     */
    private String callId;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 响应时间(毫秒)
     */
    private Long responseTime;
    
    /**
     * 调用状态
     */
    private String callStatus;
    
    /**
     * 请求参数
     */
    private String requestParams;
    
    /**
     * 响应数据
     */
    private String responseData;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}