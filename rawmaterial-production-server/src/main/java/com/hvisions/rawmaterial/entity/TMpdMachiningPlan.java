package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 预处理加工计划
 * @date 2022/4/22 10:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_machining_plan")
public class TMpdMachiningPlan extends SysBase {

    /**
     * 计划编号
     */
    private String planNo;
    /**
     * 计划日期
     */
    private Date planDate;
    /**
     * 供应商id
     */
    private Integer vendorId;
    /**
     * 物料id
     */
    private Integer materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料单位
     */
    private String unit;
    /**
     * 计划处理数量
     */
    private BigDecimal planDealNumber;
    /**
     * 实际处理数量
     */
    private BigDecimal actualNumber;

}
