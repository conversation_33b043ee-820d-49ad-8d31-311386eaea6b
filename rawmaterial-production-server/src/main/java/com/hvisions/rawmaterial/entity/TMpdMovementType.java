package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Description: 移动类型
 * @author: Jcao
 * @time: 2022/4/22 18:37
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_mpd_movement_type")
public class TMpdMovementType extends SysBase {

    /*
     * SAP移动类型编号
     */
    private String sapCode;

    /*
     * 规则名称
     */
    private String name;

}
