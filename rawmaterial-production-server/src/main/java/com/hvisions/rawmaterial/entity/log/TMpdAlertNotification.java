package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 告警通知实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_alert_notification")
public class TMpdAlertNotification extends SysBase {
    
    /**
     * 规则ID
     */
    @Column(name = "rule_id", nullable = false)
    private Integer ruleId;
    
    /**
     * 告警类型
     */
    @Column(name = "alert_type", length = 50, nullable = false)
    private String alertType;
    
    /**
     * 告警级别
     */
    @Column(name = "alert_level", length = 20, nullable = false)
    private String alertLevel;
    
    /**
     * 告警消息
     */
    @Column(name = "alert_message", length = 1000, nullable = false)
    private String alertMessage;
    
    /**
     * 指标名称
     */
    @Column(name = "metric_name", length = 100)
    private String metricName;
    
    /**
     * 当前值
     */
    @Column(name = "current_value", length = 100)
    private String currentValue;
    
    /**
     * 阈值
     */
    @Column(name = "threshold_value", length = 100)
    private String thresholdValue;
    
    /**
     * 告警时间
     */
    @Column(name = "alert_time", nullable = false)
    private LocalDateTime alertTime;
    
    /**
     * 告警状态
     */
    @Column(name = "alert_status", length = 20, nullable = false)
    private String alertStatus;
    
    /**
     * 确认用户ID
     */
    @Column(name = "acknowledge_user_id")
    private Integer acknowledgeUserId;
    
    /**
     * 确认用户名
     */
    @Column(name = "acknowledge_user_name", length = 100)
    private String acknowledgeUserName;
    
    /**
     * 确认时间
     */
    @Column(name = "acknowledge_time")
    private LocalDateTime acknowledgeTime;
    
    /**
     * 确认备注
     */
    @Column(name = "acknowledge_remark", length = 500)
    private String acknowledgeRemark;
}