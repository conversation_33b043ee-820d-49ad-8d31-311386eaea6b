package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * SAP接口调用日志实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_sap_interface_call_log")
public class TMpdSapInterfaceCallLog extends SysBase {
    
    /**
     * 调用ID
     */
    @Column(name = "call_id", length = 50, nullable = false, unique = true)
    private String callId;
    
    /**
     * 接口名称
     */
    @Column(name = "interface_name", length = 100, nullable = false)
    private String interfaceName;
    
    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    /**
     * 响应时间(毫秒)
     */
    @Column(name = "response_time")
    private Long responseTime;
    
    /**
     * 调用状态: STARTED, SUCCESS, FAILED
     */
    @Column(name = "call_status", length = 20, nullable = false)
    private String callStatus;
    
    /**
     * 请求参数
     */
    @Column(name = "request_params", columnDefinition = "TEXT")
    private String requestParams;
    
    /**
     * 响应数据
     */
    @Column(name = "response_data", columnDefinition = "TEXT")
    private String responseData;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
}