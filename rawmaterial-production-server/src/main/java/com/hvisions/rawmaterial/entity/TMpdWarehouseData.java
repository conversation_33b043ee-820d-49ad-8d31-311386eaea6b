package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Description: 库内数据
 * @author: yyy
 * @time: 2022/6/13 9:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_warehouse_data")
public class TMpdWarehouseData extends SysBase {

    /*
     * 库位id
     */
    private Integer storageId;

    /*
     * 批次
     */
    private String batch;

    /*
     * 供应商id
     */
    private Integer vendorId;

    /*
     * 库存数量
     */
    private BigDecimal stockQuantity;

    /*
     * 物料id
     */
    private Integer materialId;
    /*
     * 物料编码
     */
    private String materialCode;
    /*
     * 物料名称
     */
    private String materialName;
    /*
     * 物料单位
     */
    private String unit;

}
