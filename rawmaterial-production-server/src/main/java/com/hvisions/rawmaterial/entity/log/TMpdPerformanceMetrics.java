package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 性能指标实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_performance_metrics")
public class TMpdPerformanceMetrics extends SysBase {
    
    /**
     * 指标类型: METHOD_EXECUTION, DATABASE_QUERY, CACHE_OPERATION
     */
    @Column(name = "metric_type", length = 50, nullable = false)
    private String metricType;
    
    /**
     * 指标名称
     */
    @Column(name = "metric_name", length = 200, nullable = false)
    private String metricName;
    
    /**
     * 执行时间(毫秒)
     */
    @Column(name = "execution_time", nullable = false)
    private Long executionTime;
    
    /**
     * 是否成功
     */
    @Column(name = "success")
    private Boolean success;
    
    /**
     * 记录时间
     */
    @Column(name = "record_time", nullable = false)
    private LocalDateTime recordTime;
    
    /**
     * 参数信息
     */
    @Column(name = "parameters", columnDefinition = "TEXT")
    private String parameters;
    
    /**
     * SQL语句
     */
    @Column(name = "sql_statement", columnDefinition = "TEXT")
    private String sqlStatement;
    
    /**
     * 记录数量
     */
    @Column(name = "record_count")
    private Integer recordCount;
    
    /**
     * 缓存键
     */
    @Column(name = "cache_key", length = 500)
    private String cacheKey;
    
    /**
     * 缓存是否命中
     */
    @Column(name = "cache_hit")
    private Boolean cacheHit;
}