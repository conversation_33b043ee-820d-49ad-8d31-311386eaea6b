package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Description: 工单工艺路线
 * @author: Jcao
 * @time: 2022/4/22 10:56
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_mpd_order_process")
public class TMpdOrderProcess extends SysBase {

    /*
     * 工单类型id
     */
    private Integer orderTypeId;

    /*
     * 发出仓库
     */
    private Integer issueWarehouseId;

    /*
     * 接收仓库
     */
    private Integer acceptWarehouseId;

    /*
     * 类型说明
     */
    private String declaration;

}
