package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 告警规则实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_alert_rule")
public class TMpdAlertRule extends SysBase {
    
    /**
     * 规则名称
     */
    @Column(name = "rule_name", length = 100, nullable = false)
    private String ruleName;
    
    /**
     * 规则类型
     */
    @Column(name = "rule_type", length = 50, nullable = false)
    private String ruleType;
    
    /**
     * 指标名称
     */
    @Column(name = "metric_name", length = 100, nullable = false)
    private String metricName;
    
    /**
     * 阈值
     */
    @Column(name = "threshold_value", nullable = false)
    private Double thresholdValue;
    
    /**
     * 比较操作符
     */
    @Column(name = "operator", length = 10, nullable = false)
    private String operator;
    
    /**
     * 告警级别
     */
    @Column(name = "alert_level", length = 20, nullable = false)
    private String alertLevel;
    
    /**
     * 检查间隔(分钟)
     */
    @Column(name = "check_interval", nullable = false)
    private Integer checkInterval;
    
    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;
    
    /**
     * 规则描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}