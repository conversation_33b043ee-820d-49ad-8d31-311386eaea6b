package com.hvisions.rawmaterial.entity.schedule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.WorkGroupEmployeeDTO;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 班组成员表
 * <AUTHOR>
 */
@Slf4j
@Data
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_mpd_work_group_member")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_work_group_member", comment = "班组成员表")
public class TMpdWorkGroupMember extends SysBase {
    
    /**
     * 班组类型：1-高粱处理，2-稻壳处理
     */
    @Column(columnDefinition = "int COMMENT '班组类型：1-高粱处理，2-稻壳处理'")
    private Integer groupType;
    
    /**
     * 班组成员
     */
    @Column(columnDefinition = "text COMMENT '班组成员'")
    @Type(type = "text")
    private String members;
    
    /**
     * 生效开始日期
     */
    @Temporal(TemporalType.DATE)
    @Column(columnDefinition = "date COMMENT '生效开始日期'")
    private Date effectiveStartDate;
    
    /**
     * 生效结束日期
     */
    @Temporal(TemporalType.DATE)
    @Column(columnDefinition = "date COMMENT '生效结束日期'")
    private Date effectiveEndDate;
    
    /**
     * 备注
     */
    @Column(length = 200, columnDefinition = "varchar(200) COMMENT '备注'")
    private String remark;
    
    /**
     * 获取班组成员列表
     */
    @Transient
    private List<WorkGroupEmployeeDTO> memberList;
    
    /**
     * 获取班组成员列表
     * @return 班组成员列表
     */
    public List<WorkGroupEmployeeDTO> getMemberList() {
        if (this.members != null && !this.members.isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                return mapper.readValue(this.members, new TypeReference<List<WorkGroupEmployeeDTO>>() {});
            } catch (IOException e) {
                log.error("解析班组成员列表出错", e);
            }
        }
        return new ArrayList<>();
    }
    
    /**
     * 设置班组成员列表
     * @param memberList 班组成员列表
     */
    public void setMemberList(List<WorkGroupEmployeeDTO> memberList) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.members = mapper.writeValueAsString(memberList);
        } catch (IOException e) {
            log.error("序列化班组成员列表出错", e);
            this.members = "[]";
        }
    }
} 