package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 操作日志实体类
 * 记录用户的所有操作行为
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_operation_log")
public class TMpdOperationLog extends SysBase {
    
    /**
     * 业务模块
     */
    @Column(name = "module", length = 50)
    private String module;
    
    /**
     * 操作名称
     */
    @Column(name = "operation", length = 100)
    private String operation;
    
    /**
     * 操作类型
     */
    @Column(name = "operation_type", length = 20)
    private String operationType;
    
    /**
     * 操作用户ID
     */
    @Column(name = "user_id")
    private Integer userId;
    
    /**
     * 操作用户名
     */
    @Column(name = "username", length = 50)
    private String username;
    
    /**
     * 用户真实姓名
     */
    @Column(name = "real_name", length = 50)
    private String realName;
    
    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    @Column(name = "department_name", length = 100)
    private String departmentName;
    
    /**
     * 请求参数
     */
    @Column(name = "request_params", columnDefinition = "TEXT")
    private String requestParams;
    
    /**
     * 返回结果
     */
    @Column(name = "response_result", columnDefinition = "TEXT")
    private String responseResult;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 操作状态（0-失败，1-成功）
     */
    @Column(name = "status")
    private Integer status;
    
    /**
     * 执行时间（毫秒）
     */
    @Column(name = "execution_time")
    private Long executionTime;
    
    /**
     * 客户端IP
     */
    @Column(name = "client_ip", length = 50)
    private String clientIp;
    
    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    /**
     * 请求URL
     */
    @Column(name = "request_url", length = 500)
    private String requestUrl;
    
    /**
     * 请求方法
     */
    @Column(name = "request_method", length = 10)
    private String requestMethod;
    
    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date operationTime;
}