package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 蒸糠报废处理详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_chaff_scrap_detail")
public class TMpdChaffScrapDetail extends SysBase {

    /**
     * 报废处理id
     */
    private Integer scrapId;

    /**
     * 物料id
     */
    private Integer materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 称量方式
     */
    private String weightType;

    /**
     * 处理数量
     */
    private BigDecimal dealQuantity;

    /**
     * 入场重量
     */
    private BigDecimal enterQuantity;

    /**
     * 出厂重量
     */
    private BigDecimal outQuantity;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 记录人员id
     */
    private Integer recordPeopleId;

    /**
     * 记录人员
     */
    private String recordPeople;

    /**
     * 车牌号
     */
    private String licensePlateNumber;

    /**
     * 过磅任务id
     */
    private Integer productionWeightId;

}
