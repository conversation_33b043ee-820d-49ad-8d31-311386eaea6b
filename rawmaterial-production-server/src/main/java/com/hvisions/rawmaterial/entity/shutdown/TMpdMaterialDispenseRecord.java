package com.hvisions.rawmaterial.entity.shutdown;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物料发放记录实体类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Table(name = "t_mpd_material_dispense_record")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_material_dispense_record", comment = "物料发放记录")
@Entity
@TableName("t_mpd_material_dispense_record")
@KeySequence("t_mpd_material_dispense_record_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdMaterialDispenseRecord extends SysBase {

    /**
     * 发放单号
     */
    @Column(name = "dispense_no", length = 50, columnDefinition = "varchar(50) NOT NULL COMMENT '发放单号'")
    private String dispenseNo;

    /**
     * 车间编码
     */
    @Column(name = "workshop_code", length = 20, columnDefinition = "varchar(20) COMMENT '车间编码'")
    private String workshopCode;

    /**
     * 车间名称
     */
    @Column(name = "workshop_name", length = 50, columnDefinition = "varchar(50) COMMENT '车间名称'")
    private String workshopName;

    /**
     * 中心编码
     */
    @Column(name = "center_code", length = 20, columnDefinition = "varchar(20) COMMENT '中心编码'")
    private String centerCode;

    /**
     * 中心名称
     */
    @Column(name = "center_name", length = 50, columnDefinition = "varchar(50) COMMENT '中心名称'")
    private String centerName;

    /**
     * 筒仓编码
     */
    @Column(name = "silo_code", length = 50, columnDefinition = "varchar(50) COMMENT '筒仓编码'")
    private String siloCode;

    /**
     * 筒仓名称
     */
    @Column(name = "silo_name", length = 100, columnDefinition = "varchar(100) COMMENT '筒仓名称'")
    private String siloName;

    /**
     * 筒仓类型
     */
    @Column(name = "silo_type", length = 50, columnDefinition = "varchar(50) COMMENT '筒仓类型'")
    private String siloType;

    /**
     * 物料编码
     */
    @Column(name = "material_code", length = 50, columnDefinition = "varchar(50) COMMENT '物料编码'")
    private String materialCode;

    /**
     * 物料名称
     */
    @Column(name = "material_name", length = 100, columnDefinition = "varchar(100) COMMENT '物料名称'")
    private String materialName;

    /**
     * 物料类型
     */
    @Column(name = "material_type", length = 20, columnDefinition = "varchar(20) COMMENT '物料类型'")
    private String materialType;

    /**
     * 发放量
     */
    @Column(name = "dispense_quantity", precision = 20, scale = 6, columnDefinition = "decimal(20,6) DEFAULT 0 COMMENT '发放量'")
    private BigDecimal dispenseQuantity;

    /**
     * 单位
     */
    @Column(name = "unit", length = 10, columnDefinition = "varchar(10) DEFAULT 'kg' COMMENT '单位'")
    private String unit;

    /**
     * 发放时间
     */
    @Column(name = "dispense_time", columnDefinition = "datetime COMMENT '发放时间'")
    private Date dispenseTime;

    /**
     * 操作人
     */
    @Column(name = "operator_name", length = 50, columnDefinition = "varchar(50) COMMENT '操作人'")
    private String operatorName;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id", columnDefinition = "int COMMENT '操作人ID'")
    private Integer operatorId;

    /**
     * 发放状态（0=待发放，1=发放中，2=已发放，3=发放失败）
     */
    @Column(name = "dispense_status", columnDefinition = "int DEFAULT 0 COMMENT '发放状态'")
    private Integer dispenseStatus;

    /**
     * 业务类型（停产清仓、正常生产等）
     */
    @Column(name = "business_type", length = 20, columnDefinition = "varchar(20) COMMENT '业务类型'")
    private String businessType;

    /**
     * 关联单据号
     */
    @Column(name = "related_doc_no", length = 50, columnDefinition = "varchar(50) COMMENT '关联单据号'")
    private String relatedDocNo;

    /**
     * 数据来源
     */
    @Column(name = "data_source", length = 50, columnDefinition = "varchar(50) COMMENT '数据来源'")
    private String dataSource;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500, columnDefinition = "varchar(500) COMMENT '备注'")
    private String remark;
}