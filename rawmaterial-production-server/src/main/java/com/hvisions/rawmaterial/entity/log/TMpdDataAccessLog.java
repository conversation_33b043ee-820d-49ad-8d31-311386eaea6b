package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据访问日志实体类
 * 记录用户对敏感数据的访问和修改
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_data_access_log")
public class TMpdDataAccessLog extends SysBase {
    
    /**
     * 表名
     */
    @Column(name = "table_name", length = 100)
    private String tableName;
    
    /**
     * 操作类型（SELECT, INSERT, UPDATE, DELETE）
     */
    @Column(name = "operation_type", length = 20)
    private String operationType;
    
    /**
     * 数据ID
     */
    @Column(name = "data_id", length = 100)
    private String dataId;
    
    /**
     * 操作用户ID
     */
    @Column(name = "user_id")
    private Integer userId;
    
    /**
     * 操作用户名
     */
    @Column(name = "username", length = 50)
    private String username;
    
    /**
     * 用户真实姓名
     */
    @Column(name = "real_name", length = 50)
    private String realName;
    
    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    @Column(name = "department_name", length = 100)
    private String departmentName;
    
    /**
     * 修改前数据
     */
    @Column(name = "old_data", columnDefinition = "TEXT")
    private String oldData;
    
    /**
     * 修改后数据
     */
    @Column(name = "new_data", columnDefinition = "TEXT")
    private String newData;
    
    /**
     * 客户端IP
     */
    @Column(name = "client_ip", length = 50)
    private String clientIp;
    
    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    /**
     * 访问时间
     */
    @Column(name = "access_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date accessTime;
    
    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}