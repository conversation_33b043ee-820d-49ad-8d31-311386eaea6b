package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 杂质处理单
 * @author: Jcao
 * @time: 2022/4/27 14:32
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_mpd_impurity_deal")
public class TMpdImpurityDeal extends SysBase {

  private String taskOrder;
  private String productionPart;
  private Date dealDate;
  private Integer shiftId;
  private String shift;
  /**
   * 源单号（稻壳生产工单）
   */
  private String sourceOrder;
  private Integer materialId;
  private String materialCode;
  private String materialName;
  private String unit;
  private BigDecimal quantity;
  private String state;

}
