package com.hvisions.rawmaterial.entity.log;

import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 业务操作日志实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_business_log")
public class TMpdBusinessLog extends SysBase {
    
    /**
     * 操作类型
     */
    @Column(name = "operation_type", length = 50, nullable = false)
    private String operationType;
    
    /**
     * 操作描述
     */
    @Column(name = "operation_description", length = 200)
    private String operationDescription;
    
    /**
     * 操作参数
     */
    @Column(name = "operation_params", columnDefinition = "TEXT")
    private String operationParams;
    
    /**
     * 操作结果
     */
    @Column(name = "operation_result", columnDefinition = "TEXT")
    private String operationResult;
    
    /**
     * 操作用户ID
     */
    @Column(name = "user_id")
    private Integer userId;
    
    /**
     * 操作用户名
     */
    @Column(name = "user_name", length = 100)
    private String userName;
    
    /**
     * 操作时间
     */
    @Column(name = "operation_time", nullable = false)
    private LocalDateTime operationTime;
    
    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 50)
    private String ipAddress;
    
    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 异常堆栈
     */
    @Column(name = "stack_trace", columnDefinition = "TEXT")
    private String stackTrace;
    
    /**
     * 执行时长(毫秒)
     */
    @Column(name = "execution_time")
    private Long executionTime;
}