package com.hvisions.rawmaterial.entity.shutdown;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 筒仓库存实体类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Table(name = "t_mpd_silo_inventory")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_silo_inventory", comment = "筒仓库存")
@Entity
@TableName("t_mpd_silo_inventory")
@KeySequence("t_mpd_silo_inventory_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdSiloInventory extends SysBase {

    /**
     * 筒仓编码
     */
    @Column(name = "silo_code", length = 50, columnDefinition = "varchar(50) NOT NULL COMMENT '筒仓编码'")
    private String siloCode;

    /**
     * 筒仓名称
     */
    @Column(name = "silo_name", length = 100, columnDefinition = "varchar(100) COMMENT '筒仓名称'")
    private String siloName;

    /**
     * 筒仓类型
     */
    @Column(name = "silo_type", length = 50, columnDefinition = "varchar(50) COMMENT '筒仓类型'")
    private String siloType;

    /**
     * 物料编码
     */
    @Column(name = "material_code", length = 50, columnDefinition = "varchar(50) COMMENT '物料编码'")
    private String materialCode;

    /**
     * 物料名称
     */
    @Column(name = "material_name", length = 100, columnDefinition = "varchar(100) COMMENT '物料名称'")
    private String materialName;

    /**
     * 库存量
     */
    @Column(name = "stock_quantity", precision = 20, scale = 6, columnDefinition = "decimal(20,6) DEFAULT 0 COMMENT '库存量'")
    private BigDecimal stockQuantity;

    /**
     * 单位
     */
    @Column(name = "unit", length = 10, columnDefinition = "varchar(10) DEFAULT 'kg' COMMENT '单位'")
    private String unit;

    /**
     * 数据更新时间
     */
    @Column(name = "data_update_time", columnDefinition = "datetime COMMENT '数据更新时间'")
    private Date dataUpdateTime;

    /**
     * 数据来源
     */
    @Column(name = "data_source", length = 50, columnDefinition = "varchar(50) COMMENT '数据来源'")
    private String dataSource;

    /**
     * 车间编码
     */
    @Column(name = "workshop_code", length = 20, columnDefinition = "varchar(20) COMMENT '车间编码'")
    private String workshopCode;

    /**
     * 车间名称
     */
    @Column(name = "workshop_name", length = 50, columnDefinition = "varchar(50) COMMENT '车间名称'")
    private String workshopName;

    /**
     * 中心编码
     */
    @Column(name = "center_code", length = 20, columnDefinition = "varchar(20) COMMENT '中心编码'")
    private String centerCode;

    /**
     * 中心名称
     */
    @Column(name = "center_name", length = 50, columnDefinition = "varchar(50) COMMENT '中心名称'")
    private String centerName;

    /**
     * 是否启用
     */
    @Column(name = "enabled", columnDefinition = "bit(1) DEFAULT b'1' COMMENT '是否启用'")
    private Boolean enabled;

    /**
     * 排序序号
     */
    @Column(name = "sort_order", columnDefinition = "int DEFAULT 0 COMMENT '排序序号'")
    private Integer sortOrder;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500, columnDefinition = "varchar(500) COMMENT '备注'")
    private String remark;
}