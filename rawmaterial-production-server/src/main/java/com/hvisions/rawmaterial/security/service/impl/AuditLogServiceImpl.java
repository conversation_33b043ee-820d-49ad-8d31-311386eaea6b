package com.hvisions.rawmaterial.security.service.impl;

import com.hvisions.rawmaterial.dao.log.DataAccessLogRepository;
import com.hvisions.rawmaterial.dao.log.OperationLogRepository;
import com.hvisions.rawmaterial.entity.log.TMpdDataAccessLog;
import com.hvisions.rawmaterial.entity.log.TMpdOperationLog;
import com.hvisions.rawmaterial.security.dto.UserSecurityContext;
import com.hvisions.rawmaterial.security.service.AuditLogService;
import com.hvisions.rawmaterial.security.service.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 审计日志服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Service
public class AuditLogServiceImpl implements AuditLogService {
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private DataAccessLogRepository dataAccessLogRepository;
    
    @Autowired
    private SecurityService securityService;
    
    @Autowired
    private HttpServletRequest request;
    
    @Override
    public void logOperation(String module, String operation, String operationType, 
                           Integer userId, String username, Object params, 
                           Object result, String errorMessage) {
        try {
            UserSecurityContext userContext = securityService.getUserContext(userId);
            
            TMpdOperationLog operationLog = TMpdOperationLog.builder()
                .module(module)
                .operation(operation)
                .operationType(operationType)
                .userId(userId)
                .username(username)
                .realName(userContext != null ? userContext.getRealName() : "")
                .departmentId(userContext != null ? userContext.getDepartmentId() : null)
                .departmentName(userContext != null ? userContext.getDepartmentName() : "")
                .requestParams(params != null ? com.alibaba.fastjson.JSON.toJSONString(params) : null)
                .responseResult(result != null ? com.alibaba.fastjson.JSON.toJSONString(result) : null)
                .errorMessage(errorMessage)
                .status(StringUtils.hasText(errorMessage) ? 0 : 1)
                .clientIp(getClientIp())
                .userAgent(getUserAgent())
                .requestUrl(getRequestUrl())
                .requestMethod(getRequestMethod())
                .operationTime(new Date())
                .createTime(new Date())
                .updateTime(new Date())
                .deleted(false)
                .build();
            
            operationLogRepository.save(operationLog);
            
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }
    
    @Override
    public void logSuccess(String module, String operation, String operationType, 
                         Integer userId, String username, Object params, Object result) {
        logOperation(module, operation, operationType, userId, username, params, result, null);
    }
    
    @Override
    public void logFailure(String module, String operation, String operationType, 
                         Integer userId, String username, Object params, String errorMessage) {
        logOperation(module, operation, operationType, userId, username, params, null, errorMessage);
    }
    
    @Override
    public void logDataAccess(String tableName, String operation, Integer userId, 
                            String username, String dataId, Object oldData, Object newData) {
        try {
            UserSecurityContext userContext = securityService.getUserContext(userId);
            
            TMpdDataAccessLog dataAccessLog = TMpdDataAccessLog.builder()
                .tableName(tableName)
                .operationType(operation)
                .dataId(dataId)
                .userId(userId)
                .username(username)
                .realName(userContext != null ? userContext.getRealName() : "")
                .departmentId(userContext != null ? userContext.getDepartmentId() : null)
                .departmentName(userContext != null ? userContext.getDepartmentName() : "")
                .oldData(oldData != null ? com.alibaba.fastjson.JSON.toJSONString(oldData) : null)
                .newData(newData != null ? com.alibaba.fastjson.JSON.toJSONString(newData) : null)
                .clientIp(getClientIp())
                .userAgent(getUserAgent())
                .accessTime(new Date())
                .createTime(new Date())
                .updateTime(new Date())
                .deleted(false)
                .build();
            
            dataAccessLogRepository.save(dataAccessLog);
            
        } catch (Exception e) {
            log.error("记录数据访问日志失败", e);
        }
    }
    
    @Override
    public List<TMpdOperationLog> queryOperationLogs(Map<String, Object> params) {
        // TODO: 实现复杂查询逻辑
        // 这里简化实现，实际项目中应该使用Specification或自定义查询
        
        Integer userId = (Integer) params.get("userId");
        String module = (String) params.get("module");
        Date startTime = (Date) params.get("startTime");
        Date endTime = (Date) params.get("endTime");
        
        if (userId != null && startTime != null && endTime != null) {
            return operationLogRepository.findByUserIdAndOperationTimeBetweenOrderByOperationTimeDesc(
                userId, startTime, endTime);
        } else if (module != null && startTime != null && endTime != null) {
            return operationLogRepository.findByModuleAndOperationTimeBetweenOrderByOperationTimeDesc(
                module, startTime, endTime);
        } else if (userId != null) {
            return operationLogRepository.findByUserIdOrderByOperationTimeDesc(userId);
        } else if (module != null) {
            return operationLogRepository.findByModuleOrderByOperationTimeDesc(module);
        } else if (startTime != null && endTime != null) {
            return operationLogRepository.findByOperationTimeBetweenOrderByOperationTimeDesc(startTime, endTime);
        } else {
            // 返回最近的100条记录
            return operationLogRepository.findAll().stream()
                .sorted((a, b) -> b.getOperationTime().compareTo(a.getOperationTime()))
                .limit(100)
                .collect(java.util.stream.Collectors.toList());
        }
    }
    
    @Override
    public Map<String, Object> getUserOperationStatistics(Integer userId, Integer days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startTime = calendar.getTime();
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总操作次数
        Long totalCount = operationLogRepository.countByUserIdAndOperationTimeAfter(userId, startTime);
        statistics.put("totalCount", totalCount);
        
        // 成功操作次数
        Long successCount = operationLogRepository.countSuccessOperationsByUserIdAndOperationTimeAfter(userId, startTime);
        statistics.put("successCount", successCount);
        
        // 失败操作次数
        Long failedCount = operationLogRepository.countFailedOperationsByUserIdAndOperationTimeAfter(userId, startTime);
        statistics.put("failedCount", failedCount);
        
        // 成功率
        if (totalCount > 0) {
            double successRate = (double) successCount / totalCount * 100;
            statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
        } else {
            statistics.put("successRate", 0.0);
        }
        
        // 操作类型统计
        List<Object[]> operationTypeStats = operationLogRepository.getOperationTypeStatisticsByUserId(userId, startTime);
        Map<String, Long> operationTypeMap = new HashMap<>();
        for (Object[] stat : operationTypeStats) {
            operationTypeMap.put((String) stat[0], ((Number) stat[1]).longValue());
        }
        statistics.put("operationTypeStatistics", operationTypeMap);
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getModuleOperationStatistics(String module, Integer days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startTime = calendar.getTime();
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总操作次数
        Long totalCount = operationLogRepository.countByModuleAndOperationTimeAfter(module, startTime);
        statistics.put("totalCount", totalCount);
        
        // 操作类型统计
        List<Object[]> operationTypeStats = operationLogRepository.getOperationTypeStatisticsByModule(module, startTime);
        Map<String, Long> operationTypeMap = new HashMap<>();
        for (Object[] stat : operationTypeStats) {
            operationTypeMap.put((String) stat[0], ((Number) stat[1]).longValue());
        }
        statistics.put("operationTypeStatistics", operationTypeMap);
        
        return statistics;
    }
    
    @Override
    public int cleanExpiredLogs(Integer days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date expireTime = calendar.getTime();
        
        int operationLogCount = operationLogRepository.deleteByOperationTimeBefore(expireTime);
        int dataAccessLogCount = dataAccessLogRepository.deleteByAccessTimeBefore(expireTime);
        
        log.info("清理过期日志完成，操作日志：{}条，数据访问日志：{}条", operationLogCount, dataAccessLogCount);
        return operationLogCount + dataAccessLogCount;
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        try {
            String xForwardedFor = request.getHeader("X-Forwarded-For");
            if (StringUtils.hasText(xForwardedFor)) {
                return xForwardedFor.split(",")[0].trim();
            }
            
            String xRealIp = request.getHeader("X-Real-IP");
            if (StringUtils.hasText(xRealIp)) {
                return xRealIp;
            }
            
            return request.getRemoteAddr();
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    /**
     * 获取用户代理
     */
    private String getUserAgent() {
        try {
            return request.getHeader("User-Agent");
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    /**
     * 获取请求URL
     */
    private String getRequestUrl() {
        try {
            return request.getRequestURL().toString();
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    /**
     * 获取请求方法
     */
    private String getRequestMethod() {
        try {
            return request.getMethod();
        } catch (Exception e) {
            return "unknown";
        }
    }
}