package com.hvisions.rawmaterial.security.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * 用户安全上下文
 * 包含用户的基本信息、角色和权限
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSecurityContext {
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户姓名
     */
    private String realName;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 用户角色列表
     */
    private Set<String> roles;
    
    /**
     * 用户权限列表
     */
    private Set<String> permissions;
    
    /**
     * 是否为管理员
     */
    private Boolean isAdmin;
    
    /**
     * 是否为超级管理员
     */
    private Boolean isSuperAdmin;
    
    /**
     * 检查是否拥有指定角色
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }
    
    /**
     * 检查是否拥有任一指定角色
     */
    public boolean hasAnyRole(String... roles) {
        if (this.roles == null || roles == null) {
            return false;
        }
        for (String role : roles) {
            if (this.roles.contains(role)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否拥有所有指定角色
     */
    public boolean hasAllRoles(String... roles) {
        if (this.roles == null || roles == null) {
            return false;
        }
        for (String role : roles) {
            if (!this.roles.contains(role)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }
    
    /**
     * 检查是否拥有任一指定权限
     */
    public boolean hasAnyPermission(String... permissions) {
        if (this.permissions == null || permissions == null) {
            return false;
        }
        for (String permission : permissions) {
            if (this.permissions.contains(permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否拥有所有指定权限
     */
    public boolean hasAllPermissions(String... permissions) {
        if (this.permissions == null || permissions == null) {
            return false;
        }
        for (String permission : permissions) {
            if (!this.permissions.contains(permission)) {
                return false;
            }
        }
        return true;
    }
}