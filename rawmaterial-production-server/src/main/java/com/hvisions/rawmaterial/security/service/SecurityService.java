package com.hvisions.rawmaterial.security.service;

import com.hvisions.rawmaterial.security.dto.UserSecurityContext;

/**
 * 安全服务接口
 * 提供用户认证、授权和权限检查功能
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface SecurityService {
    
    /**
     * 获取当前用户安全上下文
     */
    UserSecurityContext getCurrentUserContext();
    
    /**
     * 根据用户ID获取用户安全上下文
     */
    UserSecurityContext getUserContext(Integer userId);
    
    /**
     * 检查当前用户是否拥有指定权限
     */
    boolean hasPermission(String permission);
    
    /**
     * 检查当前用户是否拥有任一指定权限
     */
    boolean hasAnyPermission(String... permissions);
    
    /**
     * 检查当前用户是否拥有所有指定权限
     */
    boolean hasAllPermissions(String... permissions);
    
    /**
     * 检查当前用户是否拥有指定角色
     */
    boolean hasRole(String role);
    
    /**
     * 检查当前用户是否拥有任一指定角色
     */
    boolean hasAnyRole(String... roles);
    
    /**
     * 检查当前用户是否拥有所有指定角色
     */
    boolean hasAllRoles(String... roles);
    
    /**
     * 检查指定用户是否拥有指定权限
     */
    boolean hasPermission(Integer userId, String permission);
    
    /**
     * 检查指定用户是否拥有指定角色
     */
    boolean hasRole(Integer userId, String role);
    
    /**
     * 验证权限，如果没有权限则抛出异常
     */
    void validatePermission(String permission);
    
    /**
     * 验证角色，如果没有角色则抛出异常
     */
    void validateRole(String role);
    
    /**
     * 生成操作确认令牌
     */
    String generateOperationToken(String operation, Object data);
    
    /**
     * 验证操作确认令牌
     */
    boolean validateOperationToken(String token, String confirmationCode);
    
    /**
     * 清除操作确认令牌
     */
    void clearOperationToken(String token);
}