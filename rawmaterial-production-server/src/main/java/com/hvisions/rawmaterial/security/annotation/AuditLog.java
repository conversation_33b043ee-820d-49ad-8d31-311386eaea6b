package com.hvisions.rawmaterial.security.annotation;

import java.lang.annotation.*;

/**
 * 操作审计日志注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditLog {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 业务模块
     */
    String module() default "库存差异处理";
    
    /**
     * 操作类型
     */
    String operationType() default "";
    
    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;
    
    /**
     * 是否记录返回结果
     */
    boolean logResult() default false;
    
    /**
     * 是否记录异常信息
     */
    boolean logException() default true;
}