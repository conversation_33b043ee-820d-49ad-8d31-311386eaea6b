package com.hvisions.rawmaterial.security.exception;

/**
 * 安全异常类
 * 用于权限验证失败等安全相关异常
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public class SecurityException extends RuntimeException {
    
    private String errorCode;
    
    public SecurityException(String message) {
        super(message);
        this.errorCode = "SECURITY_ERROR";
    }
    
    public SecurityException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public SecurityException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "SECURITY_ERROR";
    }
    
    public SecurityException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}