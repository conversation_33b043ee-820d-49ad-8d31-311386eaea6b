package com.hvisions.rawmaterial.security.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 操作确认请求DTO
 * 用于敏感操作的二次确认
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "操作确认请求")
public class OperationConfirmationRequest {
    
    @ApiModelProperty(value = "操作令牌", required = true)
    @NotBlank(message = "操作令牌不能为空")
    private String operationToken;
    
    @ApiModelProperty(value = "确认码", required = true)
    @NotBlank(message = "确认码不能为空")
    private String confirmationCode;
    
    @ApiModelProperty(value = "用户密码（可选）")
    private String password;
    
    @ApiModelProperty(value = "确认原因")
    private String confirmationReason;
    
    @ApiModelProperty(value = "是否确认执行", required = true)
    @NotNull(message = "确认标识不能为空")
    private Boolean confirmed;
}