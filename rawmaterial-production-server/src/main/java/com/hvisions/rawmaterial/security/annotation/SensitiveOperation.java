package com.hvisions.rawmaterial.security.annotation;

import java.lang.annotation.*;

/**
 * 敏感操作注解
 * 用于标记需要二次确认的敏感操作
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveOperation {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 操作类型
     */
    OperationType type() default OperationType.MODIFY;
    
    /**
     * 是否需要二次确认
     */
    boolean requireConfirmation() default true;
    
    /**
     * 操作类型枚举
     */
    enum OperationType {
        CREATE("创建"),
        MODIFY("修改"),
        DELETE("删除"),
        PROCESS("处理"),
        SYNC("同步"),
        EXPORT("导出");
        
        private final String description;
        
        OperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}