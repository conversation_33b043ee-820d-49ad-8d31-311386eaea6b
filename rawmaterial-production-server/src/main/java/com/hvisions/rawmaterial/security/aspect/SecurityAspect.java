package com.hvisions.rawmaterial.security.aspect;

import com.hvisions.rawmaterial.security.annotation.RequirePermission;
import com.hvisions.rawmaterial.security.annotation.RequireRole;
import com.hvisions.rawmaterial.security.annotation.SensitiveOperation;
import com.hvisions.rawmaterial.security.annotation.AuditLog;
import com.hvisions.rawmaterial.security.dto.UserSecurityContext;
import com.hvisions.rawmaterial.security.exception.SecurityException;
import com.hvisions.rawmaterial.security.service.AuditLogService;
import com.hvisions.rawmaterial.security.service.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 安全切面
 * 处理权限验证、敏感操作确认和审计日志记录
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Aspect
@Component
public class SecurityAspect {
    
    @Autowired
    private SecurityService securityService;
    
    @Autowired
    private AuditLogService auditLogService;
    
    /**
     * 权限验证切点
     */
    @Pointcut("@annotation(com.hvisions.rawmaterial.security.annotation.RequirePermission) || " +
              "@within(com.hvisions.rawmaterial.security.annotation.RequirePermission)")
    public void requirePermissionPointcut() {}
    
    /**
     * 角色验证切点
     */
    @Pointcut("@annotation(com.hvisions.rawmaterial.security.annotation.RequireRole) || " +
              "@within(com.hvisions.rawmaterial.security.annotation.RequireRole)")
    public void requireRolePointcut() {}
    
    /**
     * 敏感操作切点
     */
    @Pointcut("@annotation(com.hvisions.rawmaterial.security.annotation.SensitiveOperation)")
    public void sensitiveOperationPointcut() {}
    
    /**
     * 审计日志切点
     */
    @Pointcut("@annotation(com.hvisions.rawmaterial.security.annotation.AuditLog)")
    public void auditLogPointcut() {}
    
    /**
     * 权限验证前置通知
     */
    @Before("requirePermissionPointcut()")
    public void checkPermission(JoinPoint joinPoint) {
        RequirePermission annotation = getRequirePermissionAnnotation(joinPoint);
        if (annotation == null) {
            return;
        }
        
        String[] permissions = annotation.value();
        if (permissions.length == 0) {
            return;
        }
        
        UserSecurityContext userContext = securityService.getCurrentUserContext();
        if (userContext == null) {
            throw new SecurityException("AUTHENTICATION_REQUIRED", "用户未认证");
        }
        
        boolean hasPermission;
        if (annotation.requireAll()) {
            hasPermission = userContext.hasAllPermissions(permissions);
        } else {
            hasPermission = userContext.hasAnyPermission(permissions);
        }
        
        if (!hasPermission) {
            String permissionStr = String.join(", ", permissions);
            String message = annotation.requireAll() ? 
                "权限不足，需要所有权限：" + permissionStr :
                "权限不足，需要任一权限：" + permissionStr;
            
            log.warn("权限验证失败，用户：{}，需要权限：{}，操作：{}", 
                userContext.getUsername(), permissionStr, joinPoint.getSignature().getName());
            
            throw new SecurityException("PERMISSION_DENIED", message);
        }
        
        log.debug("权限验证通过，用户：{}，权限：{}，操作：{}", 
            userContext.getUsername(), permissionStr, joinPoint.getSignature().getName());
    }
    
    /**
     * 角色验证前置通知
     */
    @Before("requireRolePointcut()")
    public void checkRole(JoinPoint joinPoint) {
        RequireRole annotation = getRequireRoleAnnotation(joinPoint);
        if (annotation == null) {
            return;
        }
        
        String[] roles = annotation.value();
        if (roles.length == 0) {
            return;
        }
        
        UserSecurityContext userContext = securityService.getCurrentUserContext();
        if (userContext == null) {
            throw new SecurityException("AUTHENTICATION_REQUIRED", "用户未认证");
        }
        
        boolean hasRole;
        if (annotation.requireAll()) {
            hasRole = userContext.hasAllRoles(roles);
        } else {
            hasRole = userContext.hasAnyRole(roles);
        }
        
        if (!hasRole) {
            String roleStr = String.join(", ", roles);
            String message = annotation.requireAll() ? 
                "角色权限不足，需要所有角色：" + roleStr :
                "角色权限不足，需要任一角色：" + roleStr;
            
            log.warn("角色验证失败，用户：{}，需要角色：{}，操作：{}", 
                userContext.getUsername(), roleStr, joinPoint.getSignature().getName());
            
            throw new SecurityException("ROLE_DENIED", message);
        }
        
        log.debug("角色验证通过，用户：{}，角色：{}，操作：{}", 
            userContext.getUsername(), roleStr, joinPoint.getSignature().getName());
    }
    
    /**
     * 敏感操作环绕通知
     */
    @Around("sensitiveOperationPointcut()")
    public Object handleSensitiveOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        SensitiveOperation annotation = joinPoint.getTarget().getClass()
            .getMethod(joinPoint.getSignature().getName(), 
                getParameterTypes(joinPoint))
            .getAnnotation(SensitiveOperation.class);
        
        if (annotation == null) {
            return joinPoint.proceed();
        }
        
        UserSecurityContext userContext = securityService.getCurrentUserContext();
        if (userContext == null) {
            throw new SecurityException("AUTHENTICATION_REQUIRED", "用户未认证");
        }
        
        String operation = annotation.value();
        if (operation.isEmpty()) {
            operation = joinPoint.getSignature().getName();
        }
        
        log.info("执行敏感操作，用户：{}，操作：{}，类型：{}", 
            userContext.getUsername(), operation, annotation.type().getDescription());
        
        // 如果需要二次确认，这里可以添加确认逻辑
        // 实际项目中可能需要检查请求中是否包含确认令牌
        if (annotation.requireConfirmation()) {
            // TODO: 实现二次确认逻辑
            log.debug("敏感操作需要二次确认：{}", operation);
        }
        
        return joinPoint.proceed();
    }
    
    /**
     * 审计日志环绕通知
     */
    @Around("auditLogPointcut()")
    public Object handleAuditLog(ProceedingJoinPoint joinPoint) throws Throwable {
        AuditLog annotation = joinPoint.getTarget().getClass()
            .getMethod(joinPoint.getSignature().getName(), 
                getParameterTypes(joinPoint))
            .getAnnotation(AuditLog.class);
        
        if (annotation == null) {
            return joinPoint.proceed();
        }
        
        UserSecurityContext userContext = securityService.getCurrentUserContext();
        String operation = annotation.value();
        if (operation.isEmpty()) {
            operation = joinPoint.getSignature().getName();
        }
        
        Object[] args = joinPoint.getArgs();
        Object params = annotation.logParams() ? args : null;
        Object result = null;
        String errorMessage = null;
        long startTime = System.currentTimeMillis();
        
        try {
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            errorMessage = e.getMessage();
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            
            Integer userId = userContext != null ? userContext.getUserId() : 0;
            String username = userContext != null ? userContext.getUsername() : "anonymous";
            
            // 记录操作日志
            auditLogService.logOperation(
                annotation.module(),
                operation,
                annotation.operationType(),
                userId,
                username,
                params,
                annotation.logResult() ? result : null,
                annotation.logException() ? errorMessage : null
            );
            
            log.debug("记录审计日志，用户：{}，操作：{}，模块：{}，执行时间：{}ms", 
                username, operation, annotation.module(), executionTime);
        }
    }
    
    /**
     * 获取RequirePermission注解
     */
    private RequirePermission getRequirePermissionAnnotation(JoinPoint joinPoint) {
        try {
            // 先尝试从方法获取
            RequirePermission methodAnnotation = joinPoint.getTarget().getClass()
                .getMethod(joinPoint.getSignature().getName(), 
                    getParameterTypes(joinPoint))
                .getAnnotation(RequirePermission.class);
            
            if (methodAnnotation != null) {
                return methodAnnotation;
            }
            
            // 再尝试从类获取
            return joinPoint.getTarget().getClass().getAnnotation(RequirePermission.class);
        } catch (Exception e) {
            log.warn("获取RequirePermission注解失败", e);
            return null;
        }
    }
    
    /**
     * 获取RequireRole注解
     */
    private RequireRole getRequireRoleAnnotation(JoinPoint joinPoint) {
        try {
            // 先尝试从方法获取
            RequireRole methodAnnotation = joinPoint.getTarget().getClass()
                .getMethod(joinPoint.getSignature().getName(), 
                    getParameterTypes(joinPoint))
                .getAnnotation(RequireRole.class);
            
            if (methodAnnotation != null) {
                return methodAnnotation;
            }
            
            // 再尝试从类获取
            return joinPoint.getTarget().getClass().getAnnotation(RequireRole.class);
        } catch (Exception e) {
            log.warn("获取RequireRole注解失败", e);
            return null;
        }
    }
    
    /**
     * 获取方法参数类型
     */
    private Class<?>[] getParameterTypes(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        Class<?>[] parameterTypes = new Class<?>[args.length];
        for (int i = 0; i < args.length; i++) {
            parameterTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
        }
        return parameterTypes;
    }
}