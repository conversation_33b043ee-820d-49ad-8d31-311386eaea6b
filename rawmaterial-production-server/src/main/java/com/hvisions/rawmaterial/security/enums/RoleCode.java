package com.hvisions.rawmaterial.security.enums;

/**
 * 角色代码枚举
 * 定义库存差异处理模块的所有角色
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public enum RoleCode {
    
    // 基础角色
    INVENTORY_DIFFERENCE_VIEWER("inventory_difference_viewer", "库存差异查看员"),
    INVENTORY_DIFFERENCE_OPERATOR("inventory_difference_operator", "库存差异操作员"),
    INVENTORY_DIFFERENCE_PROCESSOR("inventory_difference_processor", "库存差异处理员"),
    
    // 高级角色
    INVENTORY_DIFFERENCE_SUPERVISOR("inventory_difference_supervisor", "库存差异主管"),
    INVENTORY_DIFFERENCE_MANAGER("inventory_difference_manager", "库存差异经理"),
    INVENTORY_DIFFERENCE_ADMIN("inventory_difference_admin", "库存差异管理员"),
    
    // 系统角色
    SYSTEM_ADMIN("system_admin", "系统管理员"),
    SUPER_ADMIN("super_admin", "超级管理员");
    
    private final String code;
    private final String description;
    
    RoleCode(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取角色枚举
     */
    public static RoleCode fromCode(String code) {
        for (RoleCode role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }
}