package com.hvisions.rawmaterial.security.service.impl;

import com.hvisions.auth.client.BaseUserClient;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.common.consts.CookieConst;
import com.hvisions.common.consts.RedisConst;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.security.dto.UserSecurityContext;
import com.hvisions.rawmaterial.security.exception.SecurityException;
import com.hvisions.rawmaterial.security.service.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 安全服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Service
public class SecurityServiceImpl implements SecurityService {
    
    @Autowired
    private HttpServletRequest request;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private BaseUserClient baseUserClient;
    
    private static final String OPERATION_TOKEN_PREFIX = "operation_token:";
    private static final int OPERATION_TOKEN_EXPIRE_MINUTES = 5;
    
    @Override
    public UserSecurityContext getCurrentUserContext() {
        Integer userId = getCurrentUserId();
        if (userId == null || userId == 0) {
            return null;
        }
        return getUserContext(userId);
    }
    
    @Override
    public UserSecurityContext getUserContext(Integer userId) {
        if (userId == null || userId == 0) {
            return null;
        }
        
        try {
            // 从用户服务获取用户基本信息
            ResultVO<UserBaseDTO> userResult = baseUserClient.getUserByUserId(userId);
            if (!userResult.isSuccess() || userResult.getData() == null) {
                log.warn("获取用户信息失败，用户ID：{}", userId);
                return null;
            }
            
            UserBaseDTO userBase = userResult.getData();
            
            // 构建用户安全上下文
            UserSecurityContext context = UserSecurityContext.builder()
                .userId(userId)
                .username(userBase.getUserName())
                .realName(userBase.getRealName())
                .departmentId(userBase.getDepartmentId())
                .departmentName(userBase.getDepartmentName())
                .roles(getUserRoles(userId))
                .permissions(getUserPermissions(userId))
                .isAdmin(isAdmin(userId))
                .isSuperAdmin(isSuperAdmin(userId))
                .build();
            
            return context;
            
        } catch (Exception e) {
            log.error("获取用户安全上下文失败，用户ID：{}", userId, e);
            return null;
        }
    }
    
    @Override
    public boolean hasPermission(String permission) {
        UserSecurityContext context = getCurrentUserContext();
        return context != null && context.hasPermission(permission);
    }
    
    @Override
    public boolean hasAnyPermission(String... permissions) {
        UserSecurityContext context = getCurrentUserContext();
        return context != null && context.hasAnyPermission(permissions);
    }
    
    @Override
    public boolean hasAllPermissions(String... permissions) {
        UserSecurityContext context = getCurrentUserContext();
        return context != null && context.hasAllPermissions(permissions);
    }
    
    @Override
    public boolean hasRole(String role) {
        UserSecurityContext context = getCurrentUserContext();
        return context != null && context.hasRole(role);
    }
    
    @Override
    public boolean hasAnyRole(String... roles) {
        UserSecurityContext context = getCurrentUserContext();
        return context != null && context.hasAnyRole(roles);
    }
    
    @Override
    public boolean hasAllRoles(String... roles) {
        UserSecurityContext context = getCurrentUserContext();
        return context != null && context.hasAllRoles(roles);
    }
    
    @Override
    public boolean hasPermission(Integer userId, String permission) {
        UserSecurityContext context = getUserContext(userId);
        return context != null && context.hasPermission(permission);
    }
    
    @Override
    public boolean hasRole(Integer userId, String role) {
        UserSecurityContext context = getUserContext(userId);
        return context != null && context.hasRole(role);
    }
    
    @Override
    public void validatePermission(String permission) {
        if (!hasPermission(permission)) {
            throw new SecurityException("权限不足，需要权限：" + permission);
        }
    }
    
    @Override
    public void validateRole(String role) {
        if (!hasRole(role)) {
            throw new SecurityException("角色权限不足，需要角色：" + role);
        }
    }
    
    @Override
    public String generateOperationToken(String operation, Object data) {
        String token = UUID.randomUUID().toString().replace("-", "");
        String confirmationCode = generateConfirmationCode();
        
        Map<String, Object> tokenData = new HashMap<>();
        tokenData.put("operation", operation);
        tokenData.put("data", data);
        tokenData.put("confirmationCode", confirmationCode);
        tokenData.put("userId", getCurrentUserId());
        tokenData.put("createTime", System.currentTimeMillis());
        
        // 存储到Redis，设置过期时间
        String key = OPERATION_TOKEN_PREFIX + token;
        stringRedisTemplate.opsForValue().set(key, 
            com.alibaba.fastjson.JSON.toJSONString(tokenData), 
            OPERATION_TOKEN_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("生成操作确认令牌，操作：{}，令牌：{}，确认码：{}", operation, token, confirmationCode);
        return token;
    }
    
    @Override
    public boolean validateOperationToken(String token, String confirmationCode) {
        if (!StringUtils.hasText(token) || !StringUtils.hasText(confirmationCode)) {
            return false;
        }
        
        try {
            String key = OPERATION_TOKEN_PREFIX + token;
            String tokenDataStr = stringRedisTemplate.opsForValue().get(key);
            
            if (!StringUtils.hasText(tokenDataStr)) {
                log.warn("操作令牌不存在或已过期：{}", token);
                return false;
            }
            
            Map<String, Object> tokenData = com.alibaba.fastjson.JSON.parseObject(tokenDataStr, Map.class);
            String storedCode = (String) tokenData.get("confirmationCode");
            Integer storedUserId = (Integer) tokenData.get("userId");
            Integer currentUserId = getCurrentUserId();
            
            // 验证确认码和用户ID
            if (!confirmationCode.equals(storedCode)) {
                log.warn("操作确认码不匹配，令牌：{}，期望：{}，实际：{}", token, storedCode, confirmationCode);
                return false;
            }
            
            if (!Objects.equals(storedUserId, currentUserId)) {
                log.warn("操作用户不匹配，令牌：{}，期望：{}，实际：{}", token, storedUserId, currentUserId);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证操作令牌失败：{}", token, e);
            return false;
        }
    }
    
    @Override
    public void clearOperationToken(String token) {
        if (StringUtils.hasText(token)) {
            String key = OPERATION_TOKEN_PREFIX + token;
            stringRedisTemplate.delete(key);
            log.info("清除操作令牌：{}", token);
        }
    }
    
    /**
     * 获取当前用户ID
     */
    private Integer getCurrentUserId() {
        try {
            // 首先尝试从Header获取
            String userIdHeader = request.getHeader("User-Id");
            if (StringUtils.hasText(userIdHeader)) {
                return Integer.parseInt(userIdHeader);
            }
            
            // 然后尝试从Token获取
            String token = request.getHeader(CookieConst.AUTH_TOKEN);
            if (StringUtils.hasText(token)) {
                String userIdStr = stringRedisTemplate.opsForValue()
                    .get(String.format(RedisConst.AUTH_REDIS_PREFIX, token));
                if (StringUtils.hasText(userIdStr)) {
                    return Integer.parseInt(userIdStr);
                }
            }
            
            return 0; // 默认用户ID
            
        } catch (Exception e) {
            log.warn("获取当前用户ID失败", e);
            return 0;
        }
    }
    
    /**
     * 获取用户角色
     */
    private Set<String> getUserRoles(Integer userId) {
        // TODO: 实际项目中应该从权限服务获取用户角色
        // 这里返回模拟数据
        Set<String> roles = new HashSet<>();
        
        if (userId == 1) {
            roles.add("inventory_difference_admin");
            roles.add("inventory_difference_manager");
        } else if (userId == 2) {
            roles.add("inventory_difference_processor");
            roles.add("inventory_difference_operator");
        } else {
            roles.add("inventory_difference_viewer");
        }
        
        return roles;
    }
    
    /**
     * 获取用户权限
     */
    private Set<String> getUserPermissions(Integer userId) {
        // TODO: 实际项目中应该从权限服务获取用户权限
        // 这里返回模拟数据
        Set<String> permissions = new HashSet<>();
        
        if (userId == 1) {
            // 管理员拥有所有权限
            permissions.add("inventory_difference:view");
            permissions.add("inventory_difference:query");
            permissions.add("inventory_difference:statistics");
            permissions.add("inventory_difference:process");
            permissions.add("inventory_difference:batch_process");
            permissions.add("inventory_difference:generate");
            permissions.add("inventory_difference:recalculate");
            permissions.add("inventory_difference:sap_sync");
            permissions.add("inventory_difference:sap_manual_sync");
            permissions.add("inventory_difference:delete");
            permissions.add("inventory_difference:export");
            permissions.add("inventory_difference:async_task");
            permissions.add("inventory_difference:task_cancel");
            permissions.add("inventory_difference:admin");
        } else if (userId == 2) {
            // 处理员权限
            permissions.add("inventory_difference:view");
            permissions.add("inventory_difference:query");
            permissions.add("inventory_difference:statistics");
            permissions.add("inventory_difference:process");
            permissions.add("inventory_difference:batch_process");
            permissions.add("inventory_difference:recalculate");
            permissions.add("inventory_difference:export");
        } else {
            // 查看员权限
            permissions.add("inventory_difference:view");
            permissions.add("inventory_difference:query");
            permissions.add("inventory_difference:statistics");
        }
        
        return permissions;
    }
    
    /**
     * 检查是否为管理员
     */
    private Boolean isAdmin(Integer userId) {
        Set<String> roles = getUserRoles(userId);
        return roles.contains("inventory_difference_admin") || 
               roles.contains("system_admin") || 
               roles.contains("super_admin");
    }
    
    /**
     * 检查是否为超级管理员
     */
    private Boolean isSuperAdmin(Integer userId) {
        Set<String> roles = getUserRoles(userId);
        return roles.contains("super_admin");
    }
    
    /**
     * 生成确认码
     */
    private String generateConfirmationCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }
}