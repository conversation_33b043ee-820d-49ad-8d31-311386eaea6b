package com.hvisions.rawmaterial.security.enums;

/**
 * 权限代码枚举
 * 定义库存差异处理模块的所有权限
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public enum PermissionCode {
    
    // 查询权限
    INVENTORY_DIFFERENCE_VIEW("inventory_difference:view", "查看库存差异"),
    INVENTORY_DIFFERENCE_QUERY("inventory_difference:query", "查询库存差异"),
    INVENTORY_DIFFERENCE_STATISTICS("inventory_difference:statistics", "查看统计信息"),
    
    // 操作权限
    INVENTORY_DIFFERENCE_PROCESS("inventory_difference:process", "处理库存差异"),
    INVENTORY_DIFFERENCE_BATCH_PROCESS("inventory_difference:batch_process", "批量处理差异"),
    INVENTORY_DIFFERENCE_GENERATE("inventory_difference:generate", "生成差异记录"),
    INVENTORY_DIFFERENCE_RECALCULATE("inventory_difference:recalculate", "重新计算差异"),
    
    // SAP同步权限
    INVENTORY_DIFFERENCE_SAP_SYNC("inventory_difference:sap_sync", "SAP库存同步"),
    INVENTORY_DIFFERENCE_SAP_MANUAL_SYNC("inventory_difference:sap_manual_sync", "手动SAP同步"),
    
    // 数据管理权限
    INVENTORY_DIFFERENCE_DELETE("inventory_difference:delete", "删除差异记录"),
    INVENTORY_DIFFERENCE_EXPORT("inventory_difference:export", "导出差异记录"),
    
    // 异步任务权限
    INVENTORY_DIFFERENCE_ASYNC_TASK("inventory_difference:async_task", "异步任务管理"),
    INVENTORY_DIFFERENCE_TASK_CANCEL("inventory_difference:task_cancel", "取消异步任务"),
    
    // 管理员权限
    INVENTORY_DIFFERENCE_ADMIN("inventory_difference:admin", "库存差异管理员");
    
    private final String code;
    private final String description;
    
    PermissionCode(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取权限枚举
     */
    public static PermissionCode fromCode(String code) {
        for (PermissionCode permission : values()) {
            if (permission.getCode().equals(code)) {
                return permission;
            }
        }
        return null;
    }
}