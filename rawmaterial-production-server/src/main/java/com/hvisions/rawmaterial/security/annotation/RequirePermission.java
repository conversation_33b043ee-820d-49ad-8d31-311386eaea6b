package com.hvisions.rawmaterial.security.annotation;

import java.lang.annotation.*;

/**
 * 权限控制注解
 * 用于标记需要特定权限才能访问的方法
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {
    
    /**
     * 所需权限代码
     */
    String[] value() default {};
    
    /**
     * 权限描述
     */
    String description() default "";
    
    /**
     * 是否需要所有权限（true：需要所有权限，false：需要任一权限）
     */
    boolean requireAll() default false;
}