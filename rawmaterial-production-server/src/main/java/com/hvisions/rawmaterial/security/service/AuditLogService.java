package com.hvisions.rawmaterial.security.service;

import com.hvisions.rawmaterial.entity.log.TMpdOperationLog;

import java.util.List;
import java.util.Map;

/**
 * 审计日志服务接口
 * 提供操作日志记录和查询功能
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface AuditLogService {
    
    /**
     * 记录操作日志
     */
    void logOperation(String module, String operation, String operationType, 
                     Integer userId, String username, Object params, 
                     Object result, String errorMessage);
    
    /**
     * 记录成功操作
     */
    void logSuccess(String module, String operation, String operationType, 
                   Integer userId, String username, Object params, Object result);
    
    /**
     * 记录失败操作
     */
    void logFailure(String module, String operation, String operationType, 
                   Integer userId, String username, Object params, String errorMessage);
    
    /**
     * 记录数据访问
     */
    void logDataAccess(String tableName, String operation, Integer userId, 
                      String username, String dataId, Object oldData, Object newData);
    
    /**
     * 查询操作日志
     */
    List<TMpdOperationLog> queryOperationLogs(Map<String, Object> params);
    
    /**
     * 获取用户操作统计
     */
    Map<String, Object> getUserOperationStatistics(Integer userId, Integer days);
    
    /**
     * 获取模块操作统计
     */
    Map<String, Object> getModuleOperationStatistics(String module, Integer days);
    
    /**
     * 清理过期日志
     */
    int cleanExpiredLogs(Integer days);
}