package com.hvisions.rawmaterial.security.annotation;

import java.lang.annotation.*;

/**
 * 角色控制注解
 * 用于标记需要特定角色才能访问的方法
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireRole {
    
    /**
     * 所需角色代码
     */
    String[] value() default {};
    
    /**
     * 角色描述
     */
    String description() default "";
    
    /**
     * 是否需要所有角色（true：需要所有角色，false：需要任一角色）
     */
    boolean requireAll() default false;
}