package com.hvisions.rawmaterial.configuration;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis配置 - 支持缓存和性能优化
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableCaching
public class RedisConfig {

	@Autowired
	private RedisClusterProperties clusterProperties;

	@Bean
	@Primary
	public LettuceConnectionFactory redisConnectionFactory() {
		// 1. 动态构建集群配置
		RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(clusterProperties.getNodes());

		// 设置密码（需要转换为 RedisPassword 类型）
		if (clusterProperties.getPassword() != null && !clusterProperties.getPassword().isEmpty()) {
			clusterConfig.setPassword(RedisPassword.of(clusterProperties.getPassword()));
		}

		// 2. 拓扑刷新配置（保持原有优化）
		ClusterTopologyRefreshOptions refreshOptions = ClusterTopologyRefreshOptions.builder()
				.enablePeriodicRefresh(Duration.ofSeconds(3))
				.enableAllAdaptiveRefreshTriggers()
				.adaptiveRefreshTriggersTimeout(Duration.ofSeconds(10))
				.build();

		// 3. 客户端配置
		LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
				.commandTimeout(Duration.ofSeconds(2))
				.clientOptions(ClusterClientOptions.builder()
						.topologyRefreshOptions(refreshOptions)
						.validateClusterNodeMembership(false)
						.build())
				.build();

		return new LettuceConnectionFactory(clusterConfig, clientConfig);
	}

	/**
	 * RedisTemplate配置 - 用于缓存操作
	 */
	@Bean
	@Primary
	public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory connectionFactory) {
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(connectionFactory);

		// 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
		jackson2JsonRedisSerializer.setObjectMapper(objectMapper);

		// 使用StringRedisSerializer来序列化和反序列化redis的key值
		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

		// key采用String的序列化方式
		template.setKeySerializer(stringRedisSerializer);
		// hash的key也采用String的序列化方式
		template.setHashKeySerializer(stringRedisSerializer);
		// value序列化方式采用jackson
		template.setValueSerializer(jackson2JsonRedisSerializer);
		// hash的value序列化方式采用jackson
		template.setHashValueSerializer(jackson2JsonRedisSerializer);
		template.afterPropertiesSet();

		return template;
	}

	/**
	 * 缓存管理器配置 - 支持多级缓存策略
	 */
	@Bean
	@Primary
	public CacheManager cacheManager(LettuceConnectionFactory connectionFactory) {
		// 默认缓存配置
		RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
				.entryTtl(Duration.ofMinutes(30)) // 默认30分钟过期
				.disableCachingNullValues(); // 不缓存null值

		// 不同业务场景的缓存配置
		Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
		
		// 库存差异统计数据缓存 - 5分钟过期
		cacheConfigurations.put("inventory-difference-statistics", 
			defaultConfig.entryTtl(Duration.ofMinutes(5)));
		
		// SAP库存数据缓存 - 10分钟过期
		cacheConfigurations.put("sap-stock-data", 
			defaultConfig.entryTtl(Duration.ofMinutes(10)));
		
		// MES数据缓存 - 3分钟过期
		cacheConfigurations.put("mes-data", 
			defaultConfig.entryTtl(Duration.ofMinutes(3)));
		
		// 查询结果缓存 - 15分钟过期
		cacheConfigurations.put("query-results", 
			defaultConfig.entryTtl(Duration.ofMinutes(15)));
		
		// 热点数据缓存 - 1小时过期
		cacheConfigurations.put("hot-data", 
			defaultConfig.entryTtl(Duration.ofHours(1)));

		return RedisCacheManager.builder(connectionFactory)
				.cacheDefaults(defaultConfig)
				.withInitialCacheConfigurations(cacheConfigurations)
				.build();
	}
}