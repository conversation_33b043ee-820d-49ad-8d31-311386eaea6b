package com.hvisions.rawmaterial.configuration;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 库存差异处理消息队列配置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
public class InventoryDifferenceRabbitConfig {

    // 交换机名称
    public static final String INVENTORY_DIFFERENCE_EXCHANGE = "inventory.difference.exchange";
    
    // 队列名称
    public static final String SAP_SYNC_QUEUE = "inventory.difference.sap.sync";
    public static final String BATCH_GENERATE_QUEUE = "inventory.difference.batch.generate";
    public static final String TASK_STATUS_QUEUE = "inventory.difference.task.status";
    
    // 死信队列
    public static final String SAP_SYNC_DLQ = "inventory.difference.sap.sync.dlq";
    public static final String BATCH_GENERATE_DLQ = "inventory.difference.batch.generate.dlq";
    
    // 路由键
    public static final String SAP_SYNC_ROUTING_KEY = "inventory.difference.sap.sync";
    public static final String BATCH_GENERATE_ROUTING_KEY = "inventory.difference.batch.generate";
    public static final String TASK_STATUS_ROUTING_KEY = "inventory.difference.task.status";
    
    // 死信路由键
    public static final String SAP_SYNC_DLQ_ROUTING_KEY = "inventory.difference.sap.sync.dlq";
    public static final String BATCH_GENERATE_DLQ_ROUTING_KEY = "inventory.difference.batch.generate.dlq";

    /**
     * 主交换机
     */
    @Bean
    public TopicExchange inventoryDifferenceExchange() {
        return new TopicExchange(INVENTORY_DIFFERENCE_EXCHANGE, true, false);
    }

    /**
     * SAP同步队列
     */
    @Bean
    public Queue sapSyncQueue() {
        return QueueBuilder.durable(SAP_SYNC_QUEUE)
                .withArgument("x-dead-letter-exchange", INVENTORY_DIFFERENCE_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", SAP_SYNC_DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 300000) // 5分钟TTL
                .build();
    }

    /**
     * 批量生成队列
     */
    @Bean
    public Queue batchGenerateQueue() {
        return QueueBuilder.durable(BATCH_GENERATE_QUEUE)
                .withArgument("x-dead-letter-exchange", INVENTORY_DIFFERENCE_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", BATCH_GENERATE_DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 600000) // 10分钟TTL
                .build();
    }

    /**
     * 任务状态队列
     */
    @Bean
    public Queue taskStatusQueue() {
        return QueueBuilder.durable(TASK_STATUS_QUEUE).build();
    }

    /**
     * SAP同步死信队列
     */
    @Bean
    public Queue sapSyncDeadLetterQueue() {
        return QueueBuilder.durable(SAP_SYNC_DLQ).build();
    }

    /**
     * 批量生成死信队列
     */
    @Bean
    public Queue batchGenerateDeadLetterQueue() {
        return QueueBuilder.durable(BATCH_GENERATE_DLQ).build();
    }

    /**
     * SAP同步队列绑定
     */
    @Bean
    public Binding sapSyncBinding() {
        return BindingBuilder.bind(sapSyncQueue())
                .to(inventoryDifferenceExchange())
                .with(SAP_SYNC_ROUTING_KEY);
    }

    /**
     * 批量生成队列绑定
     */
    @Bean
    public Binding batchGenerateBinding() {
        return BindingBuilder.bind(batchGenerateQueue())
                .to(inventoryDifferenceExchange())
                .with(BATCH_GENERATE_ROUTING_KEY);
    }

    /**
     * 任务状态队列绑定
     */
    @Bean
    public Binding taskStatusBinding() {
        return BindingBuilder.bind(taskStatusQueue())
                .to(inventoryDifferenceExchange())
                .with(TASK_STATUS_ROUTING_KEY);
    }

    /**
     * SAP同步死信队列绑定
     */
    @Bean
    public Binding sapSyncDlqBinding() {
        return BindingBuilder.bind(sapSyncDeadLetterQueue())
                .to(inventoryDifferenceExchange())
                .with(SAP_SYNC_DLQ_ROUTING_KEY);
    }

    /**
     * 批量生成死信队列绑定
     */
    @Bean
    public Binding batchGenerateDlqBinding() {
        return BindingBuilder.bind(batchGenerateDeadLetterQueue())
                .to(inventoryDifferenceExchange())
                .with(BATCH_GENERATE_DLQ_ROUTING_KEY);
    }

    /**
     * JSON消息转换器
     */
    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * RabbitTemplate配置
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        template.setMandatory(true);
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                // 消息发送失败处理
                System.err.println("Message send failed: " + cause);
            }
        });
        template.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            // 消息路由失败处理
            System.err.println("Message returned: " + replyText);
        });
        return template;
    }

    /**
     * 监听器容器工厂配置
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        factory.setConcurrentConsumers(3);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(5);
        factory.setDefaultRequeueRejected(false);
        return factory;
    }
}