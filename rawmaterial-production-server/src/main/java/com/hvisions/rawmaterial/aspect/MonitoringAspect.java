package com.hvisions.rawmaterial.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.service.monitoring.BusinessLogService;
import com.hvisions.rawmaterial.service.monitoring.PerformanceMetricsService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 监控切面
 * 自动记录方法执行性能和业务操作日志
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Aspect
@Component
public class MonitoringAspect {
    
    @Autowired
    private PerformanceMetricsService performanceMetricsService;
    
    @Autowired
    private BusinessLogService businessLogService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 监控库存差异处理服务的方法执行
     */
    @Around("execution(* com.hvisions.rawmaterial.service.difference..*(..))")
    public Object monitorDifferenceService(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "DIFFERENCE_SERVICE");
    }
    
    /**
     * 监控SAP集成服务的方法执行
     */
    @Around("execution(* com.hvisions.rawmaterial.service.impl.SapIntegrationServiceImpl.*(..))")
    public Object monitorSapIntegrationService(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "SAP_INTEGRATION");
    }
    
    /**
     * 监控MES数据服务的方法执行
     */
    @Around("execution(* com.hvisions.rawmaterial.service.impl.MesDataServiceImpl.*(..))")
    public Object monitorMesDataService(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "MES_DATA_SERVICE");
    }
    
    /**
     * 监控控制器层的方法执行
     */
    @Around("execution(* com.hvisions.rawmaterial.controller.difference..*(..))")
    public Object monitorController(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "CONTROLLER");
    }
    
    /**
     * 监控数据访问层的方法执行
     */
    @Around("execution(* com.hvisions.rawmaterial.dao..*(..))")
    public Object monitorRepository(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "REPOSITORY");
    }
    
    private Object monitorMethodExecution(ProceedingJoinPoint joinPoint, String serviceType) throws Throwable {
        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
        long startTime = System.currentTimeMillis();
        boolean success = true;
        Object result = null;
        String parameters = "";
        
        try {
            // 记录方法参数
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                try {
                    parameters = objectMapper.writeValueAsString(args);
                    if (parameters.length() > 1000) {
                        parameters = parameters.substring(0, 1000) + "...(truncated)";
                    }
                } catch (JsonProcessingException e) {
                    parameters = "参数序列化失败: " + e.getMessage();
                }
            }
            
            // 执行方法
            result = joinPoint.proceed();
            
            return result;
            
        } catch (Throwable throwable) {
            success = false;
            
            // 记录异常操作日志
            try {
                businessLogService.logErrorOperation(
                    methodName, 
                    throwable.getMessage(), 
                    (Exception) throwable, 
                    getCurrentUserId(), 
                    getCurrentUserName()
                );
            } catch (Exception e) {
                log.error("记录异常操作日志失败", e);
            }
            
            throw throwable;
            
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录性能指标
            try {
                performanceMetricsService.recordMethodPerformance(methodName, executionTime, success, parameters);
            } catch (Exception e) {
                log.error("记录方法性能指标失败", e);
            }
            
            // 记录慢方法日志
            if (executionTime > 3000) {
                log.warn("慢方法执行: 方法[{}], 执行时间[{}ms], 成功[{}], 参数[{}]", 
                    methodName, executionTime, success, parameters);
            }
        }
    }
    
    private Integer getCurrentUserId() {
        // TODO: 从安全上下文获取当前用户ID
        return 1;
    }
    
    private String getCurrentUserName() {
        // TODO: 从安全上下文获取当前用户名
        return "system";
    }
}