package com.hvisions.rawmaterial.controller;


import com.hvisions.rawmaterial.dto.production.bran.inspection.parameter.BranInspectionParameterDTO;
import com.hvisions.rawmaterial.service.BranInspectionParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 蒸糠质量巡检参数 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@RestController
@RequestMapping("/bran/inspection/parameter")
@Api(tags = "蒸糠质量巡检参数配置")
public class BranInspectionParameterController {

    @Resource
    private BranInspectionParameterService branInspectionParameterService;


    @ApiOperation(value = "获取蒸糠质量巡检参数")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public BranInspectionParameterDTO getBranInspectionParameter() {
        return branInspectionParameterService.getBranInspectionParameter();
    }

    @ApiOperation(value = "新增蒸糠质量巡检参数")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Boolean addBranInspectionParameter(@RequestBody BranInspectionParameterDTO branInspectionParameterDTO) {
        return branInspectionParameterService.addBranInspectionParameter(branInspectionParameterDTO);
    }

}

