# 原辅料库存盘点差异处理 API 文档

## 概述

本文档描述了原辅料库存盘点差异处理功能的所有REST API接口。这些接口提供了完整的库存差异管理功能，包括查询、处理、统计、导出等操作。

## 基础信息

- **Base URL**: `/api/rawmaterial/material-difference`
- **Content-Type**: `application/json`
- **Authentication**: 通过HTTP Header中的`User-Id`传递用户信息

## API 接口列表

### 1. 查询相关接口

#### 1.1 分页查询库存差异处理记录

**接口地址**: `POST /page`

**接口描述**: 支持多种筛选条件的分页查询差异处理记录

**请求参数**:
```json
{
  "materialCode": "MAT001",           // 物料编码（可选）
  "materialName": "测试物料",          // 物料名称（可选）
  "erpWarehouseCode": "WH001",       // ERP仓库编码（可选）
  "department": "原辅料管理部",        // 部门（可选）
  "status": 1,                       // 状态：1-待处理，2-已处理（可选）
  "startDate": "2024-01-01",         // 开始日期（可选）
  "endDate": "2024-12-31",           // 结束日期（可选）
  "pageNum": 1,                      // 页码（必填）
  "pageSize": 10                     // 页面大小（必填）
}
```

**响应示例**:
```json
{
  "content": [
    {
      "id": 1,
      "materialCode": "MAT001",
      "materialName": "测试物料",
      "erpWarehouseCode": "WH001",
      "erpWarehouseName": "测试仓库",
      "department": "原辅料管理部",
      "mesCurrentStock": 1000.000,
      "mesInitialStock": 800.000,
      "weighbridgeReceiptQuantity": 500.000,
      "solidWasteQuantity": 50.000,
      "issuedQuantity": 300.000,
      "unit": "吨",
      "sapStock": 950.000,
      "differenceQuantity": 50.000,
      "status": 1,
      "statusDesc": "待处理",
      "createTime": "2024-07-28T10:00:00"
    }
  ],
  "totalElements": 100,
  "totalPages": 10,
  "size": 10,
  "number": 0
}
```

#### 1.2 根据ID查询差异处理记录详情

**接口地址**: `GET /{id}`

**接口描述**: 获取指定ID的差异处理记录完整信息

**路径参数**:
- `id`: 差异处理记录ID（必填，大于0的整数）

**响应示例**:
```json
{
  "id": 1,
  "materialCode": "MAT001",
  "materialName": "测试物料",
  "erpWarehouseCode": "WH001",
  "erpWarehouseName": "测试仓库",
  "department": "原辅料管理部",
  "mesCurrentStock": 1000.000,
  "mesInitialStock": 800.000,
  "weighbridgeReceiptQuantity": 500.000,
  "solidWasteQuantity": 50.000,
  "issuedQuantity": 300.000,
  "unit": "吨",
  "sapStock": 950.000,
  "differenceQuantity": 50.000,
  "statisticsStartDate": "2024-07-01T00:00:00",
  "statisticsEndDate": "2024-07-28T23:59:59",
  "status": 1,
  "statusDesc": "待处理",
  "processorId": null,
  "processorName": null,
  "processTime": null,
  "processRemark": null,
  "sapSyncTime": "2024-07-28T09:00:00",
  "createTime": "2024-07-28T10:00:00",
  "detailList": []
}
```

#### 1.3 查询待处理的差异记录

**接口地址**: `GET /pending`

**接口描述**: 获取所有状态为待处理的差异记录

**响应示例**:
```json
[
  {
    "id": 1,
    "materialCode": "MAT001",
    "materialName": "测试物料",
    "differenceQuantity": 50.000,
    "status": 1,
    "statusDesc": "待处理"
  }
]
```

#### 1.4 查询历史差异记录

**接口地址**: `POST /history`

**接口描述**: 分页查询历史差异处理记录

**请求参数**: 与分页查询接口相同

### 2. 业务操作接口

#### 2.1 生成差异处理记录

**接口地址**: `POST /generate`

**接口描述**: 系统自动计算MES与SAP库存差异并生成处理记录

**响应示例**:
```json
{
  "success": true,
  "message": "差异记录生成成功",
  "generatedCount": 5
}
```

#### 2.2 处理库存差异

**接口地址**: `POST /process`

**接口描述**: 处理指定的库存差异记录，更新状态并创建新的待处理记录

**请求头**:
- `User-Id`: 用户ID（必填）

**请求参数**:
```json
{
  "id": 1,                           // 差异记录ID（必填）
  "statisticsEndDate": "2024-07-28T23:59:59", // 统计结束日期（必填）
  "processRemark": "处理备注",        // 处理备注（可选）
  "detailList": [                    // 处理明细列表（可选）
    {
      "materialCode": "MAT001",
      "materialName": "测试物料",
      "unit": "吨",
      "differenceQuantity": 50.000,
      "processQuantity": 50.000,
      "processType": 1,              // 处理类型：1-盘盈，2-盘亏
      "processRemark": "明细处理备注"
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "差异处理成功"
}
```

#### 2.3 批量处理差异

**接口地址**: `POST /batch-process`

**接口描述**: 批量处理多个差异记录

**请求头**:
- `User-Id`: 用户ID（必填）

**请求参数**:
```json
{
  "differenceIds": [1, 2, 3],        // 差异记录ID列表（必填）
  "statisticsEndDate": "2024-07-28T23:59:59", // 统计结束日期（必填）
  "processRemark": "批量处理",        // 批量处理备注（可选）
  "batchDetails": [                  // 批量处理明细（必填）
    {
      "differenceId": 1,
      "processRemark": "批量处理明细1",
      "detailList": []
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "批量处理完成",
  "successCount": 2,
  "totalCount": 3
}
```

#### 2.4 重新计算差异

**接口地址**: `POST /{id}/recalculate`

**接口描述**: 重新计算指定记录的差异数量

**路径参数**:
- `id`: 差异记录ID（必填，大于0的整数）

**响应示例**:
```json
{
  "id": 1,
  "differenceQuantity": 55.000,
  // ... 其他字段
}
```

### 3. SAP集成接口

#### 3.1 批量同步SAP库存

**接口地址**: `POST /sync-sap-stock`

**接口描述**: 批量同步SAP库存数据

**请求参数**:
```json
[
  {
    "syncTime": "2024-07-28T10:00:00",
    "syncStatus": "success",
    "syncMessage": "同步成功",
    "successCount": 1,
    "failedCount": 0
  }
]
```

**响应示例**:
```json
{
  "success": true,
  "message": "SAP库存同步完成",
  "successCount": 5,
  "totalCount": 5
}
```

#### 3.2 手动同步单个物料的SAP库存

**接口地址**: `POST /sync-single-sap-stock`

**接口描述**: 手动同步指定物料和仓库的SAP库存

**请求参数**:
- `materialCode`: 物料编码（必填）
- `erpWarehouseCode`: ERP仓库编码（必填）

**响应示例**:
```json
{
  "success": true,
  "message": "SAP库存同步成功",
  "materialCode": "MAT001",
  "erpWarehouseCode": "WH001"
}
```

### 4. 统计信息接口

#### 4.1 获取差异处理统计信息

**接口地址**: `GET /statistics`

**接口描述**: 获取差异处理的综合统计信息

**响应示例**:
```json
{
  "totalCount": 100,                 // 总记录数
  "pendingCount": 20,                // 待处理记录数
  "processedCount": 80,              // 已处理记录数
  "totalDifferenceAmount": 1000.000, // 总差异数量
  "avgProcessTime": 2.5,             // 平均处理时间（小时）
  "lastSyncTime": "2024-07-28T09:00:00" // 最后同步时间
}
```

#### 4.2 获取待处理差异统计信息

**接口地址**: `GET /statistics/pending`

**接口描述**: 获取当前待处理差异的统计信息

**响应示例**:
```json
{
  "pendingCount": 20,
  "totalDifferenceAmount": 500.000,
  "avgDifferenceAmount": 25.000,
  "maxDifferenceAmount": 100.000,
  "minDifferenceAmount": 5.000
}
```

#### 4.3 获取已处理差异统计信息

**接口地址**: `GET /statistics/processed`

**接口描述**: 获取指定天数内已处理差异的统计信息

**请求参数**:
- `days`: 统计天数（可选，默认7天，最小值1）

**响应示例**:
```json
{
  "processedCount": 80,
  "avgProcessTime": 2.5,
  "totalProcessedAmount": 2000.000,
  "processingEfficiency": 95.5
}
```

### 5. 数据导出接口

#### 5.1 导出差异记录

**接口地址**: `POST /export`

**接口描述**: 根据查询条件导出差异记录到Excel文件

**请求参数**: 与分页查询接口相同（不需要分页参数）

**响应示例**:
```json
{
  "success": true,
  "message": "导出成功",
  "exportCount": 50,
  "data": [
    {
      // 导出的差异记录数据
    }
  ]
}
```

### 6. 数据管理接口

#### 6.1 删除差异处理记录

**接口地址**: `DELETE /{id}`

**接口描述**: 删除指定的差异处理记录

**路径参数**:
- `id`: 差异处理记录ID（必填，大于0的整数）

**响应示例**:
```json
{
  "success": true,
  "message": "删除成功"
}
```

## 错误响应格式

所有接口在发生错误时都会返回统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述信息"
}
```

## HTTP状态码说明

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

## 数据类型说明

### InventoryDifferenceDTO 字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 差异记录ID |
| materialCode | String | 物料编码 |
| materialName | String | 物料名称 |
| erpWarehouseCode | String | ERP仓库编码 |
| erpWarehouseName | String | ERP仓库名称 |
| department | String | 所属部门 |
| mesCurrentStock | BigDecimal | MES当前库存 |
| mesInitialStock | BigDecimal | MES期初库存 |
| weighbridgeReceiptQuantity | BigDecimal | 地磅收货数量 |
| solidWasteQuantity | BigDecimal | 固废提报数量 |
| issuedQuantity | BigDecimal | 发料数量 |
| unit | String | 计量单位 |
| sapStock | BigDecimal | SAP库存 |
| differenceQuantity | BigDecimal | 差异数量 |
| statisticsStartDate | Date | 统计开始日期 |
| statisticsEndDate | Date | 统计结束日期 |
| status | Integer | 状态：1-待处理，2-已处理 |
| statusDesc | String | 状态描述 |
| processorId | Integer | 处理人ID |
| processorName | String | 处理人姓名 |
| processTime | Date | 处理时间 |
| processRemark | String | 处理备注 |
| sapSyncTime | Date | SAP同步时间 |
| createTime | Date | 创建时间 |
| detailList | List | 处理明细列表 |

## 使用示例

### 查询待处理记录并处理

1. 查询待处理记录：
```bash
GET /api/rawmaterial/material-difference/pending
```

2. 处理差异记录：
```bash
POST /api/rawmaterial/material-difference/process
Content-Type: application/json
User-Id: 1

{
  "id": 1,
  "statisticsEndDate": "2024-07-28T23:59:59",
  "processRemark": "处理完成"
}
```

### 批量同步SAP库存

```bash
POST /api/rawmaterial/material-difference/sync-sap-stock
Content-Type: application/json

[
  {
    "syncTime": "2024-07-28T10:00:00",
    "syncStatus": "success",
    "syncMessage": "同步成功",
    "successCount": 1,
    "failedCount": 0
  }
]
```

## 注意事项

1. 所有涉及用户操作的接口都需要在请求头中传递`User-Id`
2. 日期格式统一使用ISO 8601格式：`yyyy-MM-ddTHH:mm:ss`
3. 数值类型使用BigDecimal，保留3位小数
4. 分页查询的页码从1开始
5. 删除操作不可逆，请谨慎使用
6. 批量操作建议控制批次大小，避免超时
7. 导出功能建议添加数据量限制，避免内存溢出

## 版本信息

- **API版本**: v1.0
- **最后更新**: 2024-07-28
- **维护人员**: z19235