package com.hvisions.rawmaterial.service.impl;

import com.hvisions.auth.client.UserClient;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.consts.VentilationConstants;
import com.hvisions.rawmaterial.dao.VentilationRecordRepository;
import com.hvisions.rawmaterial.dto.VentilationRecordDTO;
import com.hvisions.rawmaterial.dto.VentilationRecordQueryDTO;
import com.hvisions.rawmaterial.dto.VentilationTemperatureMaintainDTO;
import com.hvisions.rawmaterial.entity.ventilation.TMpdVentilationRecord;
import com.hvisions.rawmaterial.mapper.VentilationRecordMapper;
import com.hvisions.rawmaterial.service.VentilationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 通风记录服务实现类
 * @Date: 2024/07/14
 */
@Slf4j
@Service
public class VentilationRecordServiceImpl implements VentilationRecordService {

    @Autowired
    private VentilationRecordRepository ventilationRecordRepository;

    @Resource
    private VentilationRecordMapper ventilationRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createVentilationRecord(VentilationRecordDTO dto) {
        TMpdVentilationRecord record = new TMpdVentilationRecord();
        BeanUtils.copyProperties(dto, record);

        // 生成工单号
        if (record.getWorkOrderNumber() == null || record.getWorkOrderNumber().trim().isEmpty()) {
            record.setWorkOrderNumber(generateWorkOrderNumber());
        }

        // 设置工单日期
        if (record.getWorkOrderDate() == null) {
            record.setWorkOrderDate(new Date());
        }

        // 设置初始状态为执行中
        record.setStatus(VentilationConstants.STATUS_EXECUTING);

        // 生成序号
        if (record.getSerialNumber() == null || record.getSerialNumber().trim().isEmpty()) {
            record.setSerialNumber(generateSerialNumber());
        }

        record = ventilationRecordRepository.save(record);
        log.info("创建通风记录成功，工单号：{}，中控任务ID：{}", record.getWorkOrderNumber(), record.getCentralControlTaskId());

        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeVentilationRecord(String centralControlTaskId, Date endTime) {
        Optional<TMpdVentilationRecord> recordOpt = ventilationRecordRepository.findByCentralControlTaskId(centralControlTaskId);
        if (!recordOpt.isPresent()) {
            log.warn("未找到中控任务ID为{}的通风记录", centralControlTaskId);
            return false;
        }

        TMpdVentilationRecord record = recordOpt.get();
        record.setStatus(VentilationConstants.STATUS_COMPLETED);
        record.setEndTime(endTime);

        ventilationRecordRepository.save(record);
        log.info("通风记录完成，工单号：{}，中控任务ID：{}", record.getWorkOrderNumber(), centralControlTaskId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean maintainTemperature(VentilationTemperatureMaintainDTO dto, UserInfoDTO userInfo) {
        Optional<TMpdVentilationRecord> recordOpt = ventilationRecordRepository.findById(dto.getId());
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("通风记录不存在");
        }

        TMpdVentilationRecord record = recordOpt.get();
        
        // 更新温度信息
        record.setTemperatureBefore(dto.getTemperatureBefore());
        record.setTemperatureAfter(dto.getTemperatureAfter());
        record.setRemark(dto.getRemark());
        record.setTemperatureMaintainTime(new Date());

        // 设置维护人信息（从当前登录用户获取）
        record.setTemperatureMaintainerId(userInfo.getId());
        record.setTemperatureMaintainerName(userInfo.getUserName());

        ventilationRecordRepository.save(record);
        log.info("温度维护成功，通风记录ID：{}，工单号：{}", record.getId(), record.getWorkOrderNumber());

        return true;
    }

    @Override
    public Page<VentilationRecordDTO> queryVentilationRecords(VentilationRecordQueryDTO queryDTO) {
        return PageHelperUtil.getPage(ventilationRecordMapper::queryVentilationRecords, queryDTO, VentilationRecordDTO.class);
    }

    @Override
    public VentilationRecordDTO getVentilationRecordById(Integer id) {
        Optional<TMpdVentilationRecord> recordOpt = ventilationRecordRepository.findById(id);
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("通风记录不存在");
        }
        return convertToDTO(recordOpt.get());
    }

    @Override
    public VentilationRecordDTO getVentilationRecordByWorkOrderNumber(String workOrderNumber) {
        VentilationRecordDTO dto = ventilationRecordMapper.getVentilationRecordByWorkOrderNumber(workOrderNumber);
        if (dto == null) {
            throw new IllegalArgumentException("通风记录不存在");
        }
        return dto;
    }

    @Override
    public List<VentilationRecordDTO> getExecutingRecords() {
        return ventilationRecordMapper.getExecutingRecords();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteVentilationRecord(Integer id) {
        if (!ventilationRecordRepository.existsById(id)) {
            throw new IllegalArgumentException("通风记录不存在");
        }
        ventilationRecordRepository.deleteById(id);
        log.info("删除通风记录成功，ID：{}", id);
        return true;
    }

    @Override
    public String generateWorkOrderNumber() {
        // 生成工单号：TF+YYMMDD+三位流水
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String dateStr = dateFormat.format(new Date());

        // 查询当天的工单数量
        SimpleDateFormat workOrderDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String workOrderDateStr = workOrderDateFormat.format(new Date());
        Long count = ventilationRecordMapper.countByWorkOrderDate(workOrderDateStr);

        // 生成三位流水号
        String serialNumber = String.format("%03d", count + 1);

        return VentilationConstants.WORK_ORDER_PREFIX + dateStr + serialNumber;
    }

    /**
     * 生成序号（流水号）
     * @return 序号
     */
    private String generateSerialNumber() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return dateFormat.format(new Date());
    }

    /**
     * 转换实体为DTO
     * @param record 通风记录实体
     * @return 通风记录DTO
     */
    private VentilationRecordDTO convertToDTO(TMpdVentilationRecord record) {
        VentilationRecordDTO dto = new VentilationRecordDTO();
        BeanUtils.copyProperties(record, dto);

        // 设置状态描述
        if (record.getStatus() != null) {
            if (VentilationConstants.STATUS_EXECUTING.equals(record.getStatus())) {
                dto.setStatusDesc(VentilationConstants.STATUS_EXECUTING_DESC);
            } else if (VentilationConstants.STATUS_COMPLETED.equals(record.getStatus())) {
                dto.setStatusDesc(VentilationConstants.STATUS_COMPLETED_DESC);
            }
        }

        return dto;
    }
}
