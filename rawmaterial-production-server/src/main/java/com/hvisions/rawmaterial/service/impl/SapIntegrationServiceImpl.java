package com.hvisions.rawmaterial.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.brewage.purchase.dto.SapBaseResponseDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.rawmaterial.consts.SapConst;
import com.hvisions.rawmaterial.dto.*;
import com.hvisions.rawmaterial.service.SapIntegrationService;
import com.hvisions.rawmaterial.service.cache.CacheService;
import com.hvisions.rawmaterial.utils.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: SAP系统集成服务实现类
 * @Date: 2024/07/28
 */
@Slf4j
@Service
public class SapIntegrationServiceImpl implements SapIntegrationService {

    @Autowired
    private RequestSap requestSap;

    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 2000L;
    private static final int BATCH_SIZE = 50;
    private static final long SAP_TIMEOUT_MS = 30000L;

    @Override
    @Retryable(value = {Exception.class}, maxAttempts = MAX_RETRY_ATTEMPTS, 
               backoff = @Backoff(delay = RETRY_DELAY_MS))
    public List<SapStockSyncDTO> getSapStockData(String materialCode, String warehouseCode) {
        log.info("开始获取SAP库存数据，物料编码：{}，仓库编码：{}", materialCode, warehouseCode);
        
        validateParameters(materialCode, warehouseCode);
        
        try {
            // 构建SAP请求
            List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = buildStockQueryInputs(materialCode, warehouseCode);
            
            // 调用SAP接口
            SapBaseResponseDto response = requestSap.dockingSap(inputs, SapConst.INVENTORY_QUERY_NO, SapConst.SAP_URI);
            
            // 解析响应
            List<SapStockSyncDTO> stockData = parseStockResponse(response, materialCode, warehouseCode);
            
            log.info("成功获取SAP库存数据，物料编码：{}，仓库编码：{}，数据条数：{}", 
                    materialCode, warehouseCode, stockData.size());
            
            return stockData;
            
        } catch (Exception e) {
            log.error("获取SAP库存数据失败，物料编码：{}，仓库编码：{}，错误信息：{}", 
                     materialCode, warehouseCode, e.getMessage(), e);
            throw new BaseKnownException(10001, "获取SAP库存数据失败：" + e.getMessage());
        }
    }

    @Override
    @Retryable(value = {Exception.class}, maxAttempts = MAX_RETRY_ATTEMPTS, 
               backoff = @Backoff(delay = RETRY_DELAY_MS))
    public SapSyncResultDTO syncDifferenceToSap(InventoryDifferenceDTO difference) {
        log.info("开始同步库存差异到SAP，差异ID：{}", difference.getId());
        
        validateDifferenceData(difference);
        
        Date syncStartTime = new Date();
        SapSyncResultDTO result = new SapSyncResultDTO();
        result.setSyncTaskId(generateSyncTaskId());
        result.setSyncStartTime(syncStartTime);
        
        try {
            // 构建SAP同步请求
            List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = buildDifferenceInputs(difference);
            
            // 调用SAP接口
            SapBaseResponseDto response = requestSap.dockingSap(inputs, SapConst.INVENTORY_MOVEMENT_NO, SapConst.SAP_URI);
            
            // 处理同步结果
            processSyncResponse(response, result);
            
            result.setSyncEndTime(new Date());
            result.setDurationSeconds((result.getSyncEndTime().getTime() - syncStartTime.getTime()) / 1000);
            
            log.info("库存差异同步完成，差异ID：{}，同步状态：{}", difference.getId(), result.getSyncStatus());
            
            return result;
            
        } catch (Exception e) {
            log.error("同步库存差异到SAP失败，差异ID：{}，错误信息：{}", difference.getId(), e.getMessage(), e);
            
            result.setSyncEndTime(new Date());
            result.setSyncStatus(3); // 失败
            result.setSyncStatusDesc("同步失败");
            result.setErrorMessage(e.getMessage());
            result.setFailureCount(1);
            result.setDurationSeconds((result.getSyncEndTime().getTime() - syncStartTime.getTime()) / 1000);
            
            return result;
        }
    }

    @Override
    public SapSyncResultDTO batchSyncStock(List<String> materialCodes) {
        log.info("开始批量同步库存数据，物料数量：{}", materialCodes.size());
        
        if (CollectionUtils.isEmpty(materialCodes)) {
            throw new BaseKnownException(10002, "物料编码列表不能为空");
        }
        
        Date syncStartTime = new Date();
        SapSyncResultDTO result = new SapSyncResultDTO();
        result.setSyncTaskId(generateSyncTaskId());
        result.setSyncStartTime(syncStartTime);
        result.setTotalCount(materialCodes.size());
        
        try {
            // 分批处理
            List<List<String>> batches = partitionList(materialCodes, BATCH_SIZE);
            List<SapSyncFailureDTO> allFailures = new ArrayList<>();
            int totalSuccess = 0;
            
            for (List<String> batch : batches) {
                try {
                    SapSyncResultDTO batchResult = processBatch(batch);
                    totalSuccess += batchResult.getSuccessCount();
                    if (!CollectionUtils.isEmpty(batchResult.getFailureDetails())) {
                        allFailures.addAll(batchResult.getFailureDetails());
                    }
                } catch (Exception e) {
                    log.error("批次处理失败，批次大小：{}，错误信息：{}", batch.size(), e.getMessage(), e);
                    // 将整个批次标记为失败
                    for (String materialCode : batch) {
                        SapSyncFailureDTO failure = createFailureRecord(materialCode, e.getMessage());
                        allFailures.add(failure);
                    }
                }
            }
            
            result.setSuccessCount(totalSuccess);
            result.setFailureCount(allFailures.size());
            result.setFailureDetails(allFailures);
            
            // 设置同步状态
            if (result.getFailureCount() == 0) {
                result.setSyncStatus(1); // 成功
                result.setSyncStatusDesc("批量同步成功");
            } else if (result.getSuccessCount() > 0) {
                result.setSyncStatus(2); // 部分成功
                result.setSyncStatusDesc("批量同步部分成功");
            } else {
                result.setSyncStatus(3); // 失败
                result.setSyncStatusDesc("批量同步失败");
            }
            
            result.setSyncEndTime(new Date());
            result.setDurationSeconds((result.getSyncEndTime().getTime() - syncStartTime.getTime()) / 1000);
            result.setSuccessRate(calculateSuccessRate(result.getSuccessCount(), result.getTotalCount()));
            
            log.info("批量同步完成，总数：{}，成功：{}，失败：{}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
            return result;
            
        } catch (Exception e) {
            log.error("批量同步库存数据失败，错误信息：{}", e.getMessage(), e);
            
            result.setSyncEndTime(new Date());
            result.setSyncStatus(3);
            result.setSyncStatusDesc("批量同步失败");
            result.setErrorMessage(e.getMessage());
            result.setFailureCount(materialCodes.size());
            result.setDurationSeconds((result.getSyncEndTime().getTime() - syncStartTime.getTime()) / 1000);
            
            return result;
        }
    }

    @Override
    public List<SapStockSyncDTO> batchGetSapStockData(List<String> materialCodes, String warehouseCode) {
        log.info("开始批量获取SAP库存数据，物料数量：{}，仓库编码：{}", materialCodes.size(), warehouseCode);
        
        if (CollectionUtils.isEmpty(materialCodes)) {
            throw new BaseKnownException(10002, "物料编码列表不能为空");
        }
        
        if (!StringUtils.hasText(warehouseCode)) {
            throw new BaseKnownException(10003, "仓库编码不能为空");
        }
        
        List<SapStockSyncDTO> allStockData = new ArrayList<>();
        
        // 分批处理
        List<List<String>> batches = partitionList(materialCodes, BATCH_SIZE);
        
        for (List<String> batch : batches) {
            try {
                List<SapStockSyncDTO> batchData = getBatchStockData(batch, warehouseCode);
                allStockData.addAll(batchData);
            } catch (Exception e) {
                log.error("批量获取库存数据失败，批次大小：{}，错误信息：{}", batch.size(), e.getMessage(), e);
                // 继续处理下一批次
            }
        }
        
        log.info("批量获取SAP库存数据完成，获取数据条数：{}", allStockData.size());
        return allStockData;
    }

    @Override
    @Retryable(value = {Exception.class}, maxAttempts = MAX_RETRY_ATTEMPTS, 
               backoff = @Backoff(delay = RETRY_DELAY_MS))
    public SapSyncResultDTO syncSingleDifference(String materialCode, String warehouseCode, 
                                                BigDecimal differenceQuantity, String movementType) {
        log.info("开始同步单个库存差异，物料编码：{}，仓库编码：{}，差异数量：{}", 
                materialCode, warehouseCode, differenceQuantity);
        
        validateParameters(materialCode, warehouseCode);
        
        if (differenceQuantity == null || differenceQuantity.compareTo(BigDecimal.ZERO) == 0) {
            throw new BaseKnownException(10004, "差异数量不能为空或零");
        }
        
        Date syncStartTime = new Date();
        SapSyncResultDTO result = new SapSyncResultDTO();
        result.setSyncTaskId(generateSyncTaskId());
        result.setSyncStartTime(syncStartTime);
        result.setTotalCount(1);
        
        try {
            // 构建SAP请求
            List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = buildSingleDifferenceInputs(
                    materialCode, warehouseCode, differenceQuantity, movementType);
            
            // 调用SAP接口
            SapBaseResponseDto response = requestSap.dockingSap(inputs, SapConst.INVENTORY_MOVEMENT_NO, SapConst.SAP_URI);
            
            // 处理同步结果
            processSyncResponse(response, result);
            
            result.setSyncEndTime(new Date());
            result.setDurationSeconds((result.getSyncEndTime().getTime() - syncStartTime.getTime()) / 1000);
            
            log.info("单个库存差异同步完成，物料编码：{}，同步状态：{}", materialCode, result.getSyncStatus());
            
            return result;
            
        } catch (Exception e) {
            log.error("同步单个库存差异失败，物料编码：{}，错误信息：{}", materialCode, e.getMessage(), e);
            
            result.setSyncEndTime(new Date());
            result.setSyncStatus(3);
            result.setSyncStatusDesc("同步失败");
            result.setErrorMessage(e.getMessage());
            result.setFailureCount(1);
            result.setDurationSeconds((result.getSyncEndTime().getTime() - syncStartTime.getTime()) / 1000);
            
            return result;
        }
    }

    @Override
    public boolean testSapConnection() {
        log.info("开始测试SAP连接");
        
        try {
            // 使用简单的库存查询来测试连接
            List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = buildTestConnectionInputs();
            
            // 设置较短的超时时间进行连接测试
            CompletableFuture<SapBaseResponseDto> future = CompletableFuture.supplyAsync(() -> {
                return requestSap.dockingSap(inputs, SapConst.INVENTORY_QUERY_NO, SapConst.SAP_URI);
            });
            
            SapBaseResponseDto response = future.get(10, TimeUnit.SECONDS);
            
            boolean isConnected = response != null && !"E".equals(response.getStatus());
            
            log.info("SAP连接测试完成，连接状态：{}", isConnected ? "成功" : "失败");
            return isConnected;
            
        } catch (Exception e) {
            log.error("SAP连接测试失败，错误信息：{}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    private void validateParameters(String materialCode, String warehouseCode) {
        if (!StringUtils.hasText(materialCode)) {
            throw new BaseKnownException(10003, "物料编码不能为空");
        }
        if (!StringUtils.hasText(warehouseCode)) {
            throw new BaseKnownException(10003, "仓库编码不能为空");
        }
    }

    private void validateDifferenceData(InventoryDifferenceDTO difference) {
        if (difference == null) {
            throw new BaseKnownException(10004, "库存差异数据不能为空");
        }
        if (!StringUtils.hasText(difference.getMaterialCode())) {
            throw new BaseKnownException(10003, "物料编码不能为空");
        }
        if (!StringUtils.hasText(difference.getErpWarehouseCode())) {
            throw new BaseKnownException(10003, "仓库编码不能为空");
        }
        if (difference.getDifferenceQuantity() == null) {
            throw new BaseKnownException(10004, "差异数量不能为空");
        }
    }

    private List<SapBaseRequestDto.Request.List.IvInput.Input> buildStockQueryInputs(String materialCode, String warehouseCode) {
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        
        SapBaseHeaderDto header = new SapBaseHeaderDto();
        header.setHeaderKey(generateHeaderKey());
        header.setExtOne(materialCode);
        header.setExtTwo(warehouseCode);
        
        List<SapBaseItemDto> items = new ArrayList<>();
        SapBaseItemDto item = new SapBaseItemDto();
        item.setItemKey(generateItemKey());
        item.setMoveType("QUERY");
        item.setExtOne(materialCode);
        item.setExtTwo(warehouseCode);
        items.add(item);
        
        SapBaseRequestDto.Request.List.IvInput.Input input = 
                new SapBaseRequestDto.Request.List.IvInput.Input(header, items);
        inputs.add(input);
        
        return inputs;
    }

    private List<SapBaseRequestDto.Request.List.IvInput.Input> buildDifferenceInputs(InventoryDifferenceDTO difference) {
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        
        SapBaseHeaderDto header = new SapBaseHeaderDto();
        header.setHeaderKey(generateHeaderKey());
        header.setExtOne(difference.getMaterialCode());
        header.setExtTwo(difference.getErpWarehouseCode());
        header.setExtThree(difference.getDifferenceQuantity().toString());
        
        List<SapBaseItemDto> items = new ArrayList<>();
        SapBaseItemDto item = new SapBaseItemDto();
        item.setItemKey(generateItemKey());
        item.setMoveType(determineMoveType(difference.getDifferenceQuantity()));
        item.setExtOne(difference.getMaterialCode());
        item.setExtTwo(difference.getErpWarehouseCode());
        item.setExtThree(difference.getDifferenceQuantity().abs().toString());
        item.setExtFour(difference.getUnit());
        items.add(item);
        
        SapBaseRequestDto.Request.List.IvInput.Input input = 
                new SapBaseRequestDto.Request.List.IvInput.Input(header, items);
        inputs.add(input);
        
        return inputs;
    }

    private List<SapBaseRequestDto.Request.List.IvInput.Input> buildSingleDifferenceInputs(
            String materialCode, String warehouseCode, BigDecimal differenceQuantity, String movementType) {
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        
        SapBaseHeaderDto header = new SapBaseHeaderDto();
        header.setHeaderKey(generateHeaderKey());
        header.setExtOne(materialCode);
        header.setExtTwo(warehouseCode);
        header.setExtThree(differenceQuantity.toString());
        
        List<SapBaseItemDto> items = new ArrayList<>();
        SapBaseItemDto item = new SapBaseItemDto();
        item.setItemKey(generateItemKey());
        item.setMoveType(StringUtils.hasText(movementType) ? movementType : determineMoveType(differenceQuantity));
        item.setExtOne(materialCode);
        item.setExtTwo(warehouseCode);
        item.setExtThree(differenceQuantity.abs().toString());
        items.add(item);
        
        SapBaseRequestDto.Request.List.IvInput.Input input = 
                new SapBaseRequestDto.Request.List.IvInput.Input(header, items);
        inputs.add(input);
        
        return inputs;
    }

    private List<SapBaseRequestDto.Request.List.IvInput.Input> buildTestConnectionInputs() {
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        
        SapBaseHeaderDto header = new SapBaseHeaderDto();
        header.setHeaderKey("TEST_CONNECTION");
        
        List<SapBaseItemDto> items = new ArrayList<>();
        SapBaseItemDto item = new SapBaseItemDto();
        item.setItemKey("TEST_ITEM");
        item.setMoveType("TEST");
        items.add(item);
        
        SapBaseRequestDto.Request.List.IvInput.Input input = 
                new SapBaseRequestDto.Request.List.IvInput.Input(header, items);
        inputs.add(input);
        
        return inputs;
    }

    private List<SapStockSyncDTO> parseStockResponse(SapBaseResponseDto response, String materialCode, String warehouseCode) {
        List<SapStockSyncDTO> stockData = new ArrayList<>();
        
        if (response == null || "E".equals(response.getStatus())) {
            log.warn("SAP响应异常，物料编码：{}，仓库编码：{}", materialCode, warehouseCode);
            return stockData;
        }
        
        // 解析响应数据
        // 这里需要根据实际的SAP响应格式进行解析
        SapStockSyncDTO stockSync = new SapStockSyncDTO();
        stockSync.setMaterialCode(materialCode);
        stockSync.setErpWarehouseCode(warehouseCode);
        stockSync.setSyncTime(new Date());
        stockSync.setSyncStatus(1);
        stockSync.setSyncStatusDesc("获取成功");
        
        // 从响应中解析库存数量
        if (response.getResponse() != null && response.getResponse().getList() != null) {
            // 根据实际响应结构解析库存数量
            stockSync.setSapStock(BigDecimal.ZERO); // 默认值，实际应从响应中解析
        }
        
        stockData.add(stockSync);
        return stockData;
    }

    private void processSyncResponse(SapBaseResponseDto response, SapSyncResultDTO result) {
        if (response == null) {
            result.setSyncStatus(3);
            result.setSyncStatusDesc("SAP响应为空");
            result.setErrorMessage("SAP接口无响应");
            result.setFailureCount(1);
            return;
        }
        
        if ("S".equals(response.getStatus())) {
            result.setSyncStatus(1);
            result.setSyncStatusDesc("同步成功");
            result.setSuccessCount(1);
        } else {
            result.setSyncStatus(3);
            result.setSyncStatusDesc("同步失败");
            result.setErrorMessage(response.getMessage());
            result.setFailureCount(1);
        }
    }

    private SapSyncResultDTO processBatch(List<String> batch) {
        SapSyncResultDTO batchResult = new SapSyncResultDTO();
        batchResult.setTotalCount(batch.size());
        
        int successCount = 0;
        List<SapSyncFailureDTO> failures = new ArrayList<>();
        
        for (String materialCode : batch) {
            try {
                // 这里应该调用实际的批量处理逻辑
                // 为了简化，这里模拟处理结果
                boolean success = processSingleMaterial(materialCode);
                if (success) {
                    successCount++;
                } else {
                    SapSyncFailureDTO failure = createFailureRecord(materialCode, "处理失败");
                    failures.add(failure);
                }
            } catch (Exception e) {
                SapSyncFailureDTO failure = createFailureRecord(materialCode, e.getMessage());
                failures.add(failure);
            }
        }
        
        batchResult.setSuccessCount(successCount);
        batchResult.setFailureCount(failures.size());
        batchResult.setFailureDetails(failures);
        
        return batchResult;
    }

    private boolean processSingleMaterial(String materialCode) {
        // 这里应该实现单个物料的处理逻辑
        // 为了简化，这里返回模拟结果
        return true;
    }

    private List<SapStockSyncDTO> getBatchStockData(List<String> batch, String warehouseCode) {
        List<SapStockSyncDTO> batchData = new ArrayList<>();
        
        for (String materialCode : batch) {
            try {
                List<SapStockSyncDTO> stockData = getSapStockData(materialCode, warehouseCode);
                batchData.addAll(stockData);
            } catch (Exception e) {
                log.error("获取单个物料库存数据失败，物料编码：{}，错误信息：{}", materialCode, e.getMessage());
                // 继续处理下一个物料
            }
        }
        
        return batchData;
    }

    private SapSyncFailureDTO createFailureRecord(String materialCode, String errorMessage) {
        SapSyncFailureDTO failure = new SapSyncFailureDTO();
        failure.setMaterialCode(materialCode);
        failure.setFailureTime(new Date());
        failure.setFailureReason(errorMessage);
        failure.setRetryCount(0);
        failure.setCanRetry(true);
        return failure;
    }

    private String determineMoveType(BigDecimal differenceQuantity) {
        if (differenceQuantity.compareTo(BigDecimal.ZERO) > 0) {
            return "701"; // 盘盈
        } else {
            return "702"; // 盘亏
        }
    }

    private String generateSyncTaskId() {
        return "SYNC_" + System.currentTimeMillis();
    }

    private String generateHeaderKey() {
        return "HDR_" + System.currentTimeMillis();
    }

    private String generateItemKey() {
        return "ITM_" + System.currentTimeMillis();
    }

    private Double calculateSuccessRate(Integer successCount, Integer totalCount) {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    @Override
    public SapStockSyncDTO syncAllStock() {
        log.info("开始全量同步SAP库存");
        
        SapStockSyncDTO result = new SapStockSyncDTO();
        result.setSyncTime(new Date());
        result.setSyncType("FULL");
        
        try {
            // 获取所有需要同步的物料
            List<String> allMaterialCodes = getAllMaterialCodes();
            
            if (CollectionUtils.isEmpty(allMaterialCodes)) {
                result.setSuccess(true);
                result.setMessage("没有需要同步的物料");
                result.setTotalCount(0);
                result.setSuccessCount(0);
                result.setFailureCount(0);
                return result;
            }
            
            // 分批同步
            int totalCount = allMaterialCodes.size();
            int successCount = 0;
            int failureCount = 0;
            List<SapSyncFailureDTO> failures = new ArrayList<>();
            
            for (int i = 0; i < totalCount; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalCount);
                List<String> batchCodes = allMaterialCodes.subList(i, endIndex);
                
                try {
                    SapSyncResultDTO batchResult = batchSyncStock(batchCodes);
                    if (batchResult.isSuccess()) {
                        successCount += batchCodes.size();
                    } else {
                        failureCount += batchCodes.size();
                        // 记录失败信息
                        for (String code : batchCodes) {
                            SapSyncFailureDTO failure = new SapSyncFailureDTO();
                            failure.setMaterialCode(code);
                            failure.setErrorMessage(batchResult.getMessage());
                            failures.add(failure);
                        }
                    }
                } catch (Exception e) {
                    log.error("批次同步失败: {}", batchCodes, e);
                    failureCount += batchCodes.size();
                    
                    for (String code : batchCodes) {
                        SapSyncFailureDTO failure = new SapSyncFailureDTO();
                        failure.setMaterialCode(code);
                        failure.setErrorMessage(e.getMessage());
                        failures.add(failure);
                    }
                }
                
                // 避免过度占用资源
                if (i % (BATCH_SIZE * 5) == 0) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            result.setSuccess(failureCount == 0);
            result.setTotalCount(totalCount);
            result.setSuccessCount(successCount);
            result.setFailureCount(failureCount);
            result.setFailures(failures);
            result.setMessage(String.format("全量同步完成，成功: %d, 失败: %d", successCount, failureCount));
            
            log.info("全量同步SAP库存完成，总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failureCount);
            
        } catch (Exception e) {
            log.error("全量同步SAP库存失败", e);
            result.setSuccess(false);
            result.setMessage("全量同步失败: " + e.getMessage());
            result.setFailureCount(result.getTotalCount());
            result.setSuccessCount(0);
        }
        
        return result;
    }

    @Override
    public SapStockSyncDTO batchSyncStock(List<String> materialCodes) {
        log.info("开始批量同步SAP库存，物料数量: {}", materialCodes.size());
        
        SapStockSyncDTO result = new SapStockSyncDTO();
        result.setSyncTime(new Date());
        result.setSyncType("PARTIAL");
        result.setTotalCount(materialCodes.size());
        
        try {
            int successCount = 0;
            int failureCount = 0;
            List<SapSyncFailureDTO> failures = new ArrayList<>();
            
            // 并发处理提高效率
            List<CompletableFuture<SapSyncResultDTO>> futures = materialCodes.stream()
                .map(materialCode -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return syncSingleMaterial(materialCode);
                    } catch (Exception e) {
                        log.error("同步物料失败: {}", materialCode, e);
                        SapSyncResultDTO errorResult = new SapSyncResultDTO();
                        errorResult.setSuccess(false);
                        errorResult.setMessage(e.getMessage());
                        return errorResult;
                    }
                }))
                .collect(Collectors.toList());
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(SAP_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            
            // 收集结果
            for (int i = 0; i < futures.size(); i++) {
                try {
                    SapSyncResultDTO syncResult = futures.get(i).get();
                    if (syncResult.isSuccess()) {
                        successCount++;
                    } else {
                        failureCount++;
                        SapSyncFailureDTO failure = new SapSyncFailureDTO();
                        failure.setMaterialCode(materialCodes.get(i));
                        failure.setErrorMessage(syncResult.getMessage());
                        failures.add(failure);
                    }
                } catch (Exception e) {
                    failureCount++;
                    SapSyncFailureDTO failure = new SapSyncFailureDTO();
                    failure.setMaterialCode(materialCodes.get(i));
                    failure.setErrorMessage(e.getMessage());
                    failures.add(failure);
                }
            }
            
            result.setSuccess(failureCount == 0);
            result.setSuccessCount(successCount);
            result.setFailureCount(failureCount);
            result.setFailures(failures);
            result.setMessage(String.format("批量同步完成，成功: %d, 失败: %d", successCount, failureCount));
            
            log.info("批量同步SAP库存完成，成功: {}, 失败: {}", successCount, failureCount);
            
        } catch (Exception e) {
            log.error("批量同步SAP库存失败", e);
            result.setSuccess(false);
            result.setMessage("批量同步失败: " + e.getMessage());
            result.setFailureCount(result.getTotalCount());
            result.setSuccessCount(0);
        }
        
        return result;
    }
    
    /**
     * 获取所有需要同步的物料编码
     */
    private List<String> getAllMaterialCodes() {
        // 这里应该从数据库或配置中获取所有需要同步的物料编码
        // 暂时返回空列表，实际实现时需要查询数据库
        log.debug("获取所有物料编码");
        return new ArrayList<>();
    }
    
    /**
     * 同步单个物料
     */
    private SapSyncResultDTO syncSingleMaterial(String materialCode) {
        log.debug("同步单个物料: {}", materialCode);
        
        SapSyncResultDTO result = new SapSyncResultDTO();
        
        try {
            // 这里实现具体的单个物料同步逻辑
            // 暂时模拟成功
            result.setSuccess(true);
            result.setMessage("同步成功");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        
        return result;
    }
}