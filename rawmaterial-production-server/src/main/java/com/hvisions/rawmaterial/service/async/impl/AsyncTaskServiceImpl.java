package com.hvisions.rawmaterial.service.async.impl;

import com.hvisions.rawmaterial.configuration.InventoryDifferenceRabbitConfig;
import com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage;
import com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage;
import com.hvisions.rawmaterial.dto.async.TaskStatusMessage;
import com.hvisions.rawmaterial.service.async.AsyncTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 异步任务服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AsyncTaskServiceImpl implements AsyncTaskService {
    
    private static final String TASK_STATUS_KEY_PREFIX = "inventory:task:status:";
    private static final String USER_TASKS_KEY_PREFIX = "inventory:user:tasks:";
    private static final int TASK_STATUS_EXPIRE_HOURS = 24; // 任务状态保存24小时
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public String submitSapSyncTask(SapSyncTaskMessage message) {
        log.info("提交SAP同步任务: {}", message.getTaskId());
        
        try {
            // 初始化任务状态
            TaskStatusMessage status = new TaskStatusMessage(
                message.getTaskId(), 
                message.getTaskType(), 
                TaskStatusMessage.TaskStatus.PENDING
            );
            status.setStatusDescription("任务已提交，等待处理");
            saveTaskStatus(status);
            
            // 添加到用户任务列表
            if (message.getCreatorId() != null) {
                addUserTask(message.getCreatorId(), message.getTaskId());
            }
            
            // 发送消息到队列
            rabbitTemplate.convertAndSend(
                InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
                InventoryDifferenceRabbitConfig.SAP_SYNC_ROUTING_KEY,
                message
            );
            
            log.info("SAP同步任务已提交到队列: {}", message.getTaskId());
            return message.getTaskId();
            
        } catch (Exception e) {
            log.error("提交SAP同步任务失败: {}", message.getTaskId(), e);
            
            // 更新任务状态为失败
            TaskStatusMessage status = new TaskStatusMessage(
                message.getTaskId(), 
                message.getTaskType(), 
                TaskStatusMessage.TaskStatus.FAILED
            );
            status.setStatusDescription("任务提交失败");
            status.setErrorMessage(e.getMessage());
            saveTaskStatus(status);
            
            throw new RuntimeException("提交SAP同步任务失败", e);
        }
    }
    
    @Override
    public String submitBatchGenerateTask(BatchGenerateTaskMessage message) {
        log.info("提交批量生成任务: {}", message.getTaskId());
        
        try {
            // 初始化任务状态
            TaskStatusMessage status = new TaskStatusMessage(
                message.getTaskId(), 
                message.getTaskType(), 
                TaskStatusMessage.TaskStatus.PENDING
            );
            status.setStatusDescription("任务已提交，等待处理");
            status.setTotalCount(message.getEstimatedCount());
            saveTaskStatus(status);
            
            // 添加到用户任务列表
            if (message.getCreatorId() != null) {
                addUserTask(message.getCreatorId(), message.getTaskId());
            }
            
            // 发送消息到队列
            rabbitTemplate.convertAndSend(
                InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
                InventoryDifferenceRabbitConfig.BATCH_GENERATE_ROUTING_KEY,
                message
            );
            
            log.info("批量生成任务已提交到队列: {}", message.getTaskId());
            return message.getTaskId();
            
        } catch (Exception e) {
            log.error("提交批量生成任务失败: {}", message.getTaskId(), e);
            
            // 更新任务状态为失败
            TaskStatusMessage status = new TaskStatusMessage(
                message.getTaskId(), 
                message.getTaskType(), 
                TaskStatusMessage.TaskStatus.FAILED
            );
            status.setStatusDescription("任务提交失败");
            status.setErrorMessage(e.getMessage());
            saveTaskStatus(status);
            
            throw new RuntimeException("提交批量生成任务失败", e);
        }
    }
    
    @Override
    public TaskStatusMessage getTaskStatus(String taskId) {
        String key = TASK_STATUS_KEY_PREFIX + taskId;
        TaskStatusMessage status = (TaskStatusMessage) redisTemplate.opsForValue().get(key);
        
        if (status == null) {
            // 任务不存在或已过期
            status = new TaskStatusMessage();
            status.setTaskId(taskId);
            status.setStatus(TaskStatusMessage.TaskStatus.FAILED);
            status.setStatusDescription("任务不存在或已过期");
        }
        
        return status;
    }
    
    @Override
    public List<TaskStatusMessage> getUserTasks(Integer userId, String taskType) {
        String key = USER_TASKS_KEY_PREFIX + userId;
        Set<Object> taskIds = redisTemplate.opsForSet().members(key);
        
        List<TaskStatusMessage> tasks = new ArrayList<>();
        if (taskIds != null) {
            for (Object taskId : taskIds) {
                TaskStatusMessage status = getTaskStatus((String) taskId);
                if (status != null && (taskType == null || taskType.equals(status.getTaskType()))) {
                    tasks.add(status);
                }
            }
        }
        
        // 按更新时间倒序排列
        tasks.sort((a, b) -> b.getUpdateTime().compareTo(a.getUpdateTime()));
        
        return tasks;
    }
    
    @Override
    public boolean cancelTask(String taskId) {
        TaskStatusMessage status = getTaskStatus(taskId);
        if (status == null) {
            return false;
        }
        
        // 只有待处理和执行中的任务可以取消
        if (status.getStatus() == TaskStatusMessage.TaskStatus.PENDING ||
            status.getStatus() == TaskStatusMessage.TaskStatus.RUNNING) {
            
            status.setStatus(TaskStatusMessage.TaskStatus.CANCELLED);
            status.setStatusDescription("任务已取消");
            saveTaskStatus(status);
            
            log.info("任务已取消: {}", taskId);
            return true;
        }
        
        return false;
    }
    
    @Override
    public int cleanExpiredTasks(int expireDays) {
        // 这里可以实现清理过期任务的逻辑
        // 由于使用Redis存储，可以通过TTL自动过期
        log.info("清理{}天前的过期任务", expireDays);
        return 0;
    }
    
    /**
     * 保存任务状态到Redis
     */
    private void saveTaskStatus(TaskStatusMessage status) {
        String key = TASK_STATUS_KEY_PREFIX + status.getTaskId();
        redisTemplate.opsForValue().set(key, status, TASK_STATUS_EXPIRE_HOURS, TimeUnit.HOURS);
        
        // 发送状态更新消息
        rabbitTemplate.convertAndSend(
            InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
            InventoryDifferenceRabbitConfig.TASK_STATUS_ROUTING_KEY,
            status
        );
    }
    
    /**
     * 添加任务到用户任务列表
     */
    private void addUserTask(Integer userId, String taskId) {
        String key = USER_TASKS_KEY_PREFIX + userId;
        redisTemplate.opsForSet().add(key, taskId);
        redisTemplate.expire(key, TASK_STATUS_EXPIRE_HOURS, TimeUnit.HOURS);
    }
    
    /**
     * 更新任务状态（供消息处理器调用）
     */
    public void updateTaskStatus(TaskStatusMessage status) {
        saveTaskStatus(status);
        log.debug("任务状态已更新: {} - {}", status.getTaskId(), status.getStatus());
    }
}