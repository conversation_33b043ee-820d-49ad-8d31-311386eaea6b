package com.hvisions.rawmaterial.service.impl;

import com.hvisions.rawmaterial.dao.*;
import com.hvisions.rawmaterial.dto.MesDataAggregationDTO;
import com.hvisions.rawmaterial.service.MesDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MES数据服务实现类
 * 负责获取MES系统的库存、发放、固废等数据
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class MesDataServiceImpl implements MesDataService {

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private WasteReportRepository wasteReportRepository;

    @Resource
    private RiceDispenseOrderMapper riceDispenseOrderMapper;

    @Resource
    private SorghumDispenseOrderMapper sorghumDispenseOrderMapper;

    @Resource
    private BranIssueOrderMapper branIssueOrderMapper;

    @Resource
    private RiceTransferOrderMapper riceTransferOrderMapper;

    @Resource
    private SorghumTransferOrderMapper sorghumTransferOrderMapper;

    @Resource
    private BranTransferOrderMapper branTransferOrderMapper;

    @Resource
    private FlourTransferOrderMapper flourTransferOrderMapper;

    @Resource
    private ShipmentOrderMapper shipmentOrderMapper;

    @Override
    @Cacheable(value = "mes-data", key = "'mes-current-stock:' + #materialCode + ':' + #warehouseCode")
    public BigDecimal getMesCurrentStock(String materialCode, String warehouseCode) {
        log.debug("获取MES当前库存 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode)) {
            log.warn("物料编码或仓库编码为空");
            return BigDecimal.ZERO;
        }

        try {
            // 通过库存管理模块获取当前库存
            // 这里需要根据实际的数据库表结构和业务逻辑来实现
            // 暂时返回模拟数据，实际实现需要查询相关库存表
            BigDecimal currentStock = queryCurrentStockFromDatabase(materialCode, warehouseCode);
            
            log.debug("MES当前库存查询结果 - 物料编码: {}, 仓库编码: {}, 库存数量: {}", 
                     materialCode, warehouseCode, currentStock);
            
            return currentStock != null ? currentStock : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取MES当前库存失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    @Cacheable(value = "mes-data", key = "'mes-issued-quantity:' + #materialCode + ':' + #warehouseCode + ':' + #startDate?.time + ':' + #endDate?.time")
    public BigDecimal getIssuedQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取发料数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) 
            || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询发料数量");
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal totalIssued = BigDecimal.ZERO;
            
            // 汇总各种发料订单的数量
            // 1. 大米发料订单
            BigDecimal riceIssued = queryRiceIssuedQuantity(materialCode, warehouseCode, startDate, endDate);
            totalIssued = totalIssued.add(riceIssued);
            
            // 2. 高粱发料订单
            BigDecimal sorghumIssued = querySorghumIssuedQuantity(materialCode, warehouseCode, startDate, endDate);
            totalIssued = totalIssued.add(sorghumIssued);
            
            // 3. 麸皮发料订单
            BigDecimal branIssued = queryBranIssuedQuantity(materialCode, warehouseCode, startDate, endDate);
            totalIssued = totalIssued.add(branIssued);
            
            log.debug("发料数量统计结果 - 物料编码: {}, 总发料数量: {}", materialCode, totalIssued);
            
            return totalIssued;
        } catch (Exception e) {
            log.error("获取发料数量统计失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    @Cacheable(value = "mes-data", key = "'mes-solid-waste:' + #materialCode + ':' + #warehouseCode + ':' + #startDate?.time + ':' + #endDate?.time")
    public BigDecimal getSolidWasteQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取固废数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询固废数量");
            return BigDecimal.ZERO;
        }

        try {
            // 查询固废提报数据
            BigDecimal solidWasteQuantity = querySolidWasteQuantity(materialCode, startDate, endDate);
            
            log.debug("固废数量统计结果 - 物料编码: {}, 固废数量: {}", materialCode, solidWasteQuantity);
            
            return solidWasteQuantity != null ? solidWasteQuantity : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取固废数量统计失败 - 物料编码: {}", materialCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    @Cacheable(value = "mes-data", key = "'mes-weighbridge-receipt:' + #materialCode + ':' + #warehouseCode + ':' + #startDate?.time + ':' + #endDate?.time")
    public BigDecimal getWeighbridgeReceiptQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取地磅收货数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) 
            || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询地磅收货数量");
            return BigDecimal.ZERO;
        }

        try {
            // 查询地磅收货数据
            // 这里需要根据实际的地磅系统集成来实现
            BigDecimal weighbridgeQuantity = queryWeighbridgeReceiptQuantity(materialCode, warehouseCode, startDate, endDate);
            
            log.debug("地磅收货数量统计结果 - 物料编码: {}, 收货数量: {}", materialCode, weighbridgeQuantity);
            
            return weighbridgeQuantity != null ? weighbridgeQuantity : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取地磅收货数量统计失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getInboundQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取入库数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) 
            || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询入库数量");
            return BigDecimal.ZERO;
        }

        try {
            // 查询入库数据，包括各种入库订单
            BigDecimal inboundQuantity = queryInboundQuantity(materialCode, warehouseCode, startDate, endDate);
            
            log.debug("入库数量统计结果 - 物料编码: {}, 入库数量: {}", materialCode, inboundQuantity);
            
            return inboundQuantity != null ? inboundQuantity : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取入库数量统计失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getOutboundQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取出库数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) 
            || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询出库数量");
            return BigDecimal.ZERO;
        }

        try {
            // 查询出库数据，包括各种出库订单
            BigDecimal outboundQuantity = queryOutboundQuantity(materialCode, warehouseCode, startDate, endDate);
            
            log.debug("出库数量统计结果 - 物料编码: {}, 出库数量: {}", materialCode, outboundQuantity);
            
            return outboundQuantity != null ? outboundQuantity : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取出库数量统计失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getTransferInQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取调拨入库数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) 
            || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询调拨入库数量");
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal totalTransferIn = BigDecimal.ZERO;
            
            // 汇总各种调拨入库订单的数量
            // 1. 大米调拨入库
            BigDecimal riceTransferIn = queryRiceTransferInQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferIn = totalTransferIn.add(riceTransferIn);
            
            // 2. 高粱调拨入库
            BigDecimal sorghumTransferIn = querySorghumTransferInQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferIn = totalTransferIn.add(sorghumTransferIn);
            
            // 3. 麸皮调拨入库
            BigDecimal branTransferIn = queryBranTransferInQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferIn = totalTransferIn.add(branTransferIn);
            
            // 4. 面粉调拨入库
            BigDecimal flourTransferIn = queryFlourTransferInQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferIn = totalTransferIn.add(flourTransferIn);
            
            log.debug("调拨入库数量统计结果 - 物料编码: {}, 调拨入库数量: {}", materialCode, totalTransferIn);
            
            return totalTransferIn;
        } catch (Exception e) {
            log.error("获取调拨入库数量统计失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getTransferOutQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取调拨出库数量统计 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) 
            || startDate == null || endDate == null) {
            log.warn("参数不完整，无法查询调拨出库数量");
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal totalTransferOut = BigDecimal.ZERO;
            
            // 汇总各种调拨出库订单的数量
            // 1. 大米调拨出库
            BigDecimal riceTransferOut = queryRiceTransferOutQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferOut = totalTransferOut.add(riceTransferOut);
            
            // 2. 高粱调拨出库
            BigDecimal sorghumTransferOut = querySorghumTransferOutQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferOut = totalTransferOut.add(sorghumTransferOut);
            
            // 3. 麸皮调拨出库
            BigDecimal branTransferOut = queryBranTransferOutQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferOut = totalTransferOut.add(branTransferOut);
            
            // 4. 面粉调拨出库
            BigDecimal flourTransferOut = queryFlourTransferOutQuantity(materialCode, warehouseCode, startDate, endDate);
            totalTransferOut = totalTransferOut.add(flourTransferOut);
            
            log.debug("调拨出库数量统计结果 - 物料编码: {}, 调拨出库数量: {}", materialCode, totalTransferOut);
            
            return totalTransferOut;
        } catch (Exception e) {
            log.error("获取调拨出库数量统计失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getMesInitialStock(String materialCode, String warehouseCode, Date referenceDate) {
        log.debug("获取MES期初库存 - 物料编码: {}, 仓库编码: {}, 参考日期: {}", 
                 materialCode, warehouseCode, referenceDate);
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode) || referenceDate == null) {
            log.warn("参数不完整，无法查询MES期初库存");
            return BigDecimal.ZERO;
        }

        try {
            // 查询期初库存数据
            BigDecimal initialStock = queryInitialStock(materialCode, warehouseCode, referenceDate);
            
            log.debug("MES期初库存查询结果 - 物料编码: {}, 期初库存: {}", materialCode, initialStock);
            
            return initialStock != null ? initialStock : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取MES期初库存失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public MesDataAggregationDTO getMesDataAggregation(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        log.debug("获取MES数据汇总信息 - 物料编码: {}, 仓库编码: {}, 开始时间: {}, 结束时间: {}", 
                 materialCode, warehouseCode, startDate, endDate);
        
        MesDataAggregationDTO aggregationDTO = new MesDataAggregationDTO();
        aggregationDTO.setMaterialCode(materialCode);
        aggregationDTO.setErpWarehouseCode(warehouseCode);
        aggregationDTO.setStatisticsStartTime(startDate);
        aggregationDTO.setStatisticsEndTime(endDate);
        aggregationDTO.setDataUpdateTime(new Date());
        
        try {
            // 获取各项数据
            aggregationDTO.setMesCurrentStock(getMesCurrentStock(materialCode, warehouseCode));
            aggregationDTO.setMesInitialStock(getMesInitialStock(materialCode, warehouseCode, startDate));
            aggregationDTO.setWeighbridgeReceiptQuantity(getWeighbridgeReceiptQuantity(materialCode, warehouseCode, startDate, endDate));
            aggregationDTO.setSolidWasteQuantity(getSolidWasteQuantity(materialCode, warehouseCode, startDate, endDate));
            aggregationDTO.setIssuedQuantity(getIssuedQuantity(materialCode, warehouseCode, startDate, endDate));
            aggregationDTO.setInboundQuantity(getInboundQuantity(materialCode, warehouseCode, startDate, endDate));
            aggregationDTO.setOutboundQuantity(getOutboundQuantity(materialCode, warehouseCode, startDate, endDate));
            aggregationDTO.setTransferInQuantity(getTransferInQuantity(materialCode, warehouseCode, startDate, endDate));
            aggregationDTO.setTransferOutQuantity(getTransferOutQuantity(materialCode, warehouseCode, startDate, endDate));
            
            // 获取物料名称和仓库名称
            setMaterialAndWarehouseNames(aggregationDTO, materialCode, warehouseCode);
            
            log.debug("MES数据汇总完成 - 物料编码: {}, 当前库存: {}, 理论库存: {}, 差异: {}", 
                     materialCode, aggregationDTO.getMesCurrentStock(), 
                     aggregationDTO.getTheoreticalStock(), aggregationDTO.getStockDifference());
            
        } catch (Exception e) {
            log.error("获取MES数据汇总信息失败 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode, e);
        }
        
        return aggregationDTO;
    }

    @Override
    public List<MesDataAggregationDTO> getBatchMesDataAggregation(List<String> materialCodes, String warehouseCode, Date startDate, Date endDate) {
        log.debug("批量获取MES数据汇总信息 - 物料数量: {}, 仓库编码: {}", materialCodes.size(), warehouseCode);
        
        List<MesDataAggregationDTO> result = new ArrayList<>();
        
        if (materialCodes == null || materialCodes.isEmpty()) {
            log.warn("物料编码列表为空");
            return result;
        }
        
        for (String materialCode : materialCodes) {
            try {
                MesDataAggregationDTO aggregation = getMesDataAggregation(materialCode, warehouseCode, startDate, endDate);
                result.add(aggregation);
            } catch (Exception e) {
                log.error("批量获取MES数据汇总信息失败 - 物料编码: {}", materialCode, e);
                // 继续处理其他物料，不中断整个批量操作
            }
        }
        
        log.debug("批量获取MES数据汇总信息完成 - 成功处理: {}/{}", result.size(), materialCodes.size());
        
        return result;
    }

    // 私有辅助方法

    private BigDecimal queryCurrentStockFromDatabase(String materialCode, String warehouseCode) {
        // TODO: 实现从数据库查询当前库存的逻辑
        // 这里需要根据实际的库存表结构来实现
        log.debug("查询当前库存 - 物料编码: {}, 仓库编码: {}", materialCode, warehouseCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryRiceIssuedQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询大米发料数量的逻辑
        log.debug("查询大米发料数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal querySorghumIssuedQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询高粱发料数量的逻辑
        log.debug("查询高粱发料数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryBranIssuedQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询麸皮发料数量的逻辑
        log.debug("查询麸皮发料数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal querySolidWasteQuantity(String materialCode, Date startDate, Date endDate) {
        // TODO: 实现查询固废数量的逻辑
        log.debug("查询固废数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryWeighbridgeReceiptQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询地磅收货数量的逻辑
        log.debug("查询地磅收货数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryInboundQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询入库数量的逻辑
        log.debug("查询入库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryOutboundQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询出库数量的逻辑
        log.debug("查询出库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryRiceTransferInQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询大米调拨入库数量的逻辑
        log.debug("查询大米调拨入库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal querySorghumTransferInQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询高粱调拨入库数量的逻辑
        log.debug("查询高粱调拨入库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryBranTransferInQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询麸皮调拨入库数量的逻辑
        log.debug("查询麸皮调拨入库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryFlourTransferInQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询面粉调拨入库数量的逻辑
        log.debug("查询面粉调拨入库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryRiceTransferOutQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询大米调拨出库数量的逻辑
        log.debug("查询大米调拨出库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal querySorghumTransferOutQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询高粱调拨出库数量的逻辑
        log.debug("查询高粱调拨出库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryBranTransferOutQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询麸皮调拨出库数量的逻辑
        log.debug("查询麸皮调拨出库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryFlourTransferOutQuantity(String materialCode, String warehouseCode, Date startDate, Date endDate) {
        // TODO: 实现查询面粉调拨出库数量的逻辑
        log.debug("查询面粉调拨出库数量 - 物料编码: {}", materialCode);
        return BigDecimal.ZERO;
    }

    private BigDecimal queryInitialStock(String materialCode, String warehouseCode, Date referenceDate) {
        // TODO: 实现查询期初库存的逻辑
        log.debug("查询期初库存 - 物料编码: {}, 参考日期: {}", materialCode, referenceDate);
        return BigDecimal.ZERO;
    }

    private void setMaterialAndWarehouseNames(MesDataAggregationDTO aggregationDTO, String materialCode, String warehouseCode) {
        // TODO: 实现设置物料名称和仓库名称的逻辑
        // 这里需要查询物料主数据和仓库主数据
        aggregationDTO.setMaterialName("物料名称-" + materialCode);
        aggregationDTO.setErpWarehouseName("仓库名称-" + warehouseCode);
        aggregationDTO.setUnit("吨"); // 默认单位，实际应该从物料主数据获取
    }
}