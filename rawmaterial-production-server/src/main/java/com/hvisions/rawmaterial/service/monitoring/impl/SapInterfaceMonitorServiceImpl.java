package com.hvisions.rawmaterial.service.monitoring.impl;

import com.hvisions.rawmaterial.dao.log.SapInterfaceCallLogRepository;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceCallLog;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceMetrics;
import com.hvisions.rawmaterial.entity.log.TMpdSapInterfaceCallLog;
import com.hvisions.rawmaterial.service.monitoring.SapInterfaceMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * SAP接口监控服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class SapInterfaceMonitorServiceImpl implements SapInterfaceMonitorService {
    
    @Autowired
    private SapInterfaceCallLogRepository sapInterfaceCallLogRepository;
    
    // 存储正在进行的接口调用
    private final ConcurrentHashMap<String, TMpdSapInterfaceCallLog> ongoingCalls = new ConcurrentHashMap<>();
    
    @Override
    public String recordInterfaceCallStart(String interfaceName, String requestParams) {
        String callId = UUID.randomUUID().toString();
        
        TMpdSapInterfaceCallLog callLog = new TMpdSapInterfaceCallLog();
        callLog.setCallId(callId);
        callLog.setInterfaceName(interfaceName);
        callLog.setRequestParams(requestParams);
        callLog.setStartTime(LocalDateTime.now());
        callLog.setCallStatus("STARTED");
        
        ongoingCalls.put(callId, callLog);
        
        log.debug("开始SAP接口调用监控: 接口[{}], 调用ID[{}]", interfaceName, callId);
        
        return callId;
    }
    
    @Override
    public void recordInterfaceCallEnd(String callId, boolean success, String responseData, String errorMessage) {
        TMpdSapInterfaceCallLog callLog = ongoingCalls.remove(callId);
        
        if (callLog == null) {
            log.warn("未找到对应的SAP接口调用记录: {}", callId);
            return;
        }
        
        LocalDateTime endTime = LocalDateTime.now();
        callLog.setEndTime(endTime);
        callLog.setResponseTime(java.time.Duration.between(callLog.getStartTime(), endTime).toMillis());
        callLog.setCallStatus(success ? "SUCCESS" : "FAILED");
        callLog.setResponseData(responseData);
        callLog.setErrorMessage(errorMessage);
        
        sapInterfaceCallLogRepository.save(callLog);
        
        if (success) {
            log.info("SAP接口调用成功: 接口[{}], 调用ID[{}], 响应时间[{}ms]", 
                callLog.getInterfaceName(), callId, callLog.getResponseTime());
        } else {
            log.error("SAP接口调用失败: 接口[{}], 调用ID[{}], 错误信息[{}]", 
                callLog.getInterfaceName(), callId, errorMessage);
        }
    }
    
    @Override
    public SapInterfaceMetrics getInterfaceMetrics(String interfaceName, LocalDateTime startTime, LocalDateTime endTime) {
        List<TMpdSapInterfaceCallLog> logs = sapInterfaceCallLogRepository
            .findByInterfaceNameAndStartTimeBetween(interfaceName, startTime, endTime);
        
        SapInterfaceMetrics metrics = new SapInterfaceMetrics();
        metrics.setInterfaceName(interfaceName);
        metrics.setStartTime(startTime);
        metrics.setEndTime(endTime);
        metrics.setTotalCalls(logs.size());
        
        long successCalls = logs.stream().filter(log -> "SUCCESS".equals(log.getCallStatus())).count();
        long failedCalls = logs.size() - successCalls;
        
        metrics.setSuccessCalls((int) successCalls);
        metrics.setFailedCalls((int) failedCalls);
        metrics.setSuccessRate(logs.isEmpty() ? 0.0 : (double) successCalls / logs.size() * 100);
        
        if (!logs.isEmpty()) {
            double avgResponseTime = logs.stream()
                .filter(log -> log.getResponseTime() != null)
                .mapToLong(TMpdSapInterfaceCallLog::getResponseTime)
                .average()
                .orElse(0.0);
            metrics.setAverageResponseTime(avgResponseTime);
            
            long maxResponseTime = logs.stream()
                .filter(log -> log.getResponseTime() != null)
                .mapToLong(TMpdSapInterfaceCallLog::getResponseTime)
                .max()
                .orElse(0L);
            metrics.setMaxResponseTime(maxResponseTime);
        }
        
        return metrics;
    }
    
    @Override
    public List<SapInterfaceCallLog> getInterfaceCallLogs(String interfaceName, LocalDateTime startTime, 
                                                         LocalDateTime endTime, int pageNum, int pageSize) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
        List<TMpdSapInterfaceCallLog> logs = sapInterfaceCallLogRepository
            .findByInterfaceNameAndStartTimeBetweenOrderByStartTimeDesc(interfaceName, startTime, endTime, pageable);
        
        return logs.stream().map(this::convertToDto).collect(Collectors.toList());
    }
    
    @Override
    public List<SapInterfaceCallLog> getFailedInterfaceCalls(LocalDateTime startTime, LocalDateTime endTime) {
        List<TMpdSapInterfaceCallLog> logs = sapInterfaceCallLogRepository
            .findByCallStatusAndStartTimeBetweenOrderByStartTimeDesc("FAILED", startTime, endTime);
        
        return logs.stream().map(this::convertToDto).collect(Collectors.toList());
    }
    
    @Override
    public boolean checkSapInterfaceHealth() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        LocalDateTime now = LocalDateTime.now();
        
        List<TMpdSapInterfaceCallLog> recentLogs = sapInterfaceCallLogRepository
            .findByStartTimeBetween(oneHourAgo, now);
        
        if (recentLogs.isEmpty()) {
            return true; // 没有调用记录，认为是健康的
        }
        
        long successCount = recentLogs.stream()
            .filter(log -> "SUCCESS".equals(log.getCallStatus()))
            .count();
        
        double successRate = (double) successCount / recentLogs.size();
        
        // 成功率低于80%认为不健康
        return successRate >= 0.8;
    }
    
    @Override
    public List<Double> getResponseTimeStatistics(String interfaceName, int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        List<TMpdSapInterfaceCallLog> logs = sapInterfaceCallLogRepository
            .findByInterfaceNameAndStartTimeBetween(interfaceName, startTime, endTime);
        
        return logs.stream()
            .filter(log -> log.getResponseTime() != null)
            .map(log -> log.getResponseTime().doubleValue())
            .collect(Collectors.toList());
    }
    
    private SapInterfaceCallLog convertToDto(TMpdSapInterfaceCallLog entity) {
        SapInterfaceCallLog dto = new SapInterfaceCallLog();
        dto.setCallId(entity.getCallId());
        dto.setInterfaceName(entity.getInterfaceName());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setResponseTime(entity.getResponseTime());
        dto.setCallStatus(entity.getCallStatus());
        dto.setRequestParams(entity.getRequestParams());
        dto.setResponseData(entity.getResponseData());
        dto.setErrorMessage(entity.getErrorMessage());
        return dto;
    }
}