package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.*;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理服务接口
 * @Date: 2024/07/14
 */
public interface InventoryDifferenceService {

    /**
     * 分页查询库存差异处理记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<InventoryDifferenceDTO> queryInventoryDifferences(InventoryDifferenceQueryDTO queryDTO);

    /**
     * 根据ID查询差异处理记录详情
     * @param id 差异处理ID
     * @return 差异处理记录详情
     */
    InventoryDifferenceDTO getInventoryDifferenceById(Integer id);

    /**
     * 查询待处理的差异记录
     * @return 待处理的差异记录列表
     */
    List<InventoryDifferenceDTO> getPendingDifferences();

    /**
     * 生成差异处理记录
     * @return 生成的记录数量
     */
    Integer generateDifferenceRecords();

    /**
     * 处理库存差异
     * @param processDTO 处理请求
     * @return 是否成功
     */
    Boolean processDifference(InventoryDifferenceProcessDTO processDTO, Integer userId);

    /**
     * 同步SAP库存
     * @param syncDTOList SAP库存同步数据列表
     * @return 同步成功的记录数量
     */
    Integer syncSapStock(List<SapStockSyncDTO> syncDTOList);

    /**
     * 手动同步单个物料的SAP库存
     * @param materialCode 物料编码
     * @param erpWarehouseCode ERP仓库编码
     * @return 是否成功
     */
    Boolean syncSingleSapStock(String materialCode, String erpWarehouseCode);

    /**
     * 计算差异数量
     * @param dto 差异处理记录
     * @return 计算后的差异数量
     */
    InventoryDifferenceDTO calculateDifference(InventoryDifferenceDTO dto);

    /**
     * 删除差异处理记录
     * @param id 差异处理ID
     * @return 是否成功
     */
    Boolean deleteDifference(Integer id);

    /**
     * 获取差异处理统计信息
     * @return 统计信息
     */
    InventoryDifferenceStatisticsDTO getDifferenceStatistics();

    /**
     * 导出差异处理记录
     * @param queryDTO 查询条件
     * @return 导出数据
     */
    List<InventoryDifferenceDTO> exportDifferences(InventoryDifferenceQueryDTO queryDTO);

    /**
     * 批量处理差异
     * @param processDTOList 批量处理请求列表
     * @return 处理成功的记录数量
     */
    Integer batchProcessDifferences(List<InventoryDifferenceProcessDTO> processDTOList, Integer userId);
    
    /**
     * 获取待处理差异统计信息
     * @return 待处理差异统计信息
     */
    Map<String, Object> getPendingDifferenceStatistics();
    
    /**
     * 获取已处理差异统计信息
     * @param days 统计天数
     * @return 已处理差异统计信息
     */
    Map<String, Object> getProcessedDifferenceStatistics(Integer days);
    
    /**
     * 查询历史差异记录
     * @param queryDTO 查询条件
     * @return 历史记录分页结果
     */
    Page<InventoryDifferenceDTO> queryHistoryDifferences(InventoryDifferenceQueryDTO queryDTO);
}
