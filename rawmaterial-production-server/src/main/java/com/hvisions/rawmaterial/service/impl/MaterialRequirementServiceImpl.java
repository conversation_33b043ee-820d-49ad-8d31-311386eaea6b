package com.hvisions.rawmaterial.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.client.RowClient;
import com.hvisions.brewage.dto.plan.dto.RowQueryReq;
import com.hvisions.brewage.dto.plan.vo.RowVO;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.dto.production.impurity.deal.ImpurityDealDTO;
import com.hvisions.rawmaterial.dto.production.material.requirement.BaseInfoDTO;
import com.hvisions.rawmaterial.dto.production.material.requirement.MaterialRequirementDTO;
import com.hvisions.rawmaterial.dto.production.material.requirement.MaterialRequirementPageDTO;
import com.hvisions.rawmaterial.dto.production.material.requirement.MaterialRequirementPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.OrderInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.PlanInsertDTO;
import com.hvisions.rawmaterial.consts.MaterialType;
import com.hvisions.rawmaterial.consts.ProductTypeCode;
import com.hvisions.rawmaterial.dao.*;
import com.hvisions.rawmaterial.dto.storage.Rl.LocationDetailDTO;
import com.hvisions.rawmaterial.dto.storage.Rl.LocationDetailQueryDTO;
import com.hvisions.rawmaterial.entity.*;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumProductionOrder;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder;
import com.hvisions.rawmaterial.service.*;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.SerialNumberUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.schedule.client.ShiftClient;
import com.hvisions.schedule.dto.ShiftDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description:物料需求
 * @date 2022/4/22 10:18
 */
@Slf4j
@Service
public class MaterialRequirementServiceImpl implements MaterialRequirementService {

    @Resource
    private RowClient rowClient;

    @Resource
    private MaterialRequirementMapper materialRequirementMapper;

    @Resource
    private SerialNumberUtil serialNumberUtil;

    @Resource
    private SorghumDispenseOrderMapper sorghumDispenseOrderMapper;

    @Resource
    private SorghumProductionOrderMapper sorghumProductionOrderMapper;

    @Resource
    private SorghumShipmentOrderMapper sorghumShipmentOrderMapper;

    @Resource
    private SorghumTransferOrderMapper sorghumTransferOrderMapper;

    @Resource
    private SorghumDispensePlanMapper sorghumDispensePlanMapper;

    @Resource
    private SorghumProductionPlanMapper sorghumProductionPlanMapper;

    @Resource
    private SorghumShipmentPlanMapper sorghumShipmentPlanMapper;

    @Resource
    private SorghumTransferPlanMapper sorghumTransferPlanMapper;

    @Resource
    private FlourTransferPlanMapper flourTransferPlanMapper;

    @Resource
    private FlourTransferOrderMapper flourTransferOrderMapper;

    @Resource
    private BranIssueOrderMapper branIssueOrderMapper;
    @Resource
    private BranProductionOrderMapper branProductionOrderMapper;
    @Resource
    private BranTransferOrderMapper branTransferOrderMapper;

    @Resource
    private BranIssuePlanMapper branIssuePlanMapper;
    @Resource
    private BranProductionPlanMapper branProductionPlanMapper;
    @Resource
    private BranTransferPlanMapper branTransferPlanMapper;

    @Resource
    private RiceTransferPlanMapper riceTransferPlanMapper;

    @Resource
    private RiceTransferOrderMapper riceTransferOrderMapper;
    @Resource
    private ImpurityDealService impurityDealService;

    @Resource
    private ShiftClient shiftClient;
    @Resource
    private RiceDispenseOrderMapper riceDispenseOrderMapper;

    @Resource
    private RiceDispensePlanMapper riceDispensePlanMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Override
    public Page<MaterialRequirementPageDTO> getMaterialRequirementPageList(MaterialRequirementPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(materialRequirementMapper::getMaterialRequirementPageList, queryDTO, MaterialRequirementPageDTO.class);
    }

    /*
     * @description:新增酿酒生产需求物料
     * <AUTHOR>
     * @date 2022/5/5 14:27
     * @param materialRequirementDTO 一中心需求物料，多车间需求物料
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer insertMaterialRequirement(MaterialRequirementDTO materialRequirementDTO) {

        /**
         * 新增酿酒生产需求物料：
         *      1、获取指定物料，当前时间下，基地是否存在记录
         *      2、基地物料存在则修改需求数量（累加），不存在则新增
         *      3、新增基地下的中心需求物料
         *      4、新增多条车间物料需求
         *      5、新增物料对应的计划和工单
         */
        int res = 0;
        try {
            BaseInfoDTO baseInfoDTO = materialRequirementMapper.getBaseIdByCoreId(materialRequirementDTO.getLocationId());

            if (StringUtil.isEmpty(baseInfoDTO)) {
                throw new BaseKnownException(10000, "中心id错误！");
            }

            // 新增基地要货物料需求
            LambdaQueryWrapper<TMpdMaterialRequirement> wrapper = Wrappers.<TMpdMaterialRequirement>lambdaQuery()
                    .eq(TMpdMaterialRequirement::getParentId, 0)
                    .eq(TMpdMaterialRequirement::getLocationId, baseInfoDTO.getLocationId())
                    .eq(TMpdMaterialRequirement::getMaterialId, materialRequirementDTO.getMaterialId())
                    .eq(TMpdMaterialRequirement::getMaterialCode, materialRequirementDTO.getMaterialCode())
                    .like(TMpdMaterialRequirement::getRequirementDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .orderByDesc(TMpdMaterialRequirement::getRequirementDate)
                    .last("LIMIT 1");

            TMpdMaterialRequirement materialRequirement1 = materialRequirementMapper.selectOne(wrapper);

            if (StringUtil.isEmpty(materialRequirement1)) {
                // 新增基地数据
                materialRequirement1 = new TMpdMaterialRequirement();
                materialRequirement1.setParentId(0);
                materialRequirement1.setRequirementNumber(materialRequirementDTO.getRequirementNumber());
                materialRequirement1.setLocationId(baseInfoDTO.getLocationId());
                materialRequirement1.setLocation(baseInfoDTO.getLocation());
                materialRequirement1.setRequirementDate(materialRequirementDTO.getRequirementDate());
                materialRequirement1.setMaterialId(materialRequirementDTO.getMaterialId());
                materialRequirement1.setMaterialCode(materialRequirementDTO.getMaterialCode());
                materialRequirement1.setMaterialName(materialRequirementDTO.getMaterialName());
                materialRequirement1.setUnit(materialRequirementDTO.getUnit());
                materialRequirement1.setMaterialTypeCode(materialRequirementDTO.getMaterialTypeCode());
                res += materialRequirementMapper.insert(materialRequirement1);

            } else {
                materialRequirement1.setRequirementNumber(materialRequirement1.getRequirementNumber().add(materialRequirementDTO.getRequirementNumber()));
                res += materialRequirementMapper.updateById(materialRequirement1);
            }

            // 新增中心要货物料:如果存在中心要货需求，累加数量，新增车间

            LambdaQueryWrapper<TMpdMaterialRequirement> wrapper1 = Wrappers.<TMpdMaterialRequirement>lambdaQuery()
                    .eq(TMpdMaterialRequirement::getParentId, materialRequirement1.getId())
                    .eq(TMpdMaterialRequirement::getLocationId, materialRequirementDTO.getLocationId())
                    .eq(TMpdMaterialRequirement::getMaterialId, materialRequirementDTO.getMaterialId())
                    .eq(TMpdMaterialRequirement::getMaterialCode, materialRequirementDTO.getMaterialCode())
                    .like(TMpdMaterialRequirement::getRequirementDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .orderByDesc(TMpdMaterialRequirement::getRequirementDate)
                    .last("LIMIT 1");

            TMpdMaterialRequirement materialRequirement2 = materialRequirementMapper.selectOne(wrapper1);
            if (StringUtil.isEmpty(materialRequirement2)) {
                materialRequirement2 = new TMpdMaterialRequirement();
                materialRequirement2.setLocationId(materialRequirementDTO.getLocationId());
                materialRequirement2.setLocation(materialRequirementDTO.getLocation());
                materialRequirement2.setRequirementDate(materialRequirementDTO.getRequirementDate());
                materialRequirement2.setMaterialId(materialRequirementDTO.getMaterialId());
                materialRequirement2.setMaterialCode(materialRequirementDTO.getMaterialCode());
                materialRequirement2.setMaterialName(materialRequirementDTO.getMaterialName());
                materialRequirement2.setUnit(materialRequirementDTO.getUnit());
                materialRequirement2.setVinasseData(materialRequirementDTO.getVinasseData());
                materialRequirement2.setRequirementNumber(materialRequirementDTO.getRequirementNumber());
                materialRequirement2.setParentId(materialRequirement1.getId());
                res += materialRequirementMapper.insert(materialRequirement2);
            } else {
                materialRequirement2.setRequirementNumber(materialRequirement2.getRequirementNumber().add(materialRequirementDTO.getRequirementNumber()));
                res += materialRequirementMapper.updateById(materialRequirement2);

            }

            // 新增车间要货物料
            if (materialRequirementDTO.getChildren() != null && materialRequirementDTO.getChildren().size() > 0) {

                for (MaterialRequirementDTO child : materialRequirementDTO.getChildren()) {
                    TMpdMaterialRequirement materialRequirement3 = new TMpdMaterialRequirement();
                    materialRequirement3.setLocationId(child.getLocationId());
                    materialRequirement3.setLocation(child.getLocation());
                    materialRequirement3.setRequirementDate(child.getRequirementDate());
                    materialRequirement3.setMaterialId(child.getMaterialId());
                    materialRequirement3.setMaterialCode(child.getMaterialCode());
                    materialRequirement3.setMaterialName(child.getMaterialName());
                    materialRequirement3.setUnit(child.getUnit());
                    materialRequirement3.setRequirementNumber(child.getRequirementNumber());
                    materialRequirement3.setFormulationAmount(child.getFormulationAmount());
                    materialRequirement3.setSourceCode(child.getSourceCode());
                    materialRequirement3.setVinasseData(child.getVinasseData());
                    materialRequirement3.setParentId(materialRequirement2.getId());
                    res += materialRequirementMapper.insert(materialRequirement3);
                }
            }

            // 新增计划工单
            res += insertPlanAndOrder(materialRequirementDTO);

        } catch (Exception e) {
            throw new BaseKnownException(10000, "新增酿酒生产物料需求失败：" + e.getMessage());
        }
        return res;
    }

    /*
     * @description:
     * <AUTHOR>
     * @date 2022/6/7 16:27
     * @param startTime
     * @param endTime
     * @return java.lang.Boolean
     */
    public Boolean isJX(String startTime, String endTime) throws ParseException {

        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_YEAR, 7);
        Date time = calendar.getTime();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(time);
        Date now = simpleDateFormat.parse(format);
        Date start = simpleDateFormat.parse(startTime);
        Date end = simpleDateFormat.parse(endTime);

        if (now.getTime() == start.getTime() || now.getTime() == end.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(now);

        Calendar b = Calendar.getInstance();
        b.setTime(start);

        Calendar e = Calendar.getInstance();
        e.setTime(end);

        if (date.after(b) && date.before(e)) {
            return true;
        } else {
            return false;
        }
    }


    /*
     * @description: 新增计划工单
     * <AUTHOR>
     * @date 2022/5/5 15:01
     * @param materialRequirementDTO
     * @return java.lang.Integer
     */
    private Integer insertPlanAndOrder(MaterialRequirementDTO materialRequirementDTO) throws ParseException {
        int res = 0;

        /**
         * 1、判断是否是检修，不是检修，需要将筒仓填满到安全库存，是检修，要多少发多少，不需要填满筒仓
         * 2、根据不同物料生成不同计划工单
         * 3、根据当前日期，+7天，判断日期是否在检修日期范围内，如果在为检修，否则正常生产
         *
         */

        boolean isJX = false;  // 是否是检修
        // 获取检修日期
        RowQueryReq rowQueryReq = new RowQueryReq();
        PageInfo pageInfo = new PageInfo();
        rowQueryReq.setCheckState(true);
        rowQueryReq.setRowState("1");
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<RowVO> rowVOPage = rowClient.queryPagedRow(rowQueryReq, pageInfo).getData();
        log.info("检修区间：---》", rowVOPage);
        List<RowVO> records = rowVOPage.getRecords();
        if (records != null && records.size() > 0) {
            RowVO rowVO = records.get(0);
            String rowBeginTime = rowVO.getRowBeginTime();
            String rowEndTime = rowVO.getRowEndTime();
            isJX = isJX(rowBeginTime, rowEndTime);
        }
        log.info("是否是检修：---》", isJX);

        PlanInsertDTO planInsertDTO = new PlanInsertDTO();
        planInsertDTO.setSchedulingType(isJX ? ProductTypeCode.OVERHAUL : ProductTypeCode.NORMAL);
        planInsertDTO.setPlanDate(materialRequirementDTO.getRequirementDate());
        planInsertDTO.setMaterialId(materialRequirementDTO.getMaterialId());
        planInsertDTO.setMaterialCode(materialRequirementDTO.getMaterialCode());
        planInsertDTO.setMaterialName(materialRequirementDTO.getMaterialName());
        planInsertDTO.setUnit(materialRequirementDTO.getUnit());
        planInsertDTO.setCreateTime(new Date());
        planInsertDTO.setUpdateTime(new Date());

        log.info("计划新增dto：---》", planInsertDTO);

        OrderInsertDTO orderInsertDTO = new OrderInsertDTO();
        orderInsertDTO.setOrderDate(materialRequirementDTO.getRequirementDate());

        orderInsertDTO.setCenterId(materialRequirementDTO.getLocationId());
        orderInsertDTO.setCenter(materialRequirementDTO.getLocation());
        orderInsertDTO.setMaterialId(materialRequirementDTO.getMaterialId());
        orderInsertDTO.setMaterialCode(materialRequirementDTO.getMaterialCode());
        orderInsertDTO.setMaterialName(materialRequirementDTO.getMaterialName());
        orderInsertDTO.setUnit(materialRequirementDTO.getUnit());
        orderInsertDTO.setProductionTypeCode(isJX ? ProductTypeCode.OVERHAUL : ProductTypeCode.NORMAL);
        orderInsertDTO.setCreateTime(new Date());
        orderInsertDTO.setUpdateTime(new Date());
        String serialNumber = "";
        log.info("当前工单新增dto：---》", orderInsertDTO);

        // 库位库存数量和安全库存数量
        List<LocationDetailDTO> locationList = rlManagementService.getLocationList(new LocationDetailQueryDTO());

        // 中心
        String center = materialRequirementDTO.getLocation();

        // 计划数量
        BigDecimal planQuantity = materialRequirementDTO.getRequirementNumber();


        if (materialRequirementDTO.getMaterialTypeCode().equals(MaterialType.SORGHUM)) { // 高粱

// 高粱粉分发

            log.info("高粱粉分发开始：-----------------");
            // 存在高粱粉分发计划，则累加分发数量，不存在则新增
            LambdaQueryWrapper<TMpdSorghumDispensePlan> wrapper = Wrappers.<TMpdSorghumDispensePlan>lambdaQuery()
                    .like(TMpdSorghumDispensePlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdSorghumDispensePlan::getDeleted, 0)
                    .orderByDesc(TMpdSorghumDispensePlan::getPlanDate)
                    .last("LIMIT 1");

            TMpdSorghumDispensePlan sorghumDispensePlan = sorghumDispensePlanMapper.selectOne(wrapper);

            // 所有发放仓库存
            BigDecimal allDispenseStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().indexOf("GLZC-") > -1 && item.getName().indexOf("高粱粉发放") > -1)
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 当前中心发放仓库存
            BigDecimal dispenseStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("GLZC-" + center) && item.getName().indexOf("高粱粉发放") > -1)
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 当前中心发放仓安全库存
            BigDecimal dispenseSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("GLZC-" + center) && item.getName().indexOf("高粱粉发放") > -1)
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (StringUtil.isNotEmpty(sorghumDispensePlan)) {
                sorghumDispensePlan.setPlanQuantity(sorghumDispensePlan.getPlanQuantity().add(planQuantity));
                sorghumDispensePlan.setInventoryQuantity(allDispenseStockQuantity);
                res += sorghumDispensePlanMapper.updateById(sorghumDispensePlan);

            } else {
                sorghumDispensePlan = DtoMapper.convert(planInsertDTO, TMpdSorghumDispensePlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_dispense_plan", 3, true);
                sorghumDispensePlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                sorghumDispensePlan.setPlanQuantity(planQuantity);
                sorghumDispensePlan.setInventoryQuantity(allDispenseStockQuantity);
                res += sorghumDispensePlanMapper.insert(sorghumDispensePlan);
            }

            // 新增工单:判断计划下的是否已经存在当前中心的工单
            TMpdSorghumDispenseOrder sorghumDispenseOrder = sorghumDispenseOrderMapper.selectOne(Wrappers.<TMpdSorghumDispenseOrder>lambdaQuery()
                    .eq(TMpdSorghumDispenseOrder::getCenterId, orderInsertDTO.getCenterId())
                    .eq(TMpdSorghumDispenseOrder::getDeleted, 0)
                    .eq(TMpdSorghumDispenseOrder::getPlanId, sorghumDispensePlan.getId())
                    .orderByDesc(TMpdSorghumDispenseOrder::getOrderDate)
                    .last("LIMIT 1")
            );
            if (StringUtil.isEmpty(sorghumDispenseOrder)) {
                sorghumDispenseOrder = DtoMapper.convert(orderInsertDTO, TMpdSorghumDispenseOrder.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_dispense_order", 3, true);
                sorghumDispenseOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                sorghumDispenseOrder.setPlanId(sorghumDispensePlan.getId());
                sorghumDispenseOrder.setPlanQuantity(planQuantity);
                sorghumDispenseOrder.setInventoryQuantity(dispenseStockQuantity);
                res += sorghumDispenseOrderMapper.insert(sorghumDispenseOrder);
            } else {
                sorghumDispenseOrder.setPlanQuantity(sorghumDispenseOrder.getPlanQuantity().add(planQuantity));
                sorghumDispenseOrder.setInventoryQuantity(dispenseStockQuantity);
                res += sorghumDispenseOrderMapper.updateById(sorghumDispenseOrder);
            }

            log.info("高粱分发结束：------------------");


// 高粱粉转运

            log.info("高粱粉转运开始：-----------------");

            // 新增修改计划
            LambdaQueryWrapper<TMpdFlourTransferPlan> wrapper4 = Wrappers.<TMpdFlourTransferPlan>lambdaQuery()
                    .like(TMpdFlourTransferPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdFlourTransferPlan::getDeleted, 0)
                    .orderByDesc(TMpdFlourTransferPlan::getPlanDate)
                    .last("LIMIT 1");

            TMpdFlourTransferPlan flourTransferPlan = flourTransferPlanMapper.selectOne(wrapper4);

            //所有粉仓库存数量
            BigDecimal allFlourStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getCode().equals("709F") || item.getCode().equals("713F") || item.getCode().equals("719F"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 该中心的粉仓库存数量
            BigDecimal flourStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getCode().equals(center + "F"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal flourSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getCode().equals(center + "F"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (center.equals("709") || center.equals("713") || center.equals("718")) {
                if (isJX) {
                    // 计划数量 - 库存数量
                    planQuantity = planQuantity.subtract(dispenseSafetyQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.subtract(dispenseSafetyQuantity) : new BigDecimal(0);
                } else {
                    // 计划数量 + 安全库存数量 - 库存数量
                    planQuantity = planQuantity.add(dispenseSafetyQuantity).subtract(dispenseStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.add(dispenseSafetyQuantity).subtract(dispenseStockQuantity) : new BigDecimal(0);
                }
            }

            if (StringUtil.isNotEmpty(flourTransferPlan)) {
                flourTransferPlan.setInventoryQuantity(allFlourStockQuantity);
                flourTransferPlan.setPlanQuantity(flourTransferPlan.getPlanQuantity().add(planQuantity));
                res += flourTransferPlanMapper.updateById(flourTransferPlan);

            } else {
                flourTransferPlan = DtoMapper.convert(planInsertDTO, TMpdFlourTransferPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_flour_transfer_plan", 3, true);
                flourTransferPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                flourTransferPlan.setInventoryQuantity(allFlourStockQuantity);
                flourTransferPlan.setPlanQuantity(planQuantity);
                res += flourTransferPlanMapper.insert(flourTransferPlan);
            }
            TMpdFlourTransferOrder flourTransferOrder = flourTransferOrderMapper.selectOne(Wrappers.<TMpdFlourTransferOrder>lambdaQuery()
                    .eq(TMpdFlourTransferOrder::getCenterId, orderInsertDTO.getCenterId())
                    .eq(TMpdFlourTransferOrder::getDeleted, 0)
                    .eq(TMpdFlourTransferOrder::getPlanId, flourTransferPlan.getId())
                    .orderByDesc(TMpdFlourTransferOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(flourTransferOrder)) {
                flourTransferOrder = DtoMapper.convert(orderInsertDTO, TMpdFlourTransferOrder.class);

                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_flour_transfer_order", 3, true);
                flourTransferOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                flourTransferOrder.setPlanId(flourTransferPlan.getId());
                flourTransferOrder.setPlanQuantity(planQuantity);
                flourTransferOrder.setInventoryQuantity(flourStockQuantity);
                res += flourTransferOrderMapper.insert(flourTransferOrder);
            } else {
                flourTransferOrder.setPlanQuantity(flourTransferOrder.getPlanQuantity().add(planQuantity));
                flourTransferOrder.setInventoryQuantity(flourStockQuantity);
                res += flourTransferOrderMapper.updateById(flourTransferOrder);
            }


            log.info("高粱粉转运开始结束：-----------------");


// 高粱生产

            log.info("高粱生产开始：------------------");
            // 新增修改计划
            LambdaQueryWrapper<TMpdSorghumProductionPlan> wrapper1 = Wrappers.<TMpdSorghumProductionPlan>lambdaQuery()
                    .like(TMpdSorghumProductionPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdSorghumProductionPlan::getDeleted, 0)
                    .orderByDesc(TMpdSorghumProductionPlan::getPlanDate)
                    .last("LIMIT 1");
            TMpdSorghumProductionPlan sorghumProductionPlan = sorghumProductionPlanMapper.selectOne(wrapper1);

            // 获取计划的当前库存数量
            BigDecimal allProductionStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().indexOf("GLZC-") > -1 && item.getName().indexOf("高粱暂存仓") > -1)
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 获取工单的当前库存数量
            BigDecimal productionStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getCode().equals(center + "" + "1") || item.getCode().equals(center + "" + "2") || item.getCode().equals(center + "" + "2"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal productionSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getCode().equals(center + "" + "1") || item.getCode().equals(center + "" + "2") || item.getCode().equals(center + "" + "2"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (center.equals("709") || center.equals("713") || center.equals("718")) {
                if (isJX) {
                    // 计划数量 - 库存数量
                    planQuantity = planQuantity.subtract(flourStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.subtract(flourStockQuantity) : new BigDecimal(0);
                } else {
                    // 计划数量 + 安全库存数量 - 库存数量
                    planQuantity = planQuantity.add(flourSafetyQuantity).subtract(flourStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.add(flourSafetyQuantity).subtract(flourStockQuantity) : new BigDecimal(0);
                }
            }

            if (StringUtil.isNotEmpty(sorghumProductionPlan)) {
                sorghumProductionPlan.setPlanQuantity(sorghumProductionPlan.getPlanQuantity().add(planQuantity));
                sorghumProductionPlan.setInventoryQuantity(allProductionStockQuantity);
                res += sorghumProductionPlanMapper.updateById(sorghumProductionPlan);

            } else {

                sorghumProductionPlan = DtoMapper.convert(planInsertDTO, TMpdSorghumProductionPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_production_plan", 3, true);
                sorghumProductionPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                sorghumProductionPlan.setPlanQuantity(planQuantity);
                sorghumProductionPlan.setInventoryQuantity(allProductionStockQuantity);
                res += sorghumProductionPlanMapper.insert(sorghumProductionPlan);
            }
            TMpdSorghumProductionOrder sorghumProductionOrder = sorghumProductionOrderMapper.selectOne(Wrappers.<TMpdSorghumProductionOrder>lambdaQuery()
                    .eq(TMpdSorghumProductionOrder::getCenterId, orderInsertDTO.getCenterId())
                    .eq(TMpdSorghumProductionOrder::getDeleted, 0)
                    .eq(TMpdSorghumProductionOrder::getPlanId, sorghumProductionPlan.getId())
                    .orderByDesc(TMpdSorghumProductionOrder::getOrderDate)
                    .last("LIMIT 1")
            );
            if (StringUtil.isEmpty(sorghumProductionOrder)) {
                sorghumProductionOrder = DtoMapper.convert(orderInsertDTO, TMpdSorghumProductionOrder.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_production_order", 3, true);
                sorghumProductionOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                sorghumProductionOrder.setPlanId(sorghumProductionPlan.getId());
                sorghumProductionOrder.setPlanQuantity(planQuantity);
                sorghumProductionOrder.setInventoryQuantity(productionStockQuantity);
                res += sorghumProductionOrderMapper.insert(sorghumProductionOrder);

            } else {
                sorghumProductionOrder.setPlanQuantity(sorghumProductionOrder.getPlanQuantity().add(planQuantity));
                sorghumProductionOrder.setInventoryQuantity(productionStockQuantity);
                res += sorghumProductionOrderMapper.updateById(sorghumProductionOrder);
            }

            log.info("高粱生产结束：----------------------");


// 高粱转粮

            log.info("高粱转粮开始：----------------------");

            // 新增修改计划
            LambdaQueryWrapper<TMpdSorghumShipmentPlan> wrapper2 = Wrappers.<TMpdSorghumShipmentPlan>lambdaQuery()
                    .like(TMpdSorghumShipmentPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdSorghumShipmentPlan::getDeleted, 0)
                    .orderByDesc(TMpdSorghumShipmentPlan::getPlanDate)
                    .last("LIMIT 1");
            TMpdSorghumShipmentPlan sorghumShipmentPlan = sorghumShipmentPlanMapper.selectOne(wrapper2);

            // 获取计划的当前库存数量
            BigDecimal allShipmentStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("GL01"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal shipmentSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("GL01"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (center.equals("709") || center.equals("713") || center.equals("718")) {
                if (isJX) {
                    // 计划数量 - 库存数量
                    planQuantity = planQuantity.subtract(productionStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.subtract(productionStockQuantity) : new BigDecimal(0);
                } else {
                    // 计划数量 + 安全库存数量 - 库存数量
                    planQuantity = planQuantity.add(productionSafetyQuantity).subtract(productionStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.add(productionStockQuantity).subtract(productionStockQuantity) : new BigDecimal(0);
                }
            }

            if (StringUtil.isNotEmpty(sorghumShipmentPlan)) {
                sorghumShipmentPlan.setPlanQuantity(sorghumShipmentPlan.getPlanQuantity().add(planQuantity));
                sorghumShipmentPlan.setInventoryQuantity(allShipmentStockQuantity);
                res += sorghumShipmentPlanMapper.updateById(sorghumShipmentPlan);

            } else {

                sorghumShipmentPlan = DtoMapper.convert(planInsertDTO, TMpdSorghumShipmentPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_shipment_plan", 3, true);
                sorghumShipmentPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                sorghumShipmentPlan.setPlanQuantity(planQuantity);
                sorghumShipmentPlan.setInventoryQuantity(allShipmentStockQuantity);
                res += sorghumShipmentPlanMapper.insert(sorghumShipmentPlan);
            }
            TMpdSorghumShipmentOrder sorghumShipmentOrder = sorghumShipmentOrderMapper.selectOne(Wrappers.<TMpdSorghumShipmentOrder>lambdaQuery()
                    .eq(TMpdSorghumShipmentOrder::getDeleted, 0)
                    //.eq(TMpdSorghumShipmentOrder::getCenterId, orderInsertDTO.getCenterId())
                    //.eq(TMpdSorghumShipmentOrder::getPlanId, sorghumShipmentPlan.getId())
                    .orderByDesc(TMpdSorghumShipmentOrder::getOrderDate)
                    .last("LIMIT 1")
            );

            if (StringUtil.isEmpty(sorghumShipmentOrder)) {
                sorghumShipmentOrder = DtoMapper.convert(orderInsertDTO, TMpdSorghumShipmentOrder.class);

                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_shipment_order", 3, true);
                sorghumShipmentOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                //sorghumShipmentOrder.setPlanId(sorghumShipmentPlan.getId());
                //sorghumShipmentOrder.setPlanQuantity(planQuantity);
                sorghumShipmentOrder.setInventoryQuantity(allShipmentStockQuantity);

                res += sorghumShipmentOrderMapper.insert(sorghumShipmentOrder);
            } else {
                //sorghumShipmentOrder.setPlanQuantity(sorghumShipmentOrder.getPlanQuantity().add(planQuantity));
                sorghumShipmentOrder.setInventoryQuantity(allShipmentStockQuantity);

                res += sorghumShipmentOrderMapper.updateById(sorghumShipmentOrder);
            }


            log.info("高粱转粮结束：---------------------");


// 高粱转运

            log.info("高粱转运开始：-------------------");

            // 新增修改计划
            LambdaQueryWrapper<TMpdSorghumTransferPlan> wrapper3 = Wrappers.<TMpdSorghumTransferPlan>lambdaQuery()
                    .like(TMpdSorghumTransferPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdSorghumTransferPlan::getDeleted, 0)
                    .orderByDesc(TMpdSorghumTransferPlan::getPlanDate)
                    .last("LIMIT 1");

            TMpdSorghumTransferPlan sorghumTransferPlan = sorghumTransferPlanMapper.selectOne(wrapper3);

            // 获取计划的当前库存数量
            BigDecimal allTransferStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("GL02"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal transferSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("GL02"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (center.equals("709") || center.equals("713") || center.equals("718")) {
                if (isJX) {
                    // 计划数量 - 库存数量
                    planQuantity = planQuantity.subtract(allShipmentStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.subtract(allShipmentStockQuantity) : new BigDecimal(0);
                } else {
                    // 计划数量 + 安全库存数量 - 库存数量
                    planQuantity = planQuantity.add(shipmentSafetyQuantity).subtract(allShipmentStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.add(shipmentSafetyQuantity).subtract(allShipmentStockQuantity) : new BigDecimal(0);
                }

            }

            if (StringUtil.isNotEmpty(sorghumTransferPlan)) {
                sorghumTransferPlan.setPlanQuantity(sorghumTransferPlan.getPlanQuantity().add(planQuantity));
                sorghumTransferPlan.setInventoryQuantity(allTransferStockQuantity);
                res += sorghumTransferPlanMapper.updateById(sorghumTransferPlan);

            } else {
                sorghumTransferPlan = DtoMapper.convert(planInsertDTO, TMpdSorghumTransferPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_transfer_plan", 3, true);
                sorghumTransferPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                sorghumTransferPlan.setPlanQuantity(planQuantity);
                sorghumTransferPlan.setInventoryQuantity(allTransferStockQuantity);
                res += sorghumTransferPlanMapper.insert(sorghumTransferPlan);
            }
            TMpdSorghumTransferOrder sorghumTransferOrder = sorghumTransferOrderMapper.selectOne(Wrappers.<TMpdSorghumTransferOrder>lambdaQuery()
                    //.eq(TMpdSorghumTransferOrder::getPlanId, sorghumTransferPlan.getId())
                    .eq(TMpdSorghumTransferOrder::getDeleted, 0)
                    .orderByDesc(TMpdSorghumTransferOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(sorghumTransferOrder)) {
                sorghumTransferOrder = DtoMapper.convert(orderInsertDTO, TMpdSorghumTransferOrder.class);

                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_sorghum_transfer_order", 3, true);
                sorghumTransferOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                //sorghumTransferOrder.setPlanId(sorghumTransferPlan.getId());
                //sorghumTransferOrder.setPlanQuantity(planQuantity);
                sorghumTransferOrder.setInventoryQuantity(allTransferStockQuantity);
                res += sorghumTransferOrderMapper.insert(sorghumTransferOrder);
            } else {
                //sorghumTransferOrder.setPlanQuantity(sorghumTransferOrder.getPlanQuantity().add(planQuantity));
                sorghumTransferOrder.setInventoryQuantity(allTransferStockQuantity);
                res += sorghumTransferOrderMapper.updateById(sorghumTransferOrder);
            }

            log.info("高粱转运结束：--------------------");


        } else if (materialRequirementDTO.getMaterialTypeCode().equals(MaterialType.BRAN)) { // 稻壳

// 熟糠分发

            log.info("熟糠分发开始：-----------------");
            // 新增修改计划
            LambdaQueryWrapper<TMpdBranIssuePlan> wrapper1 = Wrappers.<TMpdBranIssuePlan>lambdaQuery()
                    .like(TMpdBranIssuePlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdBranIssuePlan::getDeleted, 0)
                    .orderByDesc(TMpdBranIssuePlan::getPlanDate)
                    .last("LIMIT 1");
            TMpdBranIssuePlan branIssuePlan = branIssuePlanMapper.selectOne(wrapper1);

            // 获取所有发放仓当前前库存数量
            BigDecimal allIssueStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().indexOf("SKZC-") > -1)
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 获取当前中心发放仓当前库存数量
            BigDecimal issueStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("SKZC-" + center))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 当前中心发放仓的安全库存
            BigDecimal issueSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("SKZC-" + center))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (StringUtil.isNotEmpty(branIssuePlan)) {
                branIssuePlan.setPlanQuantity(branIssuePlan.getPlanQuantity().add(planQuantity));
                branIssuePlan.setInventoryQuantity(allIssueStockQuantity);
                res += branIssuePlanMapper.updateById(branIssuePlan);

            } else {
                branIssuePlan = DtoMapper.convert(planInsertDTO, TMpdBranIssuePlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_bran_issue_plan", 3, true);
                branIssuePlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                branIssuePlan.setPlanQuantity(planQuantity);
                branIssuePlan.setInventoryQuantity(allIssueStockQuantity);
                res += branIssuePlanMapper.insert(branIssuePlan);
            }

            TMpdBranIssueOrder branIssueOrder = branIssueOrderMapper.selectOne(Wrappers.<TMpdBranIssueOrder>lambdaQuery()
                    .eq(TMpdBranIssueOrder::getCenterId, orderInsertDTO.getCenterId())
                    .eq(TMpdBranIssueOrder::getDeleted, 0)
                    .eq(TMpdBranIssueOrder::getPlanId, branIssuePlan.getId())
                    .orderByDesc(TMpdBranIssueOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(branIssueOrder)) {
                branIssueOrder = DtoMapper.convert(orderInsertDTO, TMpdBranIssueOrder.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_bran_issue_order", 3, true);
                branIssueOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                branIssueOrder.setPlanId(branIssuePlan.getId());
                branIssueOrder.setPlanQuantity(planQuantity);
                branIssueOrder.setInventoryQuantity(issueStockQuantity);

                res += branIssueOrderMapper.insert(branIssueOrder);
            } else {
                branIssueOrder.setPlanQuantity(branIssueOrder.getPlanQuantity().add(planQuantity));
                branIssueOrder.setInventoryQuantity(issueStockQuantity);
                res += branIssueOrderMapper.updateById(branIssueOrder);
            }

            log.info("熟糠分发结束：-----------------");

// 熟糠传输

            log.info("熟糠传输开始：-----------------");
            // 新增修改计划
            LambdaQueryWrapper<TMpdBranTransferPlan> wrapper3 = Wrappers.<TMpdBranTransferPlan>lambdaQuery()
                    .like(TMpdBranTransferPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdBranTransferPlan::getDeleted, 0)
                    .orderByDesc(TMpdBranTransferPlan::getPlanDate)
                    .last("LIMIT 1");
            TMpdBranTransferPlan branTransferPlan = branTransferPlanMapper.selectOne(wrapper3);

            // 获取计划的当前库存数量
            BigDecimal allTransferStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("SKCK"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal transferSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("SKCK"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (center.equals("709") || center.equals("713") || center.equals("718")) {
                if (isJX) {
                    // 计划数量 - 库存数量
                    planQuantity = planQuantity.subtract(issueStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.subtract(issueStockQuantity) : new BigDecimal(0);
                } else {
                    // 计划数量 + 安全库存数量 - 库存数量
                    planQuantity = planQuantity.add(issueSafetyQuantity).subtract(issueStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.add(issueSafetyQuantity).subtract(issueStockQuantity) : new BigDecimal(0);
                }
            }

            if (StringUtil.isNotEmpty(branTransferPlan)) {
                branTransferPlan.setInventoryQuantity(allTransferStockQuantity);
                branTransferPlan.setPlanQuantity(branTransferPlan.getPlanQuantity().add(planQuantity));
                res += branTransferPlanMapper.updateById(branTransferPlan);

            } else {
                branTransferPlan = DtoMapper.convert(planInsertDTO, TMpdBranTransferPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_bran_production_plan", 3, true);
                branTransferPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                branTransferPlan.setInventoryQuantity(allTransferStockQuantity);
                branTransferPlan.setPlanQuantity(planQuantity);

                res += branTransferPlanMapper.insert(branTransferPlan);
            }

            TMpdBranTransferOrder branTransferOrder = branTransferOrderMapper.selectOne(Wrappers.<TMpdBranTransferOrder>lambdaQuery()
                    .eq(TMpdBranTransferOrder::getCenterId, orderInsertDTO.getCenterId())
                    .eq(TMpdBranTransferOrder::getDeleted, 0)
                    .eq(TMpdBranTransferOrder::getPlanId, branTransferPlan.getId())
                    .orderByDesc(TMpdBranTransferOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(branTransferOrder)) {
                branTransferOrder = DtoMapper.convert(orderInsertDTO, TMpdBranTransferOrder.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_bran_transfer_order", 3, true);
                branTransferOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                branTransferOrder.setPlanId(branTransferPlan.getId());
                branTransferOrder.setPlanQuantity(planQuantity);
                branTransferOrder.setInventoryQuantity(allTransferStockQuantity);
                res += branTransferOrderMapper.insert(branTransferOrder);
            } else {
                branTransferOrder.setPlanQuantity(branTransferOrder.getPlanQuantity().add(planQuantity));
                branTransferOrder.setInventoryQuantity(allTransferStockQuantity);
                res += branTransferOrderMapper.updateById(branTransferOrder);
            }

            log.info("熟糠传输结束：-----------------");


// 熟糠生产

            log.info("熟糠生产开始：-----------------");
            // 新增修改计划
            LambdaQueryWrapper<TMpdBranProductionPlan> wrapper2 = Wrappers.<TMpdBranProductionPlan>lambdaQuery()
                    .like(TMpdBranProductionPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdBranProductionPlan::getDeleted, 0)
                    .orderByDesc(TMpdBranProductionPlan::getPlanDate)
                    .last("LIMIT 1");

            TMpdBranProductionPlan branProductionPlan = branProductionPlanMapper.selectOne(wrapper2);

            if (center.equals("709") || center.equals("713") || center.equals("718")) {
                if (isJX) {
                    // 计划数量 - 库存数量
                    planQuantity = planQuantity.subtract(allTransferStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.subtract(allTransferStockQuantity) : new BigDecimal(0);
                } else {
                    // 计划数量 + 安全库存数量 - 库存数量
                    planQuantity = planQuantity.add(transferSafetyQuantity).subtract(allTransferStockQuantity).compareTo(new BigDecimal(0)) > -1 ? planQuantity.add(transferSafetyQuantity).subtract(allTransferStockQuantity) : new BigDecimal(0);
                }
            }

            if (StringUtil.isNotEmpty(branProductionPlan)) {
                branProductionPlan.setPlanQuantity(branProductionPlan.getPlanQuantity().add(planQuantity));
                res += branProductionPlanMapper.updateById(branProductionPlan);

            } else {
                branProductionPlan = DtoMapper.convert(planInsertDTO, TMpdBranProductionPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_bran_production_plan", 3, true);
                branProductionPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                branProductionPlan.setPlanQuantity(planQuantity);
                res += branProductionPlanMapper.insert(branProductionPlan);
            }
            TMpdBranProductionOrder branProductionOrder = branProductionOrderMapper.selectOne(Wrappers.<TMpdBranProductionOrder>lambdaQuery()
                    .eq(TMpdBranProductionOrder::getDeleted, 0)
                    .eq(TMpdBranProductionOrder::getPlanId, branProductionPlan.getId())
                    .orderByDesc(TMpdBranProductionOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(branProductionOrder)) {
                branProductionOrder = DtoMapper.convert(orderInsertDTO, TMpdBranProductionOrder.class);

                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_bran_production_order", 3, true);
                branProductionOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                branProductionOrder.setPlanId(branProductionPlan.getId());
                branProductionOrder.setPlanQuantity(planQuantity);

                res += branProductionOrderMapper.insert(branProductionOrder);
                //自动新增蒸糠杂质处理
                InsertImpurityDealAuto(branProductionOrder);


            } else {
                branProductionOrder.setPlanQuantity(branProductionOrder.getPlanQuantity().add(planQuantity));
                res += branProductionOrderMapper.updateById(branProductionOrder);
            }
            log.info("熟糠生产结束：-----------------");


// 稻壳分发

            log.info("稻壳分发开始：-----------------");

            // 新增修改计划
            LambdaQueryWrapper<TMpdRiceDispensePlan> wrapper = Wrappers.<TMpdRiceDispensePlan>lambdaQuery()
                    .like(TMpdRiceDispensePlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdRiceDispensePlan::getDeleted, 0)
                    .orderByDesc(TMpdRiceDispensePlan::getPlanDate)
                    .last("LIMIT 1");
            TMpdRiceDispensePlan riceDispensePlan = riceDispensePlanMapper.selectOne(wrapper);

            // 一期筒仓库存数量
            BigDecimal allDispenseStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("DK01"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 一期安全库存
            BigDecimal dispenseSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("DK01"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (StringUtil.isNotEmpty(riceDispensePlan)) {
                riceDispensePlan.setPlanQuantity(riceDispensePlan.getPlanQuantity().add(planQuantity));
                riceDispensePlan.setInventoryQuantity(allDispenseStockQuantity);
                res += riceDispensePlanMapper.updateById(riceDispensePlan);

            } else {
                riceDispensePlan = DtoMapper.convert(planInsertDTO, TMpdRiceDispensePlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_rice_dispense_plan", 3, true);
                riceDispensePlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                riceDispensePlan.setPlanQuantity(planQuantity);
                riceDispensePlan.setInventoryQuantity(allDispenseStockQuantity);
                res += riceDispensePlanMapper.insert(riceDispensePlan);
            }

            // 新增工单
            TMpdRiceDispenseOrder riceDispenseOrder = riceDispenseOrderMapper.selectOne(Wrappers.<TMpdRiceDispenseOrder>lambdaQuery()
                    .eq(TMpdRiceDispenseOrder::getDeleted, 0)
                    .eq(TMpdRiceDispenseOrder::getPlanId, riceDispensePlan.getId())
                    .orderByDesc(TMpdRiceDispenseOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(riceDispenseOrder)) {
                riceDispenseOrder = DtoMapper.convert(orderInsertDTO, TMpdRiceDispenseOrder.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_rice_dispense_order", 3, true);
                riceDispenseOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                riceDispenseOrder.setPlanId(riceDispensePlan.getId());
                riceDispenseOrder.setPlanQuantity(planQuantity);
                riceDispenseOrder.setInventoryQuantity(allDispenseStockQuantity);
                res += riceDispenseOrderMapper.insert(riceDispenseOrder);
            } else {
                riceDispenseOrder.setPlanQuantity(riceDispenseOrder.getPlanQuantity().add(planQuantity));
                riceDispenseOrder.setInventoryQuantity(allDispenseStockQuantity);
                res += riceDispenseOrderMapper.updateById(riceDispenseOrder);
            }

            log.info("稻壳分发结束：------------------");


// 稻壳转运

            log.info("稻壳转运开始：-----------------");
            // 新增修改计划
            LambdaQueryWrapper<TMpdRiceTransferPlan> wrapper4 = Wrappers.<TMpdRiceTransferPlan>lambdaQuery()
                    .like(TMpdRiceTransferPlan::getPlanDate, DateUtil.format(materialRequirementDTO.getRequirementDate(), "yyyy-MM-dd"))
                    .eq(TMpdRiceTransferPlan::getDeleted, 0)
                    .orderByDesc(TMpdRiceTransferPlan::getPlanDate)
                    .last("LIMIT 1");

            TMpdRiceTransferPlan riceTransferPlan = riceTransferPlanMapper.selectOne(wrapper4);

            // 二期筒仓库存数量
            BigDecimal allRiceTransferStockQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("DK02"))
                    .map(LocationDetailDTO::getStockQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 二期筒仓安全库存
            BigDecimal riceTransferSafetyQuantity = locationList
                    .stream()
                    .filter(item -> item.getParentCode().equals("DK02"))
                    .map(LocationDetailDTO::getSafetyStock)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (StringUtil.isNotEmpty(riceTransferPlan)) {
                riceTransferPlan.setInventoryQuantity(allRiceTransferStockQuantity);
                riceTransferPlan.setPlanQuantity(riceTransferPlan.getPlanQuantity().add(planQuantity));
                res += riceTransferPlanMapper.updateById(riceTransferPlan);

            } else {
                riceTransferPlan = DtoMapper.convert(planInsertDTO, TMpdRiceTransferPlan.class);
                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_rice_transfer_plan", 3, true);
                riceTransferPlan.setPlanNo("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                riceTransferPlan.setInventoryQuantity(allRiceTransferStockQuantity);
                riceTransferPlan.setPlanQuantity(planQuantity);
                res += riceTransferPlanMapper.insert(riceTransferPlan);
            }
            TMpdRiceTransferOrder riceTransferOrder = riceTransferOrderMapper.selectOne(Wrappers.<TMpdRiceTransferOrder>lambdaQuery()
                    .eq(TMpdRiceTransferOrder::getDeleted, 0)
                    .eq(TMpdRiceTransferOrder::getPlanId, riceTransferPlan.getId())
                    .orderByDesc(TMpdRiceTransferOrder::getOrderDate)
                    .last("LIMIT 1"));
            if (StringUtil.isEmpty(riceTransferOrder)) {
                riceTransferOrder = DtoMapper.convert(orderInsertDTO, TMpdRiceTransferOrder.class);

                serialNumber = serialNumberUtil.getSerialNumber("t_mpd_rice_transfer_order", 3, true);
                riceTransferOrder.setOrderNo("GD" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
                riceTransferOrder.setPlanId(riceTransferPlan.getId());
                riceTransferOrder.setPlanQuantity(riceTransferPlan.getPlanQuantity());
                riceTransferOrder.setInventoryQuantity(allRiceTransferStockQuantity);

                res += riceTransferOrderMapper.insert(riceTransferOrder);
            } else {
                riceTransferOrder.setPlanQuantity(riceTransferOrder.getPlanQuantity().add(planQuantity));
                riceTransferOrder.setInventoryQuantity(allRiceTransferStockQuantity);

                res += riceTransferOrderMapper.updateById(riceTransferOrder);
            }


            log.info("稻壳转运结束：-----------------");


        }
        return res;
    }
    /*
     * @description:自动新增蒸糠杂质处理
     * <AUTHOR>
     * @date 2024/6/13 12:27
     * @param
     * @return java.lang.Integer
     */
    public Integer InsertImpurityDealAuto(TMpdBranProductionOrder branProductionOrder) {
        //获取班次信息
        ResultVO<List<ShiftDTO>> shiftListByAreaIdAndCellId = shiftClient.getShiftListByAreaIdAndCellId(0, 0);
        AtomicInteger res = new AtomicInteger();
        //HashMap hashMap = restTemplate.getForObject("http://crew-schedule/shift/getShiftListByAreaIdAndCellId/0/0", HashMap.class);
       /* HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token","eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5c3kiLCJ1c2VyX2lkIjoyMjY5LCJpZGVudGl0eSI6IjgyNzU3ZDBiLWMzNTAtNDUyNC1hYzEwLTU0ZTQ2ZmE3MWFiOCIsImlzcyI6Imh2aXNpb25zIiwiZXhwIjoxNzE4NzMyNDkyLCJpYXQiOjE3MTg2ODkyOTIsImlzX3V1aWQiOnRydWV9.Z-KovKGHg_25vcvC8Ukr98S8OozxT6bbiZ9QeRZ6iwboZ2NXXppSMs-QkJ9y5HJUco5PrUtcIQCX7dg_lFeJKw");
        HttpEntity entity = new HttpEntity<>(headers);
        ResponseEntity<HashMap> response =  restTemplate.exchange("http://10.0.52.62:9000/crew-schedule/shift/getShiftListByAreaIdAndCellId/0/0?timestamp=1718332316219", HttpMethod.GET, entity, HashMap.class);
        HashMap hashMap = response.getBody();*/

        /*HashMap hashMap = restTemplate.getForObject("http://10.0.52.62:9000/crew-schedule/shift/getShiftListByAreaIdAndCellId/0/0", HashMap.class);

        log.info("=========请求返回值==========="+hashMap);
        if(hashMap.get("data")==null){
            log.error("请求接口:/crew-schedule/shift/getShiftListByAreaIdAndCellId,未成功");
        }
        Integer code = Optional.ofNullable(hashMap.get("code")).map(Object::toString).map(Integer::valueOf).orElse(null);
        if(code ==null || code !=200){
            log.error("请求接口:/crew-schedule/shift/getShiftListByAreaIdAndCellId错误信息:"+Optional.ofNullable((HashMap) hashMap.get("data")).map(j -> j.get("message")).map(Object::toString).orElse(null));
        }

        List<Map<String, Object>> list = (List<Map<String, Object>>) hashMap.get("data");*/

        if (shiftListByAreaIdAndCellId.getData() != null && !shiftListByAreaIdAndCellId.getData().isEmpty() ){
            shiftListByAreaIdAndCellId.getData().stream().forEach(e->{
                Integer shiftId = e.getId();
                String shiftName = e.getShiftName();
                ImpurityDealDTO impurityDealDTO = new ImpurityDealDTO();
                impurityDealDTO.setMaterialCode(branProductionOrder.getMaterialCode());
                impurityDealDTO.setMaterialName(branProductionOrder.getMaterialName());
                impurityDealDTO.setUnit(branProductionOrder.getUnit());
                impurityDealDTO.setSourceOrder(branProductionOrder.getOrderNo());
                impurityDealDTO.setMaterialId(branProductionOrder.getMaterialId());
                impurityDealDTO.setShift(shiftName);
                impurityDealDTO.setCreatorId(branProductionOrder.getCreatorId());
                impurityDealDTO.setUpdaterId(branProductionOrder.getUpdaterId());
                impurityDealDTO.setShiftId(shiftId);
                Date date = branProductionOrder.getOrderDate();
                res.addAndGet(impurityDealService.addImpurityDealAuto(impurityDealDTO,date));
            });
        }
        return res.get();
    }

}
