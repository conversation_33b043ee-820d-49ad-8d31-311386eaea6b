package com.hvisions.rawmaterial.service.performance;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 性能监控服务
 * 用于监控系统性能指标和缓存命中率
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class PerformanceMonitorService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String METRICS_PREFIX = "performance:metrics:";
    private static final String CACHE_HIT_KEY = METRICS_PREFIX + "cache:hit";
    private static final String CACHE_MISS_KEY = METRICS_PREFIX + "cache:miss";
    private static final String QUERY_TIME_KEY = METRICS_PREFIX + "query:time";
    private static final String SAP_CALL_KEY = METRICS_PREFIX + "sap:call";
    
    /**
     * 记录缓存命中
     * 
     * @param cacheType 缓存类型
     */
    public void recordCacheHit(String cacheType) {
        try {
            String key = CACHE_HIT_KEY + ":" + cacheType;
            redisTemplate.opsForValue().increment(key, 1);
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
            
            log.debug("记录缓存命中：{}", cacheType);
        } catch (Exception e) {
            log.error("记录缓存命中失败", e);
        }
    }
    
    /**
     * 记录缓存未命中
     * 
     * @param cacheType 缓存类型
     */
    public void recordCacheMiss(String cacheType) {
        try {
            String key = CACHE_MISS_KEY + ":" + cacheType;
            redisTemplate.opsForValue().increment(key, 1);
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
            
            log.debug("记录缓存未命中：{}", cacheType);
        } catch (Exception e) {
            log.error("记录缓存未命中失败", e);
        }
    }
    
    /**
     * 记录查询执行时间
     * 
     * @param queryType 查询类型
     * @param executionTime 执行时间（毫秒）
     */
    public void recordQueryTime(String queryType, long executionTime) {
        try {
            String key = QUERY_TIME_KEY + ":" + queryType;
            
            // 记录平均执行时间
            String countKey = key + ":count";
            String totalKey = key + ":total";
            
            redisTemplate.opsForValue().increment(countKey, 1);
            redisTemplate.opsForValue().increment(totalKey, executionTime);
            
            redisTemplate.expire(countKey, 24, TimeUnit.HOURS);
            redisTemplate.expire(totalKey, 24, TimeUnit.HOURS);
            
            log.debug("记录查询执行时间：{}，耗时：{}ms", queryType, executionTime);
        } catch (Exception e) {
            log.error("记录查询执行时间失败", e);
        }
    }
    
    /**
     * 记录SAP接口调用
     * 
     * @param apiType API类型
     * @param success 是否成功
     * @param responseTime 响应时间（毫秒）
     */
    public void recordSapCall(String apiType, boolean success, long responseTime) {
        try {
            String baseKey = SAP_CALL_KEY + ":" + apiType;
            String statusKey = baseKey + ":" + (success ? "success" : "failure");
            String timeKey = baseKey + ":time";
            
            redisTemplate.opsForValue().increment(statusKey, 1);
            redisTemplate.opsForValue().increment(timeKey + ":count", 1);
            redisTemplate.opsForValue().increment(timeKey + ":total", responseTime);
            
            redisTemplate.expire(statusKey, 24, TimeUnit.HOURS);
            redisTemplate.expire(timeKey + ":count", 24, TimeUnit.HOURS);
            redisTemplate.expire(timeKey + ":total", 24, TimeUnit.HOURS);
            
            log.debug("记录SAP接口调用：{}，成功：{}，响应时间：{}ms", apiType, success, responseTime);
        } catch (Exception e) {
            log.error("记录SAP接口调用失败", e);
        }
    }
    
    /**
     * 获取缓存命中率统计
     * 
     * @return 缓存命中率统计
     */
    public Map<String, Object> getCacheHitRateStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 获取所有缓存类型的命中和未命中数据
            String[] cacheTypes = {"inventory-difference-statistics", "sap-stock-data", "mes-data", "query-results", "hot-data"};
            
            for (String cacheType : cacheTypes) {
                String hitKey = CACHE_HIT_KEY + ":" + cacheType;
                String missKey = CACHE_MISS_KEY + ":" + cacheType;
                
                Long hits = (Long) redisTemplate.opsForValue().get(hitKey);
                Long misses = (Long) redisTemplate.opsForValue().get(missKey);
                
                hits = hits != null ? hits : 0L;
                misses = misses != null ? misses : 0L;
                
                long total = hits + misses;
                double hitRate = total > 0 ? (double) hits / total * 100 : 0.0;
                
                Map<String, Object> cacheStats = new HashMap<>();
                cacheStats.put("hits", hits);
                cacheStats.put("misses", misses);
                cacheStats.put("total", total);
                cacheStats.put("hitRate", Math.round(hitRate * 100.0) / 100.0);
                
                stats.put(cacheType, cacheStats);
            }
            
        } catch (Exception e) {
            log.error("获取缓存命中率统计失败", e);
        }
        
        return stats;
    }
    
    /**
     * 获取查询性能统计
     * 
     * @return 查询性能统计
     */
    public Map<String, Object> getQueryPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            String[] queryTypes = {"inventory-difference", "sap-stock", "mes-data", "statistics"};
            
            for (String queryType : queryTypes) {
                String countKey = QUERY_TIME_KEY + ":" + queryType + ":count";
                String totalKey = QUERY_TIME_KEY + ":" + queryType + ":total";
                
                Long count = (Long) redisTemplate.opsForValue().get(countKey);
                Long total = (Long) redisTemplate.opsForValue().get(totalKey);
                
                count = count != null ? count : 0L;
                total = total != null ? total : 0L;
                
                double avgTime = count > 0 ? (double) total / count : 0.0;
                
                Map<String, Object> queryStats = new HashMap<>();
                queryStats.put("count", count);
                queryStats.put("totalTime", total);
                queryStats.put("avgTime", Math.round(avgTime * 100.0) / 100.0);
                
                stats.put(queryType, queryStats);
            }
            
        } catch (Exception e) {
            log.error("获取查询性能统计失败", e);
        }
        
        return stats;
    }
    
    /**
     * 获取SAP接口调用统计
     * 
     * @return SAP接口调用统计
     */
    public Map<String, Object> getSapCallStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            String[] apiTypes = {"stock-query", "stock-sync", "difference-sync"};
            
            for (String apiType : apiTypes) {
                String successKey = SAP_CALL_KEY + ":" + apiType + ":success";
                String failureKey = SAP_CALL_KEY + ":" + apiType + ":failure";
                String countKey = SAP_CALL_KEY + ":" + apiType + ":time:count";
                String totalKey = SAP_CALL_KEY + ":" + apiType + ":time:total";
                
                Long success = (Long) redisTemplate.opsForValue().get(successKey);
                Long failure = (Long) redisTemplate.opsForValue().get(failureKey);
                Long count = (Long) redisTemplate.opsForValue().get(countKey);
                Long total = (Long) redisTemplate.opsForValue().get(totalKey);
                
                success = success != null ? success : 0L;
                failure = failure != null ? failure : 0L;
                count = count != null ? count : 0L;
                total = total != null ? total : 0L;
                
                long totalCalls = success + failure;
                double successRate = totalCalls > 0 ? (double) success / totalCalls * 100 : 0.0;
                double avgResponseTime = count > 0 ? (double) total / count : 0.0;
                
                Map<String, Object> apiStats = new HashMap<>();
                apiStats.put("success", success);
                apiStats.put("failure", failure);
                apiStats.put("total", totalCalls);
                apiStats.put("successRate", Math.round(successRate * 100.0) / 100.0);
                apiStats.put("avgResponseTime", Math.round(avgResponseTime * 100.0) / 100.0);
                
                stats.put(apiType, apiStats);
            }
            
        } catch (Exception e) {
            log.error("获取SAP接口调用统计失败", e);
        }
        
        return stats;
    }
    
    /**
     * 获取综合性能报告
     * 
     * @return 性能报告
     */
    public Map<String, Object> getPerformanceReport() {
        Map<String, Object> report = new HashMap<>();
        
        report.put("cacheStats", getCacheHitRateStats());
        report.put("queryStats", getQueryPerformanceStats());
        report.put("sapStats", getSapCallStats());
        report.put("timestamp", System.currentTimeMillis());
        
        return report;
    }
    
    /**
     * 清除性能统计数据
     */
    public void clearPerformanceStats() {
        try {
            redisTemplate.delete(redisTemplate.keys(METRICS_PREFIX + "*"));
            log.info("性能统计数据已清除");
        } catch (Exception e) {
            log.error("清除性能统计数据失败", e);
        }
    }
}