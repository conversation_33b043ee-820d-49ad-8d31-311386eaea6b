package com.hvisions.rawmaterial.service.optimization;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 批量操作服务接口
 * 用于优化数据库批量操作性能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface BatchOperationService {
    
    /**
     * 批量查询库存差异数据
     * 
     * @param materialCodes 物料编码列表
     * @param warehouseCodes 仓库编码列表
     * @return 查询结果映射
     */
    Map<String, Object> batchQueryInventoryDifferences(List<String> materialCodes, List<String> warehouseCodes);
    
    /**
     * 批量更新库存差异状态
     * 
     * @param differenceIds 差异记录ID列表
     * @param status 目标状态
     * @return 更新成功的记录数
     */
    int batchUpdateDifferenceStatus(List<Integer> differenceIds, Integer status);
    
    /**
     * 批量插入库存差异记录
     * 
     * @param differences 差异记录列表
     * @return 插入成功的记录数
     */
    int batchInsertDifferences(List<Object> differences);
    
    /**
     * 异步批量处理SAP同步
     * 
     * @param materialCodes 物料编码列表
     * @return 异步处理结果
     */
    CompletableFuture<Map<String, Object>> asyncBatchSapSync(List<String> materialCodes);
    
    /**
     * 批量删除过期缓存
     * 
     * @param cacheKeys 缓存键列表
     */
    void batchDeleteExpiredCache(List<String> cacheKeys);
    
    /**
     * 批量预加载热点数据
     * 
     * @param dataKeys 数据键列表
     * @return 预加载结果
     */
    CompletableFuture<Map<String, Object>> batchPreloadHotData(List<String> dataKeys);
}