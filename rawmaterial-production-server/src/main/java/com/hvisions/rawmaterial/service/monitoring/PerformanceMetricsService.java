package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dto.monitoring.PerformanceMetrics;
import com.hvisions.rawmaterial.dto.monitoring.SystemMetrics;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 性能指标监控服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface PerformanceMetricsService {
    
    /**
     * 记录方法执行性能指标
     * 
     * @param methodName 方法名称
     * @param executionTime 执行时间(毫秒)
     * @param success 是否成功
     * @param parameters 方法参数
     */
    void recordMethodPerformance(String methodName, long executionTime, boolean success, String parameters);
    
    /**
     * 记录数据库查询性能指标
     * 
     * @param queryType 查询类型
     * @param executionTime 执行时间(毫秒)
     * @param recordCount 记录数量
     * @param sqlStatement SQL语句
     */
    void recordDatabaseQueryPerformance(String queryType, long executionTime, int recordCount, String sqlStatement);
    
    /**
     * 记录缓存操作性能指标
     * 
     * @param operation 操作类型(GET, SET, DELETE)
     * @param cacheKey 缓存键
     * @param hit 是否命中
     * @param executionTime 执行时间(毫秒)
     */
    void recordCachePerformance(String operation, String cacheKey, boolean hit, long executionTime);
    
    /**
     * 获取方法性能指标
     * 
     * @param methodName 方法名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 性能指标
     */
    PerformanceMetrics getMethodPerformanceMetrics(String methodName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取数据库查询性能指标
     * 
     * @param queryType 查询类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 性能指标
     */
    PerformanceMetrics getDatabaseQueryPerformanceMetrics(String queryType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取缓存性能指标
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 缓存性能指标
     */
    Map<String, Object> getCachePerformanceMetrics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取系统性能指标
     * 
     * @return 系统指标
     */
    SystemMetrics getSystemMetrics();
    
    /**
     * 获取慢查询列表
     * 
     * @param threshold 阈值(毫秒)
     * @param limit 限制数量
     * @return 慢查询列表
     */
    List<PerformanceMetrics> getSlowQueries(long threshold, int limit);
    
    /**
     * 获取性能趋势数据
     * 
     * @param metricType 指标类型
     * @param hours 小时数
     * @return 趋势数据
     */
    List<Map<String, Object>> getPerformanceTrend(String metricType, int hours);
}