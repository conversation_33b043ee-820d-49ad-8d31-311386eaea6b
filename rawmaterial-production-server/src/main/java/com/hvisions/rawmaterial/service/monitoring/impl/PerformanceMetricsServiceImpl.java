package com.hvisions.rawmaterial.service.monitoring.impl;

import com.hvisions.rawmaterial.dao.log.PerformanceMetricsRepository;
import com.hvisions.rawmaterial.dto.monitoring.PerformanceMetrics;
import com.hvisions.rawmaterial.dto.monitoring.SystemMetrics;
import com.hvisions.rawmaterial.entity.log.TMpdPerformanceMetrics;
import com.hvisions.rawmaterial.service.monitoring.PerformanceMetricsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 性能指标监控服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class PerformanceMetricsServiceImpl implements PerformanceMetricsService {
    
    @Autowired
    private PerformanceMetricsRepository performanceMetricsRepository;
    
    @Override
    public void recordMethodPerformance(String methodName, long executionTime, boolean success, String parameters) {
        TMpdPerformanceMetrics metrics = new TMpdPerformanceMetrics();
        metrics.setMetricType("METHOD_EXECUTION");
        metrics.setMetricName(methodName);
        metrics.setExecutionTime(executionTime);
        metrics.setSuccess(success);
        metrics.setParameters(parameters);
        metrics.setRecordTime(LocalDateTime.now());
        
        performanceMetricsRepository.save(metrics);
        
        if (executionTime > 5000) { // 超过5秒的方法执行记录警告日志
            log.warn("方法执行时间过长: 方法[{}], 执行时间[{}ms], 参数[{}]", methodName, executionTime, parameters);
        }
    }
    
    @Override
    public void recordDatabaseQueryPerformance(String queryType, long executionTime, int recordCount, String sqlStatement) {
        TMpdPerformanceMetrics metrics = new TMpdPerformanceMetrics();
        metrics.setMetricType("DATABASE_QUERY");
        metrics.setMetricName(queryType);
        metrics.setExecutionTime(executionTime);
        metrics.setRecordCount(recordCount);
        metrics.setSqlStatement(sqlStatement);
        metrics.setRecordTime(LocalDateTime.now());
        metrics.setSuccess(true); // 数据库查询能执行到这里说明是成功的
        
        performanceMetricsRepository.save(metrics);
        
        if (executionTime > 3000) { // 超过3秒的查询记录警告日志
            log.warn("数据库查询时间过长: 查询类型[{}], 执行时间[{}ms], 记录数[{}]", queryType, executionTime, recordCount);
        }
    }
    
    @Override
    public void recordCachePerformance(String operation, String cacheKey, boolean hit, long executionTime) {
        TMpdPerformanceMetrics metrics = new TMpdPerformanceMetrics();
        metrics.setMetricType("CACHE_OPERATION");
        metrics.setMetricName(operation);
        metrics.setCacheKey(cacheKey);
        metrics.setCacheHit(hit);
        metrics.setExecutionTime(executionTime);
        metrics.setRecordTime(LocalDateTime.now());
        metrics.setSuccess(true);
        
        performanceMetricsRepository.save(metrics);
        
        log.debug("缓存操作记录: 操作[{}], 键[{}], 命中[{}], 时间[{}ms]", operation, cacheKey, hit, executionTime);
    }
    
    @Override
    public PerformanceMetrics getMethodPerformanceMetrics(String methodName, LocalDateTime startTime, LocalDateTime endTime) {
        List<TMpdPerformanceMetrics> metrics = performanceMetricsRepository
            .findByMetricTypeAndMetricNameAndRecordTimeBetween("METHOD_EXECUTION", methodName, startTime, endTime);
        
        return calculatePerformanceMetrics(metrics, methodName);
    }
    
    @Override
    public PerformanceMetrics getDatabaseQueryPerformanceMetrics(String queryType, LocalDateTime startTime, LocalDateTime endTime) {
        List<TMpdPerformanceMetrics> metrics = performanceMetricsRepository
            .findByMetricTypeAndMetricNameAndRecordTimeBetween("DATABASE_QUERY", queryType, startTime, endTime);
        
        return calculatePerformanceMetrics(metrics, queryType);
    }
    
    @Override
    public Map<String, Object> getCachePerformanceMetrics(LocalDateTime startTime, LocalDateTime endTime) {
        List<TMpdPerformanceMetrics> metrics = performanceMetricsRepository
            .findByMetricTypeAndRecordTimeBetween("CACHE_OPERATION", startTime, endTime);
        
        Map<String, Object> result = new HashMap<>();
        
        long totalOperations = metrics.size();
        long hitCount = metrics.stream().filter(m -> Boolean.TRUE.equals(m.getCacheHit())).count();
        double hitRate = totalOperations > 0 ? (double) hitCount / totalOperations * 100 : 0.0;
        
        double avgExecutionTime = metrics.stream()
            .mapToLong(TMpdPerformanceMetrics::getExecutionTime)
            .average()
            .orElse(0.0);
        
        result.put("totalOperations", totalOperations);
        result.put("hitCount", hitCount);
        result.put("missCount", totalOperations - hitCount);
        result.put("hitRate", hitRate);
        result.put("averageExecutionTime", avgExecutionTime);
        
        return result;
    }
    
    @Override
    public SystemMetrics getSystemMetrics() {
        SystemMetrics systemMetrics = new SystemMetrics();
        
        // 获取内存信息
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        
        systemMetrics.setUsedMemory(usedMemory);
        systemMetrics.setMaxMemory(maxMemory);
        systemMetrics.setMemoryUsagePercent((double) usedMemory / maxMemory * 100);
        
        // 获取CPU信息
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        systemMetrics.setCpuUsagePercent(osBean.getProcessCpuLoad() * 100);
        systemMetrics.setAvailableProcessors(osBean.getAvailableProcessors());
        
        // 获取线程信息
        systemMetrics.setActiveThreadCount(Thread.activeCount());
        
        systemMetrics.setRecordTime(LocalDateTime.now());
        
        return systemMetrics;
    }
    
    @Override
    public List<PerformanceMetrics> getSlowQueries(long threshold, int limit) {
        List<TMpdPerformanceMetrics> slowQueries = performanceMetricsRepository
            .findSlowQueries("DATABASE_QUERY", threshold, limit);
        
        return slowQueries.stream()
            .map(this::convertToPerformanceMetrics)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getPerformanceTrend(String metricType, int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        List<TMpdPerformanceMetrics> metrics = performanceMetricsRepository
            .findByMetricTypeAndRecordTimeBetween(metricType, startTime, endTime);
        
        // 按小时分组统计
        Map<Integer, List<TMpdPerformanceMetrics>> hourlyMetrics = metrics.stream()
            .collect(Collectors.groupingBy(m -> m.getRecordTime().getHour()));
        
        List<Map<String, Object>> trend = new ArrayList<>();
        for (int i = 0; i < hours; i++) {
            LocalDateTime hourTime = startTime.plusHours(i);
            int hour = hourTime.getHour();
            
            List<TMpdPerformanceMetrics> hourMetrics = hourlyMetrics.getOrDefault(hour, Collections.emptyList());
            
            Map<String, Object> hourData = new HashMap<>();
            hourData.put("hour", hourTime);
            hourData.put("count", hourMetrics.size());
            
            if (!hourMetrics.isEmpty()) {
                double avgTime = hourMetrics.stream()
                    .mapToLong(TMpdPerformanceMetrics::getExecutionTime)
                    .average()
                    .orElse(0.0);
                hourData.put("averageExecutionTime", avgTime);
            } else {
                hourData.put("averageExecutionTime", 0.0);
            }
            
            trend.add(hourData);
        }
        
        return trend;
    }
    
    private PerformanceMetrics calculatePerformanceMetrics(List<TMpdPerformanceMetrics> metrics, String name) {
        PerformanceMetrics result = new PerformanceMetrics();
        result.setMetricName(name);
        result.setTotalCount(metrics.size());
        
        if (metrics.isEmpty()) {
            return result;
        }
        
        long successCount = metrics.stream().filter(m -> Boolean.TRUE.equals(m.getSuccess())).count();
        result.setSuccessCount((int) successCount);
        result.setFailureCount(metrics.size() - (int) successCount);
        result.setSuccessRate((double) successCount / metrics.size() * 100);
        
        double avgExecutionTime = metrics.stream()
            .mapToLong(TMpdPerformanceMetrics::getExecutionTime)
            .average()
            .orElse(0.0);
        result.setAverageExecutionTime(avgExecutionTime);
        
        long maxExecutionTime = metrics.stream()
            .mapToLong(TMpdPerformanceMetrics::getExecutionTime)
            .max()
            .orElse(0L);
        result.setMaxExecutionTime(maxExecutionTime);
        
        long minExecutionTime = metrics.stream()
            .mapToLong(TMpdPerformanceMetrics::getExecutionTime)
            .min()
            .orElse(0L);
        result.setMinExecutionTime(minExecutionTime);
        
        return result;
    }
    
    private PerformanceMetrics convertToPerformanceMetrics(TMpdPerformanceMetrics entity) {
        PerformanceMetrics dto = new PerformanceMetrics();
        dto.setMetricName(entity.getMetricName());
        dto.setExecutionTime(entity.getExecutionTime().doubleValue());
        dto.setRecordTime(entity.getRecordTime());
        dto.setParameters(entity.getParameters());
        dto.setSqlStatement(entity.getSqlStatement());
        return dto;
    }
}