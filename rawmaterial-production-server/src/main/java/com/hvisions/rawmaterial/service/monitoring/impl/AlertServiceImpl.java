package com.hvisions.rawmaterial.service.monitoring.impl;

import com.hvisions.rawmaterial.dao.log.AlertNotificationRepository;
import com.hvisions.rawmaterial.dao.log.AlertRuleRepository;
import com.hvisions.rawmaterial.dto.monitoring.AlertNotification;
import com.hvisions.rawmaterial.dto.monitoring.AlertRule;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceMetrics;
import com.hvisions.rawmaterial.entity.log.TMpdAlertNotification;
import com.hvisions.rawmaterial.entity.log.TMpdAlertRule;
import com.hvisions.rawmaterial.service.monitoring.AlertService;
import com.hvisions.rawmaterial.service.monitoring.PerformanceMetricsService;
import com.hvisions.rawmaterial.service.monitoring.SapInterfaceMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AlertServiceImpl implements AlertService {
    
    @Autowired
    private AlertRuleRepository alertRuleRepository;
    
    @Autowired
    private AlertNotificationRepository alertNotificationRepository;
    
    @Autowired
    private SapInterfaceMonitorService sapInterfaceMonitorService;
    
    @Autowired
    private PerformanceMetricsService performanceMetricsService;
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    @Override
    public void checkSapInterfaceAlerts() {
        List<TMpdAlertRule> sapRules = alertRuleRepository.findByRuleTypeAndEnabled("SAP_INTERFACE", true);
        
        for (TMpdAlertRule rule : sapRules) {
            try {
                checkSapInterfaceRule(rule);
            } catch (Exception e) {
                log.error("检查SAP接口告警规则失败: {}", rule.getRuleName(), e);
            }
        }
    }
    
    @Scheduled(fixedRate = 600000) // 每10分钟检查一次
    @Override
    public void checkPerformanceAlerts() {
        List<TMpdAlertRule> performanceRules = alertRuleRepository.findByRuleTypeAndEnabled("PERFORMANCE", true);
        
        for (TMpdAlertRule rule : performanceRules) {
            try {
                checkPerformanceRule(rule);
            } catch (Exception e) {
                log.error("检查性能告警规则失败: {}", rule.getRuleName(), e);
            }
        }
    }
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    @Override
    public void checkBusinessAlerts() {
        List<TMpdAlertRule> businessRules = alertRuleRepository.findByRuleTypeAndEnabled("BUSINESS", true);
        
        for (TMpdAlertRule rule : businessRules) {
            try {
                checkBusinessRule(rule);
            } catch (Exception e) {
                log.error("检查业务告警规则失败: {}", rule.getRuleName(), e);
            }
        }
    }
    
    @Override
    public void sendAlert(AlertNotification notification) {
        TMpdAlertNotification entity = new TMpdAlertNotification();
        BeanUtils.copyProperties(notification, entity);
        entity.setAlertTime(LocalDateTime.now());
        entity.setAlertStatus("PENDING");
        
        alertNotificationRepository.save(entity);
        
        // TODO: 实现实际的告警通知发送(邮件、短信、钉钉等)
        log.warn("发送告警通知: 类型[{}], 级别[{}], 消息[{}]", 
            notification.getAlertType(), notification.getAlertLevel(), notification.getAlertMessage());
    }
    
    @Override
    public void createAlertRule(AlertRule rule) {
        TMpdAlertRule entity = new TMpdAlertRule();
        BeanUtils.copyProperties(rule, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setEnabled(true);
        
        alertRuleRepository.save(entity);
        
        log.info("创建告警规则: {}", rule.getRuleName());
    }
    
    @Override
    public void updateAlertRule(AlertRule rule) {
        TMpdAlertRule entity = alertRuleRepository.findById(rule.getId()).orElse(null);
        if (entity != null) {
            BeanUtils.copyProperties(rule, entity, "id", "createTime");
            entity.setUpdateTime(LocalDateTime.now());
            
            alertRuleRepository.save(entity);
            
            log.info("更新告警规则: {}", rule.getRuleName());
        }
    }
    
    @Override
    public void deleteAlertRule(Integer ruleId) {
        alertRuleRepository.deleteById(ruleId);
        log.info("删除告警规则: {}", ruleId);
    }
    
    @Override
    public List<AlertRule> getAllAlertRules() {
        List<TMpdAlertRule> entities = alertRuleRepository.findAll();
        return entities.stream().map(this::convertToAlertRuleDto).collect(Collectors.toList());
    }
    
    @Override
    public List<AlertNotification> getAlertHistory(int pageNum, int pageSize) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
        List<TMpdAlertNotification> entities = alertNotificationRepository
            .findAllByOrderByAlertTimeDesc(pageable);
        
        return entities.stream().map(this::convertToAlertNotificationDto).collect(Collectors.toList());
    }
    
    @Override
    public void acknowledgeAlert(Integer alertId, Integer userId, String userName, String remark) {
        TMpdAlertNotification entity = alertNotificationRepository.findById(alertId).orElse(null);
        if (entity != null) {
            entity.setAlertStatus("ACKNOWLEDGED");
            entity.setAcknowledgeUserId(userId);
            entity.setAcknowledgeUserName(userName);
            entity.setAcknowledgeTime(LocalDateTime.now());
            entity.setAcknowledgeRemark(remark);
            
            alertNotificationRepository.save(entity);
            
            log.info("确认告警: 告警ID[{}], 用户[{}], 备注[{}]", alertId, userName, remark);
        }
    }
    
    private void checkSapInterfaceRule(TMpdAlertRule rule) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(rule.getCheckInterval());
        
        // 检查SAP接口健康状态
        if ("SAP_HEALTH_CHECK".equals(rule.getMetricName())) {
            boolean isHealthy = sapInterfaceMonitorService.checkSapInterfaceHealth();
            if (!isHealthy) {
                AlertNotification notification = new AlertNotification();
                notification.setRuleId(rule.getId());
                notification.setAlertType("SAP_INTERFACE");
                notification.setAlertLevel(rule.getAlertLevel());
                notification.setAlertMessage("SAP接口健康检查失败");
                notification.setMetricName(rule.getMetricName());
                notification.setCurrentValue("false");
                notification.setThresholdValue("true");
                
                sendAlert(notification);
            }
        }
        
        // 检查SAP接口成功率
        if ("SAP_SUCCESS_RATE".equals(rule.getMetricName())) {
            SapInterfaceMetrics metrics = sapInterfaceMonitorService
                .getInterfaceMetrics("ALL", startTime, endTime);
            
            if (metrics.getSuccessRate() < rule.getThresholdValue()) {
                AlertNotification notification = new AlertNotification();
                notification.setRuleId(rule.getId());
                notification.setAlertType("SAP_INTERFACE");
                notification.setAlertLevel(rule.getAlertLevel());
                notification.setAlertMessage("SAP接口成功率过低");
                notification.setMetricName(rule.getMetricName());
                notification.setCurrentValue(metrics.getSuccessRate().toString());
                notification.setThresholdValue(rule.getThresholdValue().toString());
                
                sendAlert(notification);
            }
        }
    }
    
    private void checkPerformanceRule(TMpdAlertRule rule) {
        // TODO: 实现性能告警规则检查
        log.debug("检查性能告警规则: {}", rule.getRuleName());
    }
    
    private void checkBusinessRule(TMpdAlertRule rule) {
        // TODO: 实现业务告警规则检查
        log.debug("检查业务告警规则: {}", rule.getRuleName());
    }
    
    private AlertRule convertToAlertRuleDto(TMpdAlertRule entity) {
        AlertRule dto = new AlertRule();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
    
    private AlertNotification convertToAlertNotificationDto(TMpdAlertNotification entity) {
        AlertNotification dto = new AlertNotification();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}