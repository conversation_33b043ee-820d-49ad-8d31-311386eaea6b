# 原辅料库存差异核心业务服务实现文档

## 概述

本文档描述了原辅料库存盘点差异处理功能的核心业务服务实现，包括差异记录生成、差异处理、数据继承、重新计算等核心功能。

## 实现的核心功能

### 1. 差异记录生成逻辑

**功能描述**: 计算 MES 与 SAP 库存差异，生成差异处理记录

**核心算法**:
```
理论库存 = MES期初库存 + 地磅收货数量 - 固废提报数量 - 发料数量
差异数量 = MES当前库存 - 理论库存
```

**实现要点**:
- 使用 MES 数据服务获取实时库存数据
- 支持批量生成差异记录
- 自动跳过已存在的待处理记录
- 完善的错误处理和日志记录

**关键方法**: `generateDifferenceRecords()`, `calculateDifferenceWithValidation()`

### 2. 差异处理逻辑

**功能描述**: 处理库存差异，包括状态更新和新记录创建

**处理流程**:
1. 验证处理请求的合法性
2. 更新当前记录为已处理状态
3. 记录处理人、处理时间、处理备注
4. 自动生成下一周期的待处理记录

**业务规则验证**:
- 只有待处理状态的记录才能被处理
- 统计结束日期不能早于开始日期
- 处理人信息必须完整

**关键方法**: `processDifference()`, `validateProcessRequest()`

### 3. 数据继承逻辑

**功能描述**: 确保新记录正确继承上一记录的数据

**继承规则**:
- 新记录的统计开始时间 = 上一记录的统计结束时间
- 新记录的MES期初库存 = 上一记录的MES当前库存
- 新记录状态自动设置为"待处理"

**实现要点**:
- 查询最新的已处理记录作为数据源
- 如果没有历史记录，使用默认值
- 确保数据连续性和一致性

**关键方法**: `generateNextPeriodRecord()`, `getInitialStockFromLastRecord()`

### 4. 重新计算差异功能

**功能描述**: 基于最新数据重新计算差异数量

**计算过程**:
1. 获取最新的 MES 库存数据
2. 重新计算地磅收货、固废、发料数量
3. 更新 SAP 库存数据
4. 重新计算理论库存和差异数量

**业务限制**:
- 只有待处理状态的记录才能重新计算
- 使用事务确保数据一致性

**关键方法**: `recalculateDifference()`, `getSapStockWithFallback()`

### 5. 业务规则验证

**验证项目**:
- 请求参数完整性验证
- 业务状态合法性验证
- 日期范围合理性验证
- 处理权限验证

**异常处理**:
- 参数验证异常: `IllegalArgumentException`
- 业务状态异常: `IllegalStateException`
- 系统异常: `RuntimeException`

**关键方法**: `validateProcessRequest()`, `validateDateRange()`

## 核心业务方法详解

### calculateTheoreticalStock()
```java
private BigDecimal calculateTheoreticalStock(BigDecimal initialStock, BigDecimal receiptQty, 
                                           BigDecimal wasteQty, BigDecimal issueQty) {
    BigDecimal theoretical = initialStock != null ? initialStock : BigDecimal.ZERO;
    
    if (receiptQty != null) {
        theoretical = theoretical.add(receiptQty);
    }
    if (wasteQty != null) {
        theoretical = theoretical.subtract(wasteQty);
    }
    if (issueQty != null) {
        theoretical = theoretical.subtract(issueQty);
    }
    
    return theoretical;
}
```

### calculateDifferenceQuantity()
```java
private BigDecimal calculateDifferenceQuantity(BigDecimal currentStock, BigDecimal theoreticalStock) {
    BigDecimal current = currentStock != null ? currentStock : BigDecimal.ZERO;
    BigDecimal theoretical = theoreticalStock != null ? theoreticalStock : BigDecimal.ZERO;
    return current.subtract(theoretical);
}
```

## 数据流转图

```
[MES系统] → [获取库存数据] → [计算理论库存] → [计算差异数量] → [生成差异记录]
    ↓
[SAP系统] → [获取SAP库存] → [更新差异记录] → [待处理状态]
    ↓
[用户处理] → [验证处理请求] → [更新为已处理] → [生成下一周期记录]
    ↓
[数据继承] → [期初库存继承] → [时间范围继承] → [新的待处理记录]
```

## 错误处理策略

### 1. 分层异常处理
- **参数验证层**: 抛出 `IllegalArgumentException`
- **业务逻辑层**: 抛出 `IllegalStateException`
- **数据访问层**: 抛出 `RuntimeException`

### 2. 事务管理
- 使用 `@Transactional` 确保数据一致性
- 关键操作支持回滚
- 异常时记录详细日志

### 3. 降级处理
- SAP 接口调用失败时使用缓存数据
- MES 数据获取失败时使用默认值
- 批量操作中单个失败不影响整体

## 性能优化

### 1. 批量操作
- 批量插入差异记录
- 批量更新 SAP 库存
- 减少数据库交互次数

### 2. 数据缓存
- 缓存 SAP 库存数据
- 缓存物料仓库组合
- 减少重复查询

### 3. 异步处理
- 大批量数据生成异步化
- SAP 接口调用异步化
- 提高系统响应性能

## 测试覆盖

### 1. 单元测试
- 核心业务逻辑测试
- 边界值测试
- 异常场景测试

### 2. 集成测试
- MES 数据服务集成测试
- SAP 接口集成测试
- 数据库操作集成测试

### 3. 业务逻辑测试
- 理论库存计算测试
- 差异数量计算测试
- 数据继承逻辑测试

## 监控和日志

### 1. 关键操作日志
- 差异记录生成日志
- 差异处理操作日志
- 数据继承过程日志

### 2. 性能监控
- 方法执行时间监控
- 数据库操作性能监控
- 外部接口调用监控

### 3. 异常告警
- 业务异常实时告警
- 系统异常邮件通知
- 性能异常监控告警

## 配置说明

### 1. 数据源配置
- MES 数据服务配置
- SAP 接口服务配置
- 数据库连接配置

### 2. 业务参数配置
- 默认统计开始日期
- 批量操作大小限制
- 异常重试次数

### 3. 缓存配置
- Redis 缓存配置
- 缓存过期时间设置
- 缓存一致性策略

## 部署注意事项

### 1. 数据库准备
- 确保相关表结构已创建
- 检查索引是否正确建立
- 验证数据权限配置

### 2. 依赖服务
- 确保 MES 数据服务可用
- 确保 SAP 接口服务可用
- 确保 Redis 缓存服务可用

### 3. 配置检查
- 验证数据库连接配置
- 验证外部服务配置
- 验证日志输出配置

## 维护指南

### 1. 日常维护
- 定期检查差异记录生成情况
- 监控系统性能指标
- 清理过期日志和缓存

### 2. 故障排查
- 查看应用日志定位问题
- 检查数据库连接状态
- 验证外部服务可用性

### 3. 版本升级
- 备份现有数据
- 测试新版本兼容性
- 逐步灰度发布

## 总结

本实现完成了原辅料库存盘点差异处理功能的核心业务逻辑，包括：

✅ **差异记录生成逻辑** - 计算 MES 与 SAP 库存差异
✅ **差异处理逻辑** - 状态更新和新记录创建  
✅ **数据继承逻辑** - 确保新记录正确继承上一记录的数据
✅ **重新计算差异功能** - 基于最新数据重新计算
✅ **业务规则验证** - 完善的参数和状态验证
✅ **异常处理** - 分层异常处理和事务管理
✅ **单元测试** - 核心业务逻辑测试覆盖

所有功能均已通过业务逻辑测试验证，满足需求文档中的各项要求。