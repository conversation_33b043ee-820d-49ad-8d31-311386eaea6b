package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.pc.machining.order.PcMachiningOrderDTO;
import com.hvisions.rawmaterial.dto.production.pc.machining.order.PcMachiningOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.pc.machining.order.PcMachiningOrderPageQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @description:预处理加工工单
 * @date 2022/4/22 10:18
 */
public interface PcMachiningOrderService {

    Page<PcMachiningOrderPageDTO> getPcMachiningOrderPageList(PcMachiningOrderPageQueryDTO machiningOrderPageQueryDTO);

    Integer addPcMachiningOrder(PcMachiningOrderDTO machiningOrderDTO);

    Integer updatePcMachiningOrder(PcMachiningOrderDTO machiningOrderDTO);

    Integer deletePcMachiningOrder(Integer id);

}
