package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.rice.dispense.detail.RiceDispenseDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.dispense.order.RiceDispenseOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.dispense.order.RiceDispenseOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳分发工单
 * @date 2022/4/22 10:18
 */
public interface RiceDispenseOrderService {

    Page<RiceDispenseOrderPageDTO> getRiceDispenseOrderPageList(RiceDispenseOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);

    Page<RiceDispenseDetailListDTO> getRiceDispenseOrderDetailPageList(RiceDispenseOrderPageQueryDTO queryDTO);
}
