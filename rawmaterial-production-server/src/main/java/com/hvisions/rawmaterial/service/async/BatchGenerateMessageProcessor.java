package com.hvisions.rawmaterial.service.async;

import com.hvisions.rawmaterial.configuration.InventoryDifferenceRabbitConfig;
import com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage;
import com.hvisions.rawmaterial.dto.async.TaskStatusMessage;
import com.hvisions.rawmaterial.service.difference.MaterialDifferenceService;
import com.hvisions.rawmaterial.service.async.impl.AsyncTaskServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 批量生成差异记录消息处理器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class BatchGenerateMessageProcessor {
    
    @Autowired
    private MaterialDifferenceService materialDifferenceService;
    
    @Autowired
    private AsyncTaskServiceImpl asyncTaskService;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    /**
     * 处理批量生成消息
     */
    @RabbitListener(queues = InventoryDifferenceRabbitConfig.BATCH_GENERATE_QUEUE)
    public void processBatchGenerateMessage(BatchGenerateTaskMessage message) {
        log.info("开始处理批量生成任务: {}", message.getTaskId());
        
        // 更新任务状态为执行中
        TaskStatusMessage status = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.RUNNING
        );
        status.setStatusDescription("正在批量生成差异记录...");
        status.setTotalCount(message.getEstimatedCount());
        asyncTaskService.updateTaskStatus(status);
        
        try {
            // 执行批量生成
            String result = performBatchGenerate(message, status);
            
            // 更新任务状态为成功
            status.setStatus(TaskStatusMessage.TaskStatus.SUCCESS);
            status.setStatusDescription("批量生成差异记录完成");
            status.setResult(result);
            status.setProgress(100);
            
            asyncTaskService.updateTaskStatus(status);
            
            log.info("批量生成任务完成: {}, 处理数量: {}, 成功: {}, 失败: {}", 
                message.getTaskId(), status.getProcessedCount(), 
                status.getSuccessCount(), status.getFailureCount());
                
        } catch (Exception e) {
            log.error("批量生成任务执行失败: {}", message.getTaskId(), e);
            
            // 检查是否可以重试
            if (message.canRetry()) {
                message.incrementRetryCount();
                
                // 更新状态为重试中
                status.setStatus(TaskStatusMessage.TaskStatus.RUNNING);
                status.setStatusDescription(String.format("执行失败，正在重试 (%d/%d)", 
                    message.getRetryCount(), message.getMaxRetryCount()));
                status.setErrorMessage(e.getMessage());
                asyncTaskService.updateTaskStatus(status);
                
                // 延迟重试
                try {
                    Thread.sleep(10000 * message.getRetryCount()); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
                
                // 重新发送消息
                rabbitTemplate.convertAndSend(
                    InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
                    InventoryDifferenceRabbitConfig.BATCH_GENERATE_ROUTING_KEY,
                    message
                );
                
                log.info("批量生成任务重试: {}, 重试次数: {}", message.getTaskId(), message.getRetryCount());
                
            } else {
                // 重试次数用完，标记为失败
                status.setStatus(TaskStatusMessage.TaskStatus.FAILED);
                status.setStatusDescription("批量生成失败，已达到最大重试次数");
                status.setErrorMessage(e.getMessage());
                asyncTaskService.updateTaskStatus(status);
            }
        }
    }
    
    /**
     * 处理批量生成死信消息
     */
    @RabbitListener(queues = InventoryDifferenceRabbitConfig.BATCH_GENERATE_DLQ)
    public void processBatchGenerateDeadLetter(BatchGenerateTaskMessage message) {
        log.error("批量生成任务进入死信队列: {}", message.getTaskId());
        
        // 更新任务状态为失败
        TaskStatusMessage status = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.FAILED
        );
        status.setStatusDescription("任务处理超时或异常，已进入死信队列");
        status.setErrorMessage("消息处理超时或发生严重异常");
        
        asyncTaskService.updateTaskStatus(status);
    }
    
    /**
     * 执行批量生成
     */
    private String performBatchGenerate(BatchGenerateTaskMessage message, TaskStatusMessage status) {
        int batchSize = message.getBatchSize();
        int processedCount = 0;
        int successCount = 0;
        int failureCount = 0;
        
        try {
            // 模拟批量处理逻辑
            // 实际实现中应该调用 materialDifferenceService 的批量生成方法
            
            // 这里简化处理，实际应该根据条件查询需要处理的数据
            int totalCount = message.getEstimatedCount() != null ? message.getEstimatedCount() : 100;
            status.setTotalCount(totalCount);
            
            // 分批处理
            for (int i = 0; i < totalCount; i += batchSize) {
                int currentBatchSize = Math.min(batchSize, totalCount - i);
                
                try {
                    // 处理当前批次
                    processBatch(message, i, currentBatchSize);
                    successCount += currentBatchSize;
                    
                } catch (Exception e) {
                    log.warn("批次处理失败: 起始位置={}, 批次大小={}", i, currentBatchSize, e);
                    failureCount += currentBatchSize;
                }
                
                processedCount += currentBatchSize;
                
                // 更新进度
                status.updateProcessStats(processedCount, successCount, failureCount);
                status.setStatusDescription(String.format("正在处理... (%d/%d)", processedCount, totalCount));
                asyncTaskService.updateTaskStatus(status);
                
                // 避免长时间占用资源
                if (i % (batchSize * 5) == 0) {
                    Thread.sleep(100);
                }
            }
            
            return String.format("批量生成完成，共处理 %d 条记录，成功 %d 条，失败 %d 条", 
                processedCount, successCount, failureCount);
                
        } catch (Exception e) {
            status.updateProcessStats(processedCount, successCount, failureCount);
            throw new RuntimeException("批量生成执行失败", e);
        }
    }
    
    /**
     * 处理单个批次
     */
    private void processBatch(BatchGenerateTaskMessage message, int startIndex, int batchSize) {
        // 实际实现中应该调用具体的业务逻辑
        // 例如：materialDifferenceService.generateDifferenceRecordsBatch(...)
        
        log.debug("处理批次: 起始位置={}, 批次大小={}", startIndex, batchSize);
        
        // 模拟处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("批次处理被中断", e);
        }
    }
}