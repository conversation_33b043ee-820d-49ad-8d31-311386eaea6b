package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceMetrics;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceCallLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SAP接口监控服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface SapInterfaceMonitorService {
    
    /**
     * 记录SAP接口调用开始
     * 
     * @param interfaceName 接口名称
     * @param requestParams 请求参数
     * @return 调用ID
     */
    String recordInterfaceCallStart(String interfaceName, String requestParams);
    
    /**
     * 记录SAP接口调用结束
     * 
     * @param callId 调用ID
     * @param success 是否成功
     * @param responseData 响应数据
     * @param errorMessage 错误信息
     */
    void recordInterfaceCallEnd(String callId, boolean success, String responseData, String errorMessage);
    
    /**
     * 获取SAP接口调用指标
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 接口指标
     */
    SapInterfaceMetrics getInterfaceMetrics(String interfaceName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取SAP接口调用日志
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 调用日志列表
     */
    List<SapInterfaceCallLog> getInterfaceCallLogs(String interfaceName, LocalDateTime startTime, 
                                                  LocalDateTime endTime, int pageNum, int pageSize);
    
    /**
     * 获取失败的SAP接口调用
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败调用列表
     */
    List<SapInterfaceCallLog> getFailedInterfaceCalls(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 检查SAP接口健康状态
     * 
     * @return 健康状态
     */
    boolean checkSapInterfaceHealth();
    
    /**
     * 获取SAP接口响应时间统计
     * 
     * @param interfaceName 接口名称
     * @param hours 统计小时数
     * @return 响应时间统计
     */
    List<Double> getResponseTimeStatistics(String interfaceName, int hours);
}