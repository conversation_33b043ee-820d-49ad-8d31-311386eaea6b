package com.hvisions.rawmaterial.service.shutdown;

import com.hvisions.rawmaterial.entity.shutdown.TMpdSiloInventory;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 实时库存服务接口
 * 负责从中控系统获取实时库存数据
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface RealTimeInventoryService {
    
    /**
     * 根据筒仓编码获取实时库存
     * 
     * @param siloCode 筒仓编码
     * @return 库存量
     */
    BigDecimal getRealTimeInventoryBySiloCode(String siloCode);
    
    /**
     * 批量获取筒仓实时库存
     * 
     * @param siloCodes 筒仓编码列表
     * @return 筒仓编码与库存量的映射
     */
    Map<String, BigDecimal> getBatchRealTimeInventory(List<String> siloCodes);
    
    /**
     * 根据物料类型获取所有相关筒仓的实时库存
     * 
     * @param materialType 物料类型（高粱、稻壳）
     * @return 筒仓库存信息列表
     */
    List<TMpdSiloInventory> getRealTimeInventoryByMaterialType(String materialType);
    
    /**
     * 刷新指定筒仓的库存缓存
     * 
     * @param siloCode 筒仓编码
     */
    void refreshInventoryCache(String siloCode);
    
    /**
     * 刷新所有筒仓的库存缓存
     */
    void refreshAllInventoryCache();
    
    /**
     * 检查中控系统连接状态
     * 
     * @return 连接状态
     */
    boolean checkCentralControlConnection();
}