package com.hvisions.rawmaterial.service.async;

import com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage;
import com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage;
import com.hvisions.rawmaterial.dto.async.TaskStatusMessage;

import java.util.List;

/**
 * 异步任务服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AsyncTaskService {
    
    /**
     * 提交SAP同步任务
     * 
     * @param message SAP同步任务消息
     * @return 任务ID
     */
    String submitSapSyncTask(SapSyncTaskMessage message);
    
    /**
     * 提交批量生成任务
     * 
     * @param message 批量生成任务消息
     * @return 任务ID
     */
    String submitBatchGenerateTask(BatchGenerateTaskMessage message);
    
    /**
     * 查询任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    TaskStatusMessage getTaskStatus(String taskId);
    
    /**
     * 查询用户的任务列表
     * 
     * @param userId 用户ID
     * @param taskType 任务类型（可选）
     * @return 任务状态列表
     */
    List<TaskStatusMessage> getUserTasks(Integer userId, String taskType);
    
    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelTask(String taskId);
    
    /**
     * 清理过期任务状态
     * 
     * @param expireDays 过期天数
     * @return 清理数量
     */
    int cleanExpiredTasks(int expireDays);
}