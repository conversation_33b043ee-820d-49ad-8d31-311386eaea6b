package com.hvisions.rawmaterial.service.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 缓存一致性管理器
 * 负责维护缓存与数据库的一致性
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class CacheConsistencyManager {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheConsistencyManager.class);
    
    @Autowired
    private CacheService cacheService;
    
    // 缓存键前缀常量
    private static final String INVENTORY_DIFFERENCE_PREFIX = "inventory:difference:";
    private static final String SAP_STOCK_PREFIX = "sap:stock:";
    private static final String MES_DATA_PREFIX = "mes:data:";
    private static final String STATISTICS_PREFIX = "statistics:";
    private static final String QUERY_RESULT_PREFIX = "query:result:";
    
    /**
     * 清除库存差异相关缓存
     * 
     * @param materialCode 物料编码
     * @param warehouseCode 仓库编码
     */
    public void evictInventoryDifferenceCache(String materialCode, String warehouseCode) {
        try {
            // 清除具体物料和仓库的缓存
            if (materialCode != null && warehouseCode != null) {
                String specificKey = INVENTORY_DIFFERENCE_PREFIX + materialCode + ":" + warehouseCode;
                cacheService.delete(specificKey);
            }
            
            // 清除相关的统计缓存
            cacheService.deleteByPattern(STATISTICS_PREFIX + "*");
            
            // 清除查询结果缓存
            cacheService.deleteByPattern(QUERY_RESULT_PREFIX + "*");
            
            logger.info("Evicted inventory difference cache for material: {}, warehouse: {}", 
                       materialCode, warehouseCode);
        } catch (Exception e) {
            logger.error("Failed to evict inventory difference cache", e);
        }
    }
    
    /**
     * 清除SAP库存缓存
     * 
     * @param materialCode 物料编码
     */
    public void evictSapStockCache(String materialCode) {
        try {
            if (materialCode != null) {
                String pattern = SAP_STOCK_PREFIX + materialCode + "*";
                cacheService.deleteByPattern(pattern);
            } else {
                cacheService.deleteByPattern(SAP_STOCK_PREFIX + "*");
            }
            
            logger.info("Evicted SAP stock cache for material: {}", materialCode);
        } catch (Exception e) {
            logger.error("Failed to evict SAP stock cache", e);
        }
    }
    
    /**
     * 清除MES数据缓存
     * 
     * @param materialCode 物料编码
     */
    public void evictMesDataCache(String materialCode) {
        try {
            if (materialCode != null) {
                String pattern = MES_DATA_PREFIX + materialCode + "*";
                cacheService.deleteByPattern(pattern);
            } else {
                cacheService.deleteByPattern(MES_DATA_PREFIX + "*");
            }
            
            logger.info("Evicted MES data cache for material: {}", materialCode);
        } catch (Exception e) {
            logger.error("Failed to evict MES data cache", e);
        }
    }
    
    /**
     * 清除统计数据缓存
     */
    public void evictStatisticsCache() {
        try {
            cacheService.deleteByPattern(STATISTICS_PREFIX + "*");
            logger.info("Evicted all statistics cache");
        } catch (Exception e) {
            logger.error("Failed to evict statistics cache", e);
        }
    }
    
    /**
     * 清除查询结果缓存
     * 
     * @param queryType 查询类型
     */
    public void evictQueryResultCache(String queryType) {
        try {
            if (queryType != null) {
                String pattern = QUERY_RESULT_PREFIX + queryType + "*";
                cacheService.deleteByPattern(pattern);
            } else {
                cacheService.deleteByPattern(QUERY_RESULT_PREFIX + "*");
            }
            
            logger.info("Evicted query result cache for type: {}", queryType);
        } catch (Exception e) {
            logger.error("Failed to evict query result cache", e);
        }
    }
    
    /**
     * 批量清除相关缓存
     * 当数据发生变更时，清除所有相关缓存
     * 
     * @param materialCodes 物料编码列表
     */
    public void evictRelatedCaches(List<String> materialCodes) {
        try {
            // 清除库存差异缓存
            for (String materialCode : materialCodes) {
                evictInventoryDifferenceCache(materialCode, null);
                evictSapStockCache(materialCode);
                evictMesDataCache(materialCode);
            }
            
            // 清除统计和查询结果缓存
            evictStatisticsCache();
            evictQueryResultCache(null);
            
            logger.info("Evicted all related caches for materials: {}", materialCodes);
        } catch (Exception e) {
            logger.error("Failed to evict related caches", e);
        }
    }
    
    /**
     * 清除所有业务相关缓存
     * 用于系统维护或数据重置
     */
    public void evictAllBusinessCache() {
        try {
            List<String> patterns = Arrays.asList(
                INVENTORY_DIFFERENCE_PREFIX + "*",
                SAP_STOCK_PREFIX + "*",
                MES_DATA_PREFIX + "*",
                STATISTICS_PREFIX + "*",
                QUERY_RESULT_PREFIX + "*"
            );
            
            for (String pattern : patterns) {
                cacheService.deleteByPattern(pattern);
            }
            
            logger.info("Evicted all business cache");
        } catch (Exception e) {
            logger.error("Failed to evict all business cache", e);
        }
    }
    
    /**
     * 预热缓存
     * 在系统启动或数据更新后预加载热点数据
     */
    public void warmUpCache() {
        try {
            // 这里可以预加载一些热点数据到缓存
            // 例如：常用的统计数据、配置信息等
            logger.info("Cache warm-up completed");
        } catch (Exception e) {
            logger.error("Failed to warm up cache", e);
        }
    }
}