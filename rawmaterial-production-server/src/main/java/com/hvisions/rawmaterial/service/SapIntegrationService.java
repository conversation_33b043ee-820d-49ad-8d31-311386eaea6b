package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.dto.SapSyncResultDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: SAP系统集成服务接口
 * @Date: 2024/07/28
 */
public interface SapIntegrationService {

    /**
     * 获取SAP库存数据
     *
     * @param materialCode 物料编码
     * @param warehouseCode 仓库编码
     * @return SAP库存数据列表
     */
    List<SapStockSyncDTO> getSapStockData(String materialCode, String warehouseCode);

    /**
     * 同步库存差异到SAP
     *
     * @param difference 库存差异数据
     * @return 同步结果
     */
    SapSyncResultDTO syncDifferenceToSap(InventoryDifferenceDTO difference);

    /**
     * 批量同步库存数据
     *
     * @param materialCodes 物料编码列表
     * @return 批量同步结果
     */
    SapSyncResultDTO batchSyncStock(List<String> materialCodes);

    /**
     * 批量获取SAP库存数据
     *
     * @param materialCodes 物料编码列表
     * @param warehouseCode 仓库编码
     * @return SAP库存数据列表
     */
    List<SapStockSyncDTO> batchGetSapStockData(List<String> materialCodes, String warehouseCode);

    /**
     * 同步单个库存差异记录到SAP
     *
     * @param materialCode 物料编码
     * @param warehouseCode 仓库编码
     * @param differenceQuantity 差异数量
     * @param movementType 移动类型
     * @return 同步结果
     */
    SapSyncResultDTO syncSingleDifference(String materialCode, String warehouseCode, 
                                         java.math.BigDecimal differenceQuantity, String movementType);

    /**
     * 测试SAP连接
     *
     * @return 连接测试结果
     */
    boolean testSapConnection();

    /**
     * 全量同步SAP库存（异步方法支持）
     *
     * @return 同步结果
     */
    SapStockSyncDTO syncAllStock();

    /**
     * 批量同步库存数据（异步方法支持）
     *
     * @param materialCodes 物料编码列表
     * @return 同步结果
     */
    SapStockSyncDTO batchSyncStock(List<String> materialCodes);
}