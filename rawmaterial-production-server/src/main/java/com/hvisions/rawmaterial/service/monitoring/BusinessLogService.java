package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dto.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceProcessDTO;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;

/**
 * 业务日志服务接口
 * 负责记录关键业务操作的日志
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface BusinessLogService {
    
    /**
     * 记录差异处理操作日志
     * 
     * @param processDTO 处理请求数据
     * @param result 处理结果
     * @param userId 操作用户ID
     * @param userName 操作用户名
     */
    void logDifferenceProcessing(InventoryDifferenceProcessDTO processDTO, String result, Integer userId, String userName);
    
    /**
     * 记录SAP库存同步日志
     * 
     * @param syncResult 同步结果
     * @param userId 操作用户ID
     * @param userName 操作用户名
     */
    void logSapStockSync(SapStockSyncDTO syncResult, Integer userId, String userName);
    
    /**
     * 记录差异记录生成日志
     * 
     * @param recordCount 生成记录数量
     * @param userId 操作用户ID
     * @param userName 操作用户名
     */
    void logDifferenceRecordGeneration(int recordCount, Integer userId, String userName);
    
    /**
     * 记录查询操作日志
     * 
     * @param queryType 查询类型
     * @param queryParams 查询参数
     * @param resultCount 结果数量
     * @param userId 操作用户ID
     * @param userName 操作用户名
     */
    void logQueryOperation(String queryType, String queryParams, int resultCount, Integer userId, String userName);
    
    /**
     * 记录异常操作日志
     * 
     * @param operation 操作类型
     * @param errorMessage 错误信息
     * @param exception 异常对象
     * @param userId 操作用户ID
     * @param userName 操作用户名
     */
    void logErrorOperation(String operation, String errorMessage, Exception exception, Integer userId, String userName);
}