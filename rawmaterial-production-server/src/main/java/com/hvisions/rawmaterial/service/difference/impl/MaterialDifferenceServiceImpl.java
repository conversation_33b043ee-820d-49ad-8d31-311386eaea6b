package com.hvisions.rawmaterial.service.difference.impl;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.difference.MaterialDifferenceMapper;
import com.hvisions.rawmaterial.dto.difference.MaterialDifferenceDTO;
import com.hvisions.rawmaterial.dto.difference.MaterialDifferenceProcessDTO;
import com.hvisions.rawmaterial.dto.difference.MaterialDifferenceQueryDTO;
import com.hvisions.rawmaterial.dto.difference.SapStockSyncDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdMaterialDifference;
import com.hvisions.rawmaterial.service.difference.MaterialDifferenceService;
import com.hvisions.rawmaterial.service.MesDataService;
import com.hvisions.rawmaterial.service.SapIntegrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 原辅料差异处理Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Slf4j
@Service
public class MaterialDifferenceServiceImpl implements MaterialDifferenceService {

    @Autowired
    private MaterialDifferenceMapper materialDifferenceMapper;
    
    @Autowired
    private MesDataService mesDataService;
    
    @Autowired
    private SapIntegrationService sapIntegrationService;

    @Override
    public Page<MaterialDifferenceDTO> findByCondition(MaterialDifferenceQueryDTO queryDTO) {
        log.info("分页查询原辅料差异处理，查询条件：{}", queryDTO);
        
        // 设置分页
        PageHelperUtil.setPage(queryDTO);
        
        // 查询数据
        List<TMpdMaterialDifference> entities = materialDifferenceMapper.selectByCondition(queryDTO);
        
        // 转换为DTO
        List<MaterialDifferenceDTO> dtoList = entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return PageHelperUtil.getPageResult(dtoList);
    }

    @Override
    public MaterialDifferenceDTO findById(Integer id) {
        log.info("根据ID查询原辅料差异处理，ID：{}", id);
        
        TMpdMaterialDifference entity = materialDifferenceMapper.selectById(id);
        if (entity == null || entity.getDeleted()) {
            return null;
        }
        
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String processDifference(MaterialDifferenceProcessDTO processDTO) {
        log.info("处理差异，请求：{}", processDTO);
        
        // 业务规则验证
        validateProcessRequest(processDTO);
        
        TMpdMaterialDifference entity = materialDifferenceMapper.selectById(processDTO.getId());
        if (entity == null || entity.getDeleted()) {
            throw new IllegalArgumentException("差异记录不存在，ID: " + processDTO.getId());
        }
        
        if (entity.getStatus() != 0) {
            throw new IllegalStateException("该差异记录已处理，不能重复处理。当前状态: " + entity.getStatus());
        }
        
        // 验证统计结束日期不能早于开始日期
        if (processDTO.getStatisticsEndDate().before(entity.getStatisticsStartDate())) {
            throw new IllegalArgumentException("统计结束日期不能早于开始日期");
        }
        
        try {
            // 更新当前记录为已处理状态
            entity.setStatus(1);
            entity.setStatisticsEndDate(processDTO.getStatisticsEndDate());
            entity.setProcessor(processDTO.getProcessor());
            entity.setProcessorId(processDTO.getProcessorId());
            entity.setProcessTime(new Date());
            entity.setProcessRemark(processDTO.getProcessRemark());
            entity.setUpdateTime(new Date());
            
            int updateCount = materialDifferenceMapper.updateById(entity);
            if (updateCount == 0) {
                throw new RuntimeException("更新差异记录失败，可能记录已被其他用户修改");
            }
            
            // 生成下一周期的差异记录（数据继承逻辑）
            generateNextPeriodRecord(entity);
            
            log.info("处理差异成功，差异记录ID：{}", processDTO.getId());
            return "处理成功";
            
        } catch (Exception e) {
            log.error("处理差异失败，差异记录ID：{}", processDTO.getId(), e);
            throw new RuntimeException("处理差异失败: " + e.getMessage(), e);
        }
    }

    @Override
    public SapStockSyncDTO syncSapStock() {
        log.info("开始同步SAP库存");
        
        SapStockSyncDTO syncResult = new SapStockSyncDTO();
        syncResult.setSyncTime(new Date());
        
        try {
            // 查询需要同步的物料仓库组合
            List<Map<String, Object>> combinations = materialDifferenceMapper.selectMaterialWarehouseCombinations();
            
            List<SapStockSyncDTO.SapStockData> stockDataList = new ArrayList<>();
            int successCount = 0;
            int failedCount = 0;
            
            for (Map<String, Object> combination : combinations) {
                String materialCode = (String) combination.get("material_code");
                String warehouseCode = (String) combination.get("erp_warehouse_code");
                
                try {
                    // 调用SAP接口获取库存（这里模拟）
                    BigDecimal sapStock = mockGetSapStock(materialCode, warehouseCode);
                    
                    // 更新数据库中的SAP库存
                    int updateCount = materialDifferenceMapper.updateSapStock(
                        materialCode, warehouseCode, sapStock, new Date()
                    );
                    
                    SapStockSyncDTO.SapStockData stockData = new SapStockSyncDTO.SapStockData();
                    stockData.setMaterialCode(materialCode);
                    stockData.setMaterialName((String) combination.get("material_name"));
                    stockData.setWarehouseCode(warehouseCode);
                    stockData.setWarehouseName((String) combination.get("erp_warehouse_name"));
                    stockData.setStockQty(sapStock);
                    stockData.setUnit((String) combination.get("unit"));
                    stockData.setSyncStatus("success");
                    stockData.setSyncMessage("同步成功");
                    
                    stockDataList.add(stockData);
                    successCount++;
                    
                } catch (Exception e) {
                    log.error("同步SAP库存失败，物料：{}，仓库：{}", materialCode, warehouseCode, e);
                    
                    SapStockSyncDTO.SapStockData stockData = new SapStockSyncDTO.SapStockData();
                    stockData.setMaterialCode(materialCode);
                    stockData.setWarehouseCode(warehouseCode);
                    stockData.setSyncStatus("failed");
                    stockData.setSyncMessage("同步失败：" + e.getMessage());
                    
                    stockDataList.add(stockData);
                    failedCount++;
                }
            }
            
            syncResult.setSyncStatus("success");
            syncResult.setSyncMessage("同步完成");
            syncResult.setStockDataList(stockDataList);
            syncResult.setSuccessCount(successCount);
            syncResult.setFailedCount(failedCount);
            
        } catch (Exception e) {
            log.error("同步SAP库存失败", e);
            syncResult.setSyncStatus("failed");
            syncResult.setSyncMessage("同步失败：" + e.getMessage());
            syncResult.setSuccessCount(0);
            syncResult.setFailedCount(0);
        }
        
        log.info("SAP库存同步完成，成功：{}，失败：{}", syncResult.getSuccessCount(), syncResult.getFailedCount());
        return syncResult;
    }

    @Override
    public SapStockSyncDTO syncSapStockByMaterial(String materialCode, String warehouseCode) {
        log.info("手动同步指定物料的SAP库存，物料：{}，仓库：{}", materialCode, warehouseCode);
        
        SapStockSyncDTO syncResult = new SapStockSyncDTO();
        syncResult.setSyncTime(new Date());
        
        try {
            // 调用SAP接口获取库存（这里模拟）
            BigDecimal sapStock = mockGetSapStock(materialCode, warehouseCode);
            
            // 更新数据库中的SAP库存
            int updateCount = materialDifferenceMapper.updateSapStock(
                materialCode, warehouseCode, sapStock, new Date()
            );
            
            SapStockSyncDTO.SapStockData stockData = new SapStockSyncDTO.SapStockData();
            stockData.setMaterialCode(materialCode);
            stockData.setWarehouseCode(warehouseCode);
            stockData.setStockQty(sapStock);
            stockData.setSyncStatus("success");
            stockData.setSyncMessage("同步成功");
            
            syncResult.setSyncStatus("success");
            syncResult.setSyncMessage("同步成功");
            syncResult.setStockDataList(Arrays.asList(stockData));
            syncResult.setSuccessCount(1);
            syncResult.setFailedCount(0);
            
        } catch (Exception e) {
            log.error("同步指定物料SAP库存失败", e);
            syncResult.setSyncStatus("failed");
            syncResult.setSyncMessage("同步失败：" + e.getMessage());
            syncResult.setSuccessCount(0);
            syncResult.setFailedCount(1);
        }
        
        return syncResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateDifferenceRecords() {
        log.info("开始生成差异记录");
        
        try {
            // 查询需要生成差异记录的物料仓库组合
            List<Map<String, Object>> combinations = materialDifferenceMapper.selectMaterialWarehouseCombinations();
            if (combinations == null || combinations.isEmpty()) {
                log.warn("未找到需要生成差异记录的物料仓库组合");
                return "未找到需要生成差异记录的物料仓库组合";
            }
            
            List<TMpdMaterialDifference> differenceList = new ArrayList<>();
            Date now = new Date();
            int skippedCount = 0;
            int errorCount = 0;
            
            for (Map<String, Object> combination : combinations) {
                String materialCode = (String) combination.get("material_code");
                String warehouseCode = (String) combination.get("erp_warehouse_code");
                
                try {
                    // 检查是否已存在待处理的记录
                    TMpdMaterialDifference existing = materialDifferenceMapper.selectOne(
                        new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<TMpdMaterialDifference>()
                            .eq("material_code", materialCode)
                            .eq("erp_warehouse_code", warehouseCode)
                            .eq("status", 0)
                            .eq("deleted", false)
                    );
                    
                    if (existing != null) {
                        skippedCount++;
                        log.debug("物料{}仓库{}已存在待处理记录，跳过", materialCode, warehouseCode);
                        continue; // 已存在待处理记录，跳过
                    }
                    
                    // 查询最新的已处理记录，获取统计开始日期
                    TMpdMaterialDifference latestProcessed = materialDifferenceMapper.selectLatestProcessedRecord(
                        materialCode, warehouseCode
                    );
                    
                    Date statisticsStartDate = latestProcessed != null ? 
                        latestProcessed.getStatisticsEndDate() : 
                        getDefaultStartDate();
                    
                    // 计算差异数据
                    TMpdMaterialDifference difference = calculateDifferenceWithValidation(
                        combination, statisticsStartDate, now
                    );
                    
                    if (difference != null) {
                        differenceList.add(difference);
                    }
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("生成物料{}仓库{}的差异记录失败", materialCode, warehouseCode, e);
                }
            }
            
            // 批量插入差异记录
            if (!differenceList.isEmpty()) {
                int insertCount = materialDifferenceMapper.batchInsert(differenceList);
                log.info("批量插入差异记录完成，预期插入{}条，实际插入{}条", differenceList.size(), insertCount);
            }
            
            String result = String.format("生成差异记录完成，共生成%d条记录，跳过%d条，失败%d条", 
                differenceList.size(), skippedCount, errorCount);
            log.info(result);
            return result;
            
        } catch (Exception e) {
            log.error("生成差异记录失败", e);
            throw new RuntimeException("生成差异记录失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialDifferenceDTO recalculateDifference(Integer id) {
        log.info("重新计算差异，ID：{}", id);
        
        if (id == null) {
            throw new IllegalArgumentException("差异记录ID不能为空");
        }
        
        TMpdMaterialDifference entity = materialDifferenceMapper.selectById(id);
        if (entity == null || entity.getDeleted()) {
            throw new IllegalArgumentException("差异记录不存在，ID: " + id);
        }
        
        if (entity.getStatus() != 0) {
            throw new IllegalStateException("已处理的差异记录不能重新计算，当前状态: " + entity.getStatus());
        }
        
        try {
            // 重新计算差异数据
            Date statisticsStartDate = entity.getStatisticsStartDate();
            Date statisticsEndDate = new Date();
            
            // 使用MES数据服务获取最新数据
            BigDecimal mesCurrentStock = mesDataService.getMesCurrentStock(
                entity.getMaterialCode(), entity.getErpWarehouseCode()
            );
            
            BigDecimal weighbridgeReceiptQty = mesDataService.getWeighbridgeReceiptQuantity(
                entity.getMaterialCode(), statisticsStartDate, statisticsEndDate
            );
            
            BigDecimal solidWasteQty = mesDataService.getSolidWasteQuantity(
                entity.getMaterialCode(), statisticsStartDate, statisticsEndDate
            );
            
            BigDecimal issueQty = mesDataService.getIssuedQuantity(
                entity.getMaterialCode(), statisticsStartDate, statisticsEndDate
            );
            
            // 获取SAP库存数据
            BigDecimal sapStock = getSapStockWithFallback(entity.getMaterialCode(), entity.getErpWarehouseCode());
            
            // 计算理论库存和差异数量
            BigDecimal theoreticalStock = calculateTheoreticalStock(
                entity.getMesInitialStock(), weighbridgeReceiptQty, solidWasteQty, issueQty
            );
            
            BigDecimal differenceQty = calculateDifferenceQuantity(mesCurrentStock, theoreticalStock);
            
            // 更新实体
            entity.setMesCurrentStock(mesCurrentStock);
            entity.setWeighbridgeReceiptQty(weighbridgeReceiptQty);
            entity.setSolidWasteQty(solidWasteQty);
            entity.setIssueQty(issueQty);
            entity.setSapStock(sapStock);
            entity.setDifferenceQty(differenceQty);
            entity.setUpdateTime(new Date());
            
            int updateCount = materialDifferenceMapper.updateById(entity);
            if (updateCount == 0) {
                throw new RuntimeException("更新差异记录失败，可能记录已被其他用户修改");
            }
            
            log.info("重新计算差异完成，物料：{}，仓库：{}，差异数量：{}", 
                entity.getMaterialCode(), entity.getErpWarehouseCode(), differenceQty);
            
            return convertToDTO(entity);
            
        } catch (Exception e) {
            log.error("重新计算差异失败，ID：{}", id, e);
            throw new RuntimeException("重新计算差异失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getDifferenceStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取待处理统计
        Map<String, Object> pendingStats = materialDifferenceMapper.selectPendingStatistics();
        statistics.put("pending", pendingStats);
        
        // 获取最近7天已处理统计
        Calendar calendar = Calendar.getInstance();
        Date endDate = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date startDate = calendar.getTime();
        
        Map<String, Object> processedStats = materialDifferenceMapper.selectProcessedStatistics(startDate, endDate);
        statistics.put("processed", processedStats);
        
        return statistics;
    }

    @Override
    public Map<String, Object> getPendingStatistics() {
        return materialDifferenceMapper.selectPendingStatistics();
    }

    @Override
    public Map<String, Object> getProcessedStatistics(Integer days) {
        Calendar calendar = Calendar.getInstance();
        Date endDate = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startDate = calendar.getTime();
        
        return materialDifferenceMapper.selectProcessedStatistics(startDate, endDate);
    }

    @Override
    public Boolean hasPendingDifferences() {
        Map<String, Object> pendingStats = materialDifferenceMapper.selectPendingStatistics();
        Long totalCount = (Long) pendingStats.get("total_count");
        return totalCount != null && totalCount > 0;
    }

    @Override
    public MaterialDifferenceDTO getDifferenceDetail(Integer id) {
        MaterialDifferenceDTO dto = findById(id);
        if (dto != null) {
            // 计算扩展字段
            calculateExtendedFields(dto);
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchProcessDifference(List<Integer> ids, String processor, Integer processorId, String processRemark) {
        log.info("批量处理差异，IDs：{}，处理人：{}", ids, processor);
        
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("请选择要处理的差异记录");
        }
        
        Date processTime = new Date();
        int processedCount = 0;
        
        for (Integer id : ids) {
            TMpdMaterialDifference entity = materialDifferenceMapper.selectById(id);
            if (entity != null && !entity.getDeleted() && entity.getStatus() == 0) {
                entity.setStatus(1);
                entity.setProcessor(processor);
                entity.setProcessorId(processorId);
                entity.setProcessTime(processTime);
                entity.setProcessRemark(processRemark);
                entity.setStatisticsEndDate(processTime);
                entity.setUpdateTime(processTime);
                
                materialDifferenceMapper.updateById(entity);
                
                // 生成下一周期的差异记录
                generateNextPeriodRecord(entity);
                
                processedCount++;
            }
        }
        
        log.info("批量处理差异完成，共处理{}条记录", processedCount);
        return String.format("批量处理成功，共处理%d条记录", processedCount);
    }

    @Override
    public String exportDifferenceRecords(MaterialDifferenceQueryDTO queryDTO) {
        // TODO: 实现导出功能
        log.info("导出差异记录，查询条件：{}", queryDTO);
        return "导出功能待实现";
    }

    /**
     * 转换为DTO
     */
    private MaterialDifferenceDTO convertToDTO(TMpdMaterialDifference entity) {
        if (entity == null) {
            return null;
        }
        
        MaterialDifferenceDTO dto = new MaterialDifferenceDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 设置状态描述
        dto.setStatusDesc(entity.getStatus() == 0 ? "待处理" : "已处理");
        
        // 计算扩展字段
        calculateExtendedFields(dto);
        
        return dto;
    }

    /**
     * 计算扩展字段
     */
    private void calculateExtendedFields(MaterialDifferenceDTO dto) {
        if (dto.getMesInitialStock() != null && dto.getWeighbridgeReceiptQty() != null &&
            dto.getSolidWasteQty() != null && dto.getIssueQty() != null) {
            
            // 计算理论库存
            BigDecimal theoreticalStock = dto.getMesInitialStock()
                .add(dto.getWeighbridgeReceiptQty())
                .subtract(dto.getSolidWasteQty())
                .subtract(dto.getIssueQty());
            dto.setTheoreticalStock(theoreticalStock);
            
            // 计算差异率
            if (theoreticalStock.compareTo(BigDecimal.ZERO) != 0 && dto.getDifferenceQty() != null) {
                BigDecimal differenceRate = dto.getDifferenceQty()
                    .divide(theoreticalStock, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
                dto.setDifferenceRate(differenceRate);
            }
        }
        
        // 设置是否可处理
        dto.setCanProcess(dto.getStatus() == 0);
        
        // 设置处理建议
        if (dto.getDifferenceQty() != null) {
            BigDecimal absDifference = dto.getDifferenceQty().abs();
            if (absDifference.compareTo(new BigDecimal("100")) <= 0) {
                dto.setProcessSuggestion("差异较小，建议直接处理");
            } else if (absDifference.compareTo(new BigDecimal("1000")) <= 0) {
                dto.setProcessSuggestion("差异适中，需要核实后处理");
            } else {
                dto.setProcessSuggestion("差异较大，建议详细核查");
            }
        }
    }

    /**
     * 模拟获取SAP库存
     */
    private BigDecimal mockGetSapStock(String materialCode, String warehouseCode) {
        // 这里应该调用实际的SAP接口
        // 现在返回模拟数据
        Random random = new Random();
        return new BigDecimal(random.nextInt(10000) + 1000);
    }

    /**
     * 获取默认开始日期
     */
    private Date getDefaultStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 计算差异数据（带验证）
     */
    private TMpdMaterialDifference calculateDifferenceWithValidation(Map<String, Object> combination, 
                                                                   Date statisticsStartDate, Date statisticsEndDate) {
        String materialCode = (String) combination.get("material_code");
        String warehouseCode = (String) combination.get("erp_warehouse_code");
        
        if (!StringUtils.hasText(materialCode) || !StringUtils.hasText(warehouseCode)) {
            log.warn("物料编码或仓库编码为空，跳过：materialCode={}, warehouseCode={}", materialCode, warehouseCode);
            return null;
        }
        
        try {
            // 使用MES数据服务获取各项数据
            BigDecimal mesCurrentStock = mesDataService.getMesCurrentStock(materialCode, warehouseCode);
            BigDecimal weighbridgeReceiptQty = mesDataService.getWeighbridgeReceiptQuantity(
                materialCode, statisticsStartDate, statisticsEndDate
            );
            BigDecimal solidWasteQty = mesDataService.getSolidWasteQuantity(
                materialCode, statisticsStartDate, statisticsEndDate
            );
            BigDecimal issueQty = mesDataService.getIssuedQuantity(
                materialCode, statisticsStartDate, statisticsEndDate
            );
            
            // 获取SAP库存数据
            BigDecimal sapStock = getSapStockWithFallback(materialCode, warehouseCode);
            
            // 获取期初库存（数据继承逻辑：新记录的期初库存 = 上一记录的当前库存）
            BigDecimal mesInitialStock = getInitialStockFromLastRecord(materialCode, warehouseCode);
            
            // 计算理论库存和差异数量
            BigDecimal theoreticalStock = calculateTheoreticalStock(
                mesInitialStock, weighbridgeReceiptQty, solidWasteQty, issueQty
            );
            BigDecimal differenceQty = calculateDifferenceQuantity(mesCurrentStock, theoreticalStock);
            
            // 构建差异记录
            TMpdMaterialDifference difference = TMpdMaterialDifference.builder()
                .materialCode(materialCode)
                .materialName((String) combination.get("material_name"))
                .erpWarehouseCode(warehouseCode)
                .erpWarehouseName((String) combination.get("erp_warehouse_name"))
                .department("原辅料管理部")
                .mesCurrentStock(mesCurrentStock)
                .mesInitialStock(mesInitialStock)
                .weighbridgeReceiptQty(weighbridgeReceiptQty)
                .solidWasteQty(solidWasteQty)
                .issueQty(issueQty)
                .unit((String) combination.get("unit"))
                .sapStock(sapStock)
                .differenceQty(differenceQty)
                .statisticsStartDate(statisticsStartDate)
                .statisticsEndDate(null) // 待处理时为空
                .status(0) // 待处理
                .createTime(statisticsEndDate)
                .updateTime(statisticsEndDate)
                .deleted(false)
                .build();
            
            log.debug("计算差异数据完成，物料：{}，仓库：{}，差异数量：{}", materialCode, warehouseCode, differenceQty);
            return difference;
            
        } catch (Exception e) {
            log.error("计算差异数据失败，物料：{}，仓库：{}", materialCode, warehouseCode, e);
            return null;
        }
    }
    
    /**
     * 获取期初库存（数据继承逻辑）
     */
    private BigDecimal getInitialStockFromLastRecord(String materialCode, String warehouseCode) {
        TMpdMaterialDifference lastProcessed = materialDifferenceMapper.selectLatestProcessedRecord(
            materialCode, warehouseCode
        );
        
        // 新记录的期初库存 = 上一记录的当前库存
        return lastProcessed != null && lastProcessed.getMesCurrentStock() != null ? 
            lastProcessed.getMesCurrentStock() : BigDecimal.ZERO;
    }
    
    /**
     * 计算理论库存
     */
    private BigDecimal calculateTheoreticalStock(BigDecimal initialStock, BigDecimal receiptQty, 
                                               BigDecimal wasteQty, BigDecimal issueQty) {
        BigDecimal theoretical = initialStock != null ? initialStock : BigDecimal.ZERO;
        
        if (receiptQty != null) {
            theoretical = theoretical.add(receiptQty);
        }
        if (wasteQty != null) {
            theoretical = theoretical.subtract(wasteQty);
        }
        if (issueQty != null) {
            theoretical = theoretical.subtract(issueQty);
        }
        
        return theoretical;
    }
    
    /**
     * 计算差异数量
     */
    private BigDecimal calculateDifferenceQuantity(BigDecimal currentStock, BigDecimal theoreticalStock) {
        BigDecimal current = currentStock != null ? currentStock : BigDecimal.ZERO;
        BigDecimal theoretical = theoreticalStock != null ? theoreticalStock : BigDecimal.ZERO;
        return current.subtract(theoretical);
    }
    
    /**
     * 获取SAP库存（带降级处理）
     */
    private BigDecimal getSapStockWithFallback(String materialCode, String warehouseCode) {
        try {
            // 首先尝试从数据库获取
            BigDecimal sapStock = materialDifferenceMapper.selectSapStock(materialCode, warehouseCode);
            if (sapStock != null) {
                return sapStock;
            }
            
            // 如果数据库中没有，尝试从SAP接口获取
            return sapIntegrationService.getSapStockData(materialCode, warehouseCode)
                .stream()
                .findFirst()
                .map(stock -> stock.getStockQty())
                .orElse(BigDecimal.ZERO);
                
        } catch (Exception e) {
            log.warn("获取SAP库存失败，使用默认值0，物料：{}，仓库：{}", materialCode, warehouseCode, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 验证处理请求
     */
    private void validateProcessRequest(MaterialDifferenceProcessDTO processDTO) {
        if (processDTO == null) {
            throw new IllegalArgumentException("处理请求不能为空");
        }
        
        if (processDTO.getId() == null) {
            throw new IllegalArgumentException("差异记录ID不能为空");
        }
        
        if (processDTO.getStatisticsEndDate() == null) {
            throw new IllegalArgumentException("统计结束日期不能为空");
        }
        
        if (processDTO.getStatisticsEndDate().after(new Date())) {
            throw new IllegalArgumentException("统计结束日期不能晚于当前时间");
        }
        
        if (!StringUtils.hasText(processDTO.getProcessor())) {
            throw new IllegalArgumentException("处理人不能为空");
        }
        
        if (processDTO.getProcessorId() == null) {
            throw new IllegalArgumentException("处理人ID不能为空");
        }
    }

    /**
     * 生成下一周期的差异记录（数据继承逻辑）
     */
    private void generateNextPeriodRecord(TMpdMaterialDifference processedRecord) {
        try {
            // 检查是否已存在待处理记录
            TMpdMaterialDifference existing = materialDifferenceMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<TMpdMaterialDifference>()
                    .eq("material_code", processedRecord.getMaterialCode())
                    .eq("erp_warehouse_code", processedRecord.getErpWarehouseCode())
                    .eq("status", 0)
                    .eq("deleted", false)
            );
            
            if (existing != null) {
                log.info("物料{}仓库{}已存在待处理记录，跳过生成下一周期记录", 
                    processedRecord.getMaterialCode(), processedRecord.getErpWarehouseCode());
                return;
            }
            
            Map<String, Object> combination = new HashMap<>();
            combination.put("material_code", processedRecord.getMaterialCode());
            combination.put("material_name", processedRecord.getMaterialName());
            combination.put("erp_warehouse_code", processedRecord.getErpWarehouseCode());
            combination.put("erp_warehouse_name", processedRecord.getErpWarehouseName());
            combination.put("unit", processedRecord.getUnit());
            
            // 数据继承逻辑：新记录的统计开始时间 = 上一记录的统计结束时间
            Date nextStartDate = processedRecord.getStatisticsEndDate();
            Date nextEndDate = new Date();
            
            TMpdMaterialDifference nextRecord = calculateDifferenceWithValidation(
                combination, nextStartDate, nextEndDate
            );
            
            if (nextRecord != null) {
                // 确保数据继承正确：新记录的期初库存 = 上一记录的当前库存
                nextRecord.setMesInitialStock(processedRecord.getMesCurrentStock());
                
                int insertCount = materialDifferenceMapper.insert(nextRecord);
                if (insertCount > 0) {
                    log.info("生成下一周期差异记录成功，物料：{}，仓库：{}，期初库存：{}", 
                        processedRecord.getMaterialCode(), processedRecord.getErpWarehouseCode(),
                        nextRecord.getMesInitialStock());
                } else {
                    log.warn("生成下一周期差异记录失败，插入数据库返回0");
                }
            } else {
                log.warn("计算下一周期差异数据失败，物料：{}，仓库：{}", 
                    processedRecord.getMaterialCode(), processedRecord.getErpWarehouseCode());
            }
            
        } catch (Exception e) {
            log.error("生成下一周期差异记录失败，物料：{}，仓库：{}", 
                processedRecord.getMaterialCode(), processedRecord.getErpWarehouseCode(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}
