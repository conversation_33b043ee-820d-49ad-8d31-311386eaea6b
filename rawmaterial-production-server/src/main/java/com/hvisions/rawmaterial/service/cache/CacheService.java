package com.hvisions.rawmaterial.service.cache;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface CacheService {
    
    /**
     * 设置缓存
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit unit);
    
    /**
     * 获取缓存
     * 
     * @param key 缓存键
     * @param clazz 返回类型
     * @return 缓存值
     */
    <T> T get(String key, Class<T> clazz);
    
    /**
     * 删除缓存
     * 
     * @param key 缓存键
     */
    void delete(String key);
    
    /**
     * 批量删除缓存
     * 
     * @param keys 缓存键列表
     */
    void deleteAll(List<String> keys);
    
    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);
    
    /**
     * 设置缓存过期时间
     * 
     * @param key 缓存键
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void expire(String key, long timeout, TimeUnit unit);
    
    /**
     * 获取缓存剩余过期时间
     * 
     * @param key 缓存键
     * @return 剩余时间（秒）
     */
    long getExpire(String key);
    
    /**
     * 清空指定模式的缓存
     * 
     * @param pattern 缓存键模式
     */
    void deleteByPattern(String pattern);
    
    /**
     * 原子性递增
     * 
     * @param key 缓存键
     * @param delta 递增值
     * @return 递增后的值
     */
    long increment(String key, long delta);
    
    /**
     * 原子性递减
     * 
     * @param key 缓存键
     * @param delta 递减值
     * @return 递减后的值
     */
    long decrement(String key, long delta);
}