package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dto.monitoring.AlertRule;
import com.hvisions.rawmaterial.dto.monitoring.AlertNotification;

import java.util.List;

/**
 * 告警服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AlertService {
    
    /**
     * 检查SAP接口异常告警
     */
    void checkSapInterfaceAlerts();
    
    /**
     * 检查性能异常告警
     */
    void checkPerformanceAlerts();
    
    /**
     * 检查业务异常告警
     */
    void checkBusinessAlerts();
    
    /**
     * 发送告警通知
     * 
     * @param notification 告警通知
     */
    void sendAlert(AlertNotification notification);
    
    /**
     * 创建告警规则
     * 
     * @param rule 告警规则
     */
    void createAlertRule(AlertRule rule);
    
    /**
     * 更新告警规则
     * 
     * @param rule 告警规则
     */
    void updateAlertRule(AlertRule rule);
    
    /**
     * 删除告警规则
     * 
     * @param ruleId 规则ID
     */
    void deleteAlertRule(Integer ruleId);
    
    /**
     * 获取所有告警规则
     * 
     * @return 告警规则列表
     */
    List<AlertRule> getAllAlertRules();
    
    /**
     * 获取告警历史
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 告警历史列表
     */
    List<AlertNotification> getAlertHistory(int pageNum, int pageSize);
    
    /**
     * 确认告警
     * 
     * @param alertId 告警ID
     * @param userId 用户ID
     * @param userName 用户名
     * @param remark 备注
     */
    void acknowledgeAlert(Integer alertId, Integer userId, String userName, String remark);
}