package com.hvisions.rawmaterial.service.optimization.impl;

import com.hvisions.rawmaterial.dao.InventoryDifferenceRepository;
import com.hvisions.rawmaterial.mapper.InventoryDifferenceMapper;
import com.hvisions.rawmaterial.service.SapIntegrationService;
import com.hvisions.rawmaterial.service.cache.CacheService;
import com.hvisions.rawmaterial.service.optimization.BatchOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量操作服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class BatchOperationServiceImpl implements BatchOperationService {
    
    @Autowired
    private InventoryDifferenceRepository inventoryDifferenceRepository;
    
    @Resource
    private InventoryDifferenceMapper inventoryDifferenceMapper;
    
    @Autowired
    private SapIntegrationService sapIntegrationService;
    
    @Autowired
    private CacheService cacheService;
    
    private static final int BATCH_SIZE = 100;
    private static final int MAX_CONCURRENT_TASKS = 10;
    
    @Override
    public Map<String, Object> batchQueryInventoryDifferences(List<String> materialCodes, List<String> warehouseCodes) {
        log.info("开始批量查询库存差异数据，物料数量：{}，仓库数量：{}", 
                materialCodes.size(), warehouseCodes.size());
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 分批查询以避免SQL过长
            List<List<String>> materialBatches = partitionList(materialCodes, BATCH_SIZE);
            List<List<String>> warehouseBatches = partitionList(warehouseCodes, BATCH_SIZE);
            
            List<Object> allResults = new ArrayList<>();
            
            for (List<String> materialBatch : materialBatches) {
                for (List<String> warehouseBatch : warehouseBatches) {
                    // 使用MyBatis批量查询
                    List<Object> batchResult = inventoryDifferenceMapper.batchQueryByMaterialAndWarehouse(
                            materialBatch, warehouseBatch);
                    if (!CollectionUtils.isEmpty(batchResult)) {
                        allResults.addAll(batchResult);
                    }
                }
            }
            
            result.put("data", allResults);
            result.put("total", allResults.size());
            result.put("success", true);
            
            log.info("批量查询库存差异数据完成，共查询到{}条记录", allResults.size());
            
        } catch (Exception e) {
            log.error("批量查询库存差异数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateDifferenceStatus(List<Integer> differenceIds, Integer status) {
        log.info("开始批量更新库存差异状态，记录数量：{}，目标状态：{}", differenceIds.size(), status);
        
        if (CollectionUtils.isEmpty(differenceIds)) {
            return 0;
        }
        
        try {
            // 分批更新以避免锁表时间过长
            List<List<Integer>> batches = partitionList(differenceIds, BATCH_SIZE);
            int totalUpdated = 0;
            
            for (List<Integer> batch : batches) {
                int updated = inventoryDifferenceMapper.batchUpdateStatus(batch, status, new Date());
                totalUpdated += updated;
                
                // 清除相关缓存
                batch.forEach(id -> {
                    cacheService.delete("inventory-difference-detail:" + id);
                });
            }
            
            // 清除统计缓存
            cacheService.deleteByPattern("inventory-difference-statistics:*");
            cacheService.deleteByPattern("query-results:*");
            
            log.info("批量更新库存差异状态完成，共更新{}条记录", totalUpdated);
            return totalUpdated;
            
        } catch (Exception e) {
            log.error("批量更新库存差异状态失败", e);
            throw new RuntimeException("批量更新失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertDifferences(List<Object> differences) {
        log.info("开始批量插入库存差异记录，记录数量：{}", differences.size());
        
        if (CollectionUtils.isEmpty(differences)) {
            return 0;
        }
        
        try {
            // 分批插入以提高性能
            List<List<Object>> batches = partitionList(differences, BATCH_SIZE);
            int totalInserted = 0;
            
            for (List<Object> batch : batches) {
                int inserted = inventoryDifferenceMapper.batchInsertDifferences(batch);
                totalInserted += inserted;
            }
            
            // 清除相关缓存
            cacheService.deleteByPattern("inventory-difference-statistics:*");
            cacheService.deleteByPattern("query-results:*");
            
            log.info("批量插入库存差异记录完成，共插入{}条记录", totalInserted);
            return totalInserted;
            
        } catch (Exception e) {
            log.error("批量插入库存差异记录失败", e);
            throw new RuntimeException("批量插入失败：" + e.getMessage());
        }
    }
    
    @Override
    @Async("taskExecutor")
    public CompletableFuture<Map<String, Object>> asyncBatchSapSync(List<String> materialCodes) {
        log.info("开始异步批量SAP同步，物料数量：{}", materialCodes.size());
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 分批处理以避免SAP接口超时
            List<List<String>> batches = partitionList(materialCodes, 20); // SAP接口批次较小
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (List<String> batch : batches) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 调用SAP接口同步数据
                        for (String materialCode : batch) {
                            sapIntegrationService.getSapStockData(materialCode, null);
                            
                            // 清除相关缓存
                            cacheService.deleteByPattern("sap-stock:" + materialCode + "*");
                        }
                    } catch (Exception e) {
                        log.error("批量SAP同步失败，批次：{}", batch, e);
                    }
                });
                futures.add(future);
            }
            
            // 等待所有批次完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            result.put("success", true);
            result.put("syncedCount", materialCodes.size());
            
            log.info("异步批量SAP同步完成，物料数量：{}", materialCodes.size());
            
        } catch (Exception e) {
            log.error("异步批量SAP同步失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Override
    @Async("taskExecutor")
    public void batchDeleteExpiredCache(List<String> cacheKeys) {
        log.info("开始批量删除过期缓存，缓存键数量：{}", cacheKeys.size());
        
        try {
            // 分批删除以避免Redis压力过大
            List<List<String>> batches = partitionList(cacheKeys, BATCH_SIZE);
            
            for (List<String> batch : batches) {
                // 检查缓存是否过期
                List<String> expiredKeys = batch.stream()
                        .filter(key -> {
                            long expire = cacheService.getExpire(key);
                            return expire <= 0; // 已过期或不存在
                        })
                        .collect(Collectors.toList());
                
                if (!CollectionUtils.isEmpty(expiredKeys)) {
                    cacheService.deleteAll(expiredKeys);
                }
            }
            
            log.info("批量删除过期缓存完成");
            
        } catch (Exception e) {
            log.error("批量删除过期缓存失败", e);
        }
    }
    
    @Override
    @Async("taskExecutor")
    public CompletableFuture<Map<String, Object>> batchPreloadHotData(List<String> dataKeys) {
        log.info("开始批量预加载热点数据，数据键数量：{}", dataKeys.size());
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> preloadedData = new HashMap<>();
            
            // 并发预加载数据
            List<CompletableFuture<Void>> futures = dataKeys.stream()
                    .map(dataKey -> CompletableFuture.runAsync(() -> {
                        try {
                            // 根据数据键类型预加载不同的数据
                            Object data = preloadDataByKey(dataKey);
                            if (data != null) {
                                synchronized (preloadedData) {
                                    preloadedData.put(dataKey, data);
                                }
                                
                                // 设置缓存
                                cacheService.set(dataKey, data, 30, TimeUnit.MINUTES);
                            }
                        } catch (Exception e) {
                            log.error("预加载数据失败，数据键：{}", dataKey, e);
                        }
                    }))
                    .collect(Collectors.toList());
            
            // 等待所有预加载完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            result.put("success", true);
            result.put("preloadedCount", preloadedData.size());
            result.put("data", preloadedData);
            
            log.info("批量预加载热点数据完成，成功预加载{}项数据", preloadedData.size());
            
        } catch (Exception e) {
            log.error("批量预加载热点数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 根据数据键预加载数据
     */
    private Object preloadDataByKey(String dataKey) {
        // 根据不同的数据键类型预加载不同的数据
        if (dataKey.startsWith("pending-differences")) {
            return inventoryDifferenceMapper.getPendingDifferences();
        } else if (dataKey.startsWith("statistics")) {
            return inventoryDifferenceMapper.getStatistics();
        }
        // 可以根据需要添加更多的预加载逻辑
        return null;
    }
    
    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }
}