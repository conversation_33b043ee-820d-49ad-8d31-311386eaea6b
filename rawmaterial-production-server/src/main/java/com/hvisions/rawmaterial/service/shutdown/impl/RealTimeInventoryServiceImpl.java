package com.hvisions.rawmaterial.service.shutdown.impl;

import com.hvisions.rawmaterial.dao.shutdown.SiloInventoryMapper;
import com.hvisions.rawmaterial.entity.shutdown.TMpdSiloInventory;
import com.hvisions.rawmaterial.service.shutdown.RealTimeInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实时库存服务实现类
 * 集成中控系统获取实时库存数据
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class RealTimeInventoryServiceImpl implements RealTimeInventoryService {
    
    @Autowired
    private SiloInventoryMapper siloInventoryMapper;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 中控系统API配置
    private static final String CENTRAL_CONTROL_BASE_URL = "http://central-control-system/api";
    private static final String INVENTORY_ENDPOINT = "/inventory/silo/{siloCode}";
    private static final String BATCH_INVENTORY_ENDPOINT = "/inventory/batch";
    private static final String HEALTH_CHECK_ENDPOINT = "/health";
    
    @Override
    @Cacheable(value = "siloInventory", key = "#siloCode", unless = "#result == null")
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public BigDecimal getRealTimeInventoryBySiloCode(String siloCode) {
        log.info("获取筒仓实时库存，筒仓编码: {}", siloCode);
        
        try {
            // 首先尝试从中控系统获取实时数据
            String url = CENTRAL_CONTROL_BASE_URL + INVENTORY_ENDPOINT;
            Map<String, Object> response = restTemplate.getForObject(url, Map.class, siloCode);
            
            if (response != null && response.containsKey("stockQuantity")) {
                BigDecimal stockQuantity = new BigDecimal(response.get("stockQuantity").toString());
                
                // 更新本地缓存数据
                updateLocalInventoryCache(siloCode, stockQuantity);
                
                log.info("成功获取筒仓 {} 实时库存: {}", siloCode, stockQuantity);
                return stockQuantity;
            }
        } catch (Exception e) {
            log.warn("从中控系统获取筒仓 {} 实时库存失败，尝试从本地缓存获取: {}", siloCode, e.getMessage());
        }
        
        // 如果中控系统不可用，从本地数据库获取最新数据
        TMpdSiloInventory inventory = siloInventoryMapper.selectBySiloCode(siloCode);
        if (inventory != null) {
            log.info("从本地缓存获取筒仓 {} 库存: {}", siloCode, inventory.getStockQuantity());
            return inventory.getStockQuantity();
        }
        
        log.warn("无法获取筒仓 {} 的库存数据", siloCode);
        return BigDecimal.ZERO;
    }
    
    @Override
    @Cacheable(value = "batchSiloInventory", key = "#siloCodes.hashCode()", unless = "#result.isEmpty()")
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Map<String, BigDecimal> getBatchRealTimeInventory(List<String> siloCodes) {
        log.info("批量获取筒仓实时库存，筒仓数量: {}", siloCodes.size());
        
        Map<String, BigDecimal> result = new HashMap<>();
        
        try {
            // 尝试从中控系统批量获取
            String url = CENTRAL_CONTROL_BASE_URL + BATCH_INVENTORY_ENDPOINT;
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("siloCodes", siloCodes);
            
            Map<String, Object> response = restTemplate.postForObject(url, requestBody, Map.class);
            
            if (response != null && response.containsKey("inventories")) {
                Map<String, Object> inventories = (Map<String, Object>) response.get("inventories");
                
                for (String siloCode : siloCodes) {
                    if (inventories.containsKey(siloCode)) {
                        BigDecimal stockQuantity = new BigDecimal(inventories.get(siloCode).toString());
                        result.put(siloCode, stockQuantity);
                        
                        // 更新本地缓存
                        updateLocalInventoryCache(siloCode, stockQuantity);
                    }
                }
                
                log.info("成功批量获取 {} 个筒仓的实时库存", result.size());
                return result;
            }
        } catch (Exception e) {
            log.warn("从中控系统批量获取库存失败，尝试从本地缓存获取: {}", e.getMessage());
        }
        
        // 如果中控系统不可用，从本地数据库获取
        List<TMpdSiloInventory> inventories = siloInventoryMapper.selectBySiloCodes(siloCodes);
        result = inventories.stream()
                .collect(Collectors.toMap(
                        TMpdSiloInventory::getSiloCode,
                        TMpdSiloInventory::getStockQuantity,
                        (existing, replacement) -> replacement
                ));
        
        log.info("从本地缓存获取 {} 个筒仓的库存数据", result.size());
        return result;
    }
    
    @Override
    @Cacheable(value = "materialTypeInventory", key = "#materialType", unless = "#result.isEmpty()")
    public List<TMpdSiloInventory> getRealTimeInventoryByMaterialType(String materialType) {
        log.info("根据物料类型获取实时库存，物料类型: {}", materialType);
        
        // 首先从数据库获取该物料类型的所有筒仓
        List<TMpdSiloInventory> inventories = siloInventoryMapper.selectByMaterialType(materialType);
        
        if (inventories.isEmpty()) {
            log.warn("未找到物料类型 {} 对应的筒仓", materialType);
            return inventories;
        }
        
        // 获取筒仓编码列表
        List<String> siloCodes = inventories.stream()
                .map(TMpdSiloInventory::getSiloCode)
                .collect(Collectors.toList());
        
        // 批量获取实时库存
        Map<String, BigDecimal> realTimeInventories = getBatchRealTimeInventory(siloCodes);
        
        // 更新库存数据
        inventories.forEach(inventory -> {
            String siloCode = inventory.getSiloCode();
            if (realTimeInventories.containsKey(siloCode)) {
                inventory.setStockQuantity(realTimeInventories.get(siloCode));
                inventory.setUpdateTime(LocalDateTime.now());
            }
        });
        
        log.info("成功获取物料类型 {} 的 {} 个筒仓库存数据", materialType, inventories.size());
        return inventories;
    }
    
    @Override
    @CacheEvict(value = "siloInventory", key = "#siloCode")
    public void refreshInventoryCache(String siloCode) {
        log.info("刷新筒仓库存缓存，筒仓编码: {}", siloCode);
        
        try {
            // 强制从中控系统获取最新数据
            BigDecimal latestInventory = getRealTimeInventoryBySiloCode(siloCode);
            log.info("成功刷新筒仓 {} 库存缓存: {}", siloCode, latestInventory);
        } catch (Exception e) {
            log.error("刷新筒仓 {} 库存缓存失败: {}", siloCode, e.getMessage(), e);
        }
    }
    
    @Override
    @CacheEvict(value = {"siloInventory", "batchSiloInventory", "materialTypeInventory"}, allEntries = true)
    public void refreshAllInventoryCache() {
        log.info("刷新所有筒仓库存缓存");
        
        try {
            // 获取所有筒仓编码
            List<String> allSiloCodes = siloInventoryMapper.selectAllSiloCodes();
            
            // 批量刷新
            getBatchRealTimeInventory(allSiloCodes);
            
            log.info("成功刷新所有 {} 个筒仓的库存缓存", allSiloCodes.size());
        } catch (Exception e) {
            log.error("刷新所有库存缓存失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 500))
    public boolean checkCentralControlConnection() {
        try {
            String url = CENTRAL_CONTROL_BASE_URL + HEALTH_CHECK_ENDPOINT;
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            
            boolean isHealthy = response != null && "UP".equals(response.get("status"));
            log.info("中控系统连接状态检查: {}", isHealthy ? "正常" : "异常");
            
            return isHealthy;
        } catch (Exception e) {
            log.warn("中控系统连接检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 更新本地库存缓存
     * 
     * @param siloCode 筒仓编码
     * @param stockQuantity 库存量
     */
    private void updateLocalInventoryCache(String siloCode, BigDecimal stockQuantity) {
        try {
            TMpdSiloInventory inventory = siloInventoryMapper.selectBySiloCode(siloCode);
            if (inventory != null) {
                inventory.setStockQuantity(stockQuantity);
                inventory.setUpdateTime(LocalDateTime.now());
                inventory.setDataSource("中控系统");
                siloInventoryMapper.updateById(inventory);
            } else {
                log.warn("未找到筒仓 {} 的本地记录，无法更新缓存", siloCode);
            }
        } catch (Exception e) {
            log.error("更新筒仓 {} 本地缓存失败: {}", siloCode, e.getMessage(), e);
        }
    }
}