package com.hvisions.rawmaterial.service.async;

import com.hvisions.rawmaterial.configuration.InventoryDifferenceRabbitConfig;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage;
import com.hvisions.rawmaterial.dto.async.TaskStatusMessage;
import com.hvisions.rawmaterial.service.SapIntegrationService;
import com.hvisions.rawmaterial.service.async.impl.AsyncTaskServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SAP同步消息处理器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class SapSyncMessageProcessor {
    
    @Autowired
    private SapIntegrationService sapIntegrationService;
    
    @Autowired
    private AsyncTaskServiceImpl asyncTaskService;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    /**
     * 处理SAP同步消息
     */
    @RabbitListener(queues = InventoryDifferenceRabbitConfig.SAP_SYNC_QUEUE)
    public void processSapSyncMessage(SapSyncTaskMessage message) {
        log.info("开始处理SAP同步任务: {}", message.getTaskId());
        
        // 更新任务状态为执行中
        TaskStatusMessage status = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.RUNNING
        );
        status.setStatusDescription("正在同步SAP库存数据...");
        asyncTaskService.updateTaskStatus(status);
        
        try {
            // 执行SAP同步
            SapStockSyncDTO syncResult = performSapSync(message);
            
            // 更新任务状态为成功
            status.setStatus(TaskStatusMessage.TaskStatus.SUCCESS);
            status.setStatusDescription("SAP库存同步完成");
            status.setResult(syncResult);
            status.setProgress(100);
            status.setSuccessCount(syncResult.getSuccessCount());
            status.setFailureCount(syncResult.getFailureCount());
            status.setTotalCount(syncResult.getTotalCount());
            
            asyncTaskService.updateTaskStatus(status);
            
            log.info("SAP同步任务完成: {}, 成功: {}, 失败: {}", 
                message.getTaskId(), syncResult.getSuccessCount(), syncResult.getFailureCount());
                
        } catch (Exception e) {
            log.error("SAP同步任务执行失败: {}", message.getTaskId(), e);
            
            // 检查是否可以重试
            if (message.canRetry()) {
                message.incrementRetryCount();
                
                // 更新状态为重试中
                status.setStatus(TaskStatusMessage.TaskStatus.RUNNING);
                status.setStatusDescription(String.format("执行失败，正在重试 (%d/%d)", 
                    message.getRetryCount(), message.getMaxRetryCount()));
                status.setErrorMessage(e.getMessage());
                asyncTaskService.updateTaskStatus(status);
                
                // 延迟重试
                try {
                    Thread.sleep(5000 * message.getRetryCount()); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
                
                // 重新发送消息
                rabbitTemplate.convertAndSend(
                    InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
                    InventoryDifferenceRabbitConfig.SAP_SYNC_ROUTING_KEY,
                    message
                );
                
                log.info("SAP同步任务重试: {}, 重试次数: {}", message.getTaskId(), message.getRetryCount());
                
            } else {
                // 重试次数用完，标记为失败
                status.setStatus(TaskStatusMessage.TaskStatus.FAILED);
                status.setStatusDescription("SAP同步失败，已达到最大重试次数");
                status.setErrorMessage(e.getMessage());
                asyncTaskService.updateTaskStatus(status);
            }
        }
    }
    
    /**
     * 处理SAP同步死信消息
     */
    @RabbitListener(queues = InventoryDifferenceRabbitConfig.SAP_SYNC_DLQ)
    public void processSapSyncDeadLetter(SapSyncTaskMessage message) {
        log.error("SAP同步任务进入死信队列: {}", message.getTaskId());
        
        // 更新任务状态为失败
        TaskStatusMessage status = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.FAILED
        );
        status.setStatusDescription("任务处理超时或异常，已进入死信队列");
        status.setErrorMessage("消息处理超时或发生严重异常");
        
        asyncTaskService.updateTaskStatus(status);
    }
    
    /**
     * 执行SAP同步
     */
    private SapStockSyncDTO performSapSync(SapSyncTaskMessage message) {
        SapStockSyncDTO result = new SapStockSyncDTO();
        result.setSyncTime(new java.util.Date());
        result.setSyncType(message.getSyncType());
        
        try {
            if ("FULL".equals(message.getSyncType())) {
                // 全量同步
                result = sapIntegrationService.syncAllStock();
            } else if ("PARTIAL".equals(message.getSyncType()) && 
                       message.getMaterialCodes() != null && !message.getMaterialCodes().isEmpty()) {
                // 部分同步
                result = sapIntegrationService.batchSyncStock(message.getMaterialCodes());
            } else {
                throw new IllegalArgumentException("无效的同步类型或参数");
            }
            
            result.setSuccess(true);
            result.setMessage("同步完成");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("同步失败: " + e.getMessage());
            result.setFailureCount(result.getTotalCount());
            result.setSuccessCount(0);
            throw e;
        }
        
        return result;
    }
}