package com.hvisions.rawmaterial.service.cache.impl;

import com.hvisions.rawmaterial.service.cache.CacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class CacheServiceImpl implements CacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheServiceImpl.class);
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            logger.debug("Cache set successfully: key={}, timeout={} {}", key, timeout, unit);
        } catch (Exception e) {
            logger.error("Failed to set cache: key={}", key, e);
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null && clazz.isInstance(value)) {
                logger.debug("Cache hit: key={}", key);
                return (T) value;
            }
            logger.debug("Cache miss: key={}", key);
            return null;
        } catch (Exception e) {
            logger.error("Failed to get cache: key={}", key, e);
            return null;
        }
    }
    
    @Override
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
            logger.debug("Cache deleted: key={}", key);
        } catch (Exception e) {
            logger.error("Failed to delete cache: key={}", key, e);
        }
    }
    
    @Override
    public void deleteAll(List<String> keys) {
        try {
            redisTemplate.delete(keys);
            logger.debug("Batch cache deleted: count={}", keys.size());
        } catch (Exception e) {
            logger.error("Failed to batch delete cache: keys={}", keys, e);
        }
    }
    
    @Override
    public boolean exists(String key) {
        try {
            Boolean exists = redisTemplate.hasKey(key);
            return exists != null && exists;
        } catch (Exception e) {
            logger.error("Failed to check cache existence: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public void expire(String key, long timeout, TimeUnit unit) {
        try {
            redisTemplate.expire(key, timeout, unit);
            logger.debug("Cache expiration set: key={}, timeout={} {}", key, timeout, unit);
        } catch (Exception e) {
            logger.error("Failed to set cache expiration: key={}", key, e);
        }
    }
    
    @Override
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            logger.error("Failed to get cache expiration: key={}", key, e);
            return -1;
        }
    }
    
    @Override
    public void deleteByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                logger.debug("Pattern cache deleted: pattern={}, count={}", pattern, keys.size());
            }
        } catch (Exception e) {
            logger.error("Failed to delete cache by pattern: pattern={}", pattern, e);
        }
    }
    
    @Override
    public long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            logger.debug("Cache incremented: key={}, delta={}, result={}", key, delta, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("Failed to increment cache: key={}, delta={}", key, delta, e);
            return 0;
        }
    }
    
    @Override
    public long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            logger.debug("Cache decremented: key={}, delta={}, result={}", key, delta, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("Failed to decrement cache: key={}, delta={}", key, delta, e);
            return 0;
        }
    }
}