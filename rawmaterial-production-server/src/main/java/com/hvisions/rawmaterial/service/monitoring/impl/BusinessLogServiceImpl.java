package com.hvisions.rawmaterial.service.monitoring.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.InventoryDifferenceProcessDTO;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.entity.log.TMpdBusinessLog;
import com.hvisions.rawmaterial.dao.log.BusinessLogRepository;
import com.hvisions.rawmaterial.service.monitoring.BusinessLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 业务日志服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class BusinessLogServiceImpl implements BusinessLogService {
    
    @Autowired
    private BusinessLogRepository businessLogRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public void logDifferenceProcessing(InventoryDifferenceProcessDTO processDTO, String result, Integer userId, String userName) {
        try {
            TMpdBusinessLog businessLog = new TMpdBusinessLog();
            businessLog.setOperationType("DIFFERENCE_PROCESSING");
            businessLog.setOperationDescription("库存差异处理");
            businessLog.setOperationParams(objectMapper.writeValueAsString(processDTO));
            businessLog.setOperationResult(result);
            businessLog.setUserId(userId);
            businessLog.setUserName(userName);
            businessLog.setOperationTime(LocalDateTime.now());
            businessLog.setIpAddress(getCurrentIpAddress());
            businessLog.setUserAgent(getCurrentUserAgent());
            
            businessLogRepository.save(businessLog);
            
            log.info("记录差异处理操作日志: 用户[{}] 处理差异记录[{}], 结果: {}", 
                userName, processDTO.getId(), result);
                
        } catch (JsonProcessingException e) {
            log.error("记录差异处理日志失败", e);
        }
    }
    
    @Override
    public void logSapStockSync(SapStockSyncDTO syncResult, Integer userId, String userName) {
        try {
            TMpdBusinessLog businessLog = new TMpdBusinessLog();
            businessLog.setOperationType("SAP_STOCK_SYNC");
            businessLog.setOperationDescription("SAP库存同步");
            businessLog.setOperationParams("{}");
            businessLog.setOperationResult(objectMapper.writeValueAsString(syncResult));
            businessLog.setUserId(userId);
            businessLog.setUserName(userName);
            businessLog.setOperationTime(LocalDateTime.now());
            businessLog.setIpAddress(getCurrentIpAddress());
            businessLog.setUserAgent(getCurrentUserAgent());
            
            businessLogRepository.save(businessLog);
            
            log.info("记录SAP库存同步日志: 用户[{}] 同步状态[{}], 成功数量[{}], 失败数量[{}]", 
                userName, syncResult.getSyncStatus(), syncResult.getSuccessCount(), syncResult.getFailureCount());
                
        } catch (JsonProcessingException e) {
            log.error("记录SAP同步日志失败", e);
        }
    }
    
    @Override
    public void logDifferenceRecordGeneration(int recordCount, Integer userId, String userName) {
        TMpdBusinessLog businessLog = new TMpdBusinessLog();
        businessLog.setOperationType("DIFFERENCE_RECORD_GENERATION");
        businessLog.setOperationDescription("差异记录生成");
        businessLog.setOperationParams("{}");
        businessLog.setOperationResult("生成记录数量: " + recordCount);
        businessLog.setUserId(userId);
        businessLog.setUserName(userName);
        businessLog.setOperationTime(LocalDateTime.now());
        businessLog.setIpAddress(getCurrentIpAddress());
        businessLog.setUserAgent(getCurrentUserAgent());
        
        businessLogRepository.save(businessLog);
        
        log.info("记录差异记录生成日志: 用户[{}] 生成记录数量[{}]", userName, recordCount);
    }
    
    @Override
    public void logQueryOperation(String queryType, String queryParams, int resultCount, Integer userId, String userName) {
        TMpdBusinessLog businessLog = new TMpdBusinessLog();
        businessLog.setOperationType("QUERY_OPERATION");
        businessLog.setOperationDescription("查询操作: " + queryType);
        businessLog.setOperationParams(queryParams);
        businessLog.setOperationResult("查询结果数量: " + resultCount);
        businessLog.setUserId(userId);
        businessLog.setUserName(userName);
        businessLog.setOperationTime(LocalDateTime.now());
        businessLog.setIpAddress(getCurrentIpAddress());
        businessLog.setUserAgent(getCurrentUserAgent());
        
        businessLogRepository.save(businessLog);
        
        log.debug("记录查询操作日志: 用户[{}] 查询类型[{}], 结果数量[{}]", userName, queryType, resultCount);
    }
    
    @Override
    public void logErrorOperation(String operation, String errorMessage, Exception exception, Integer userId, String userName) {
        TMpdBusinessLog businessLog = new TMpdBusinessLog();
        businessLog.setOperationType("ERROR_OPERATION");
        businessLog.setOperationDescription("操作异常: " + operation);
        businessLog.setOperationParams("{}");
        businessLog.setOperationResult("错误信息: " + errorMessage);
        businessLog.setUserId(userId);
        businessLog.setUserName(userName);
        businessLog.setOperationTime(LocalDateTime.now());
        businessLog.setIpAddress(getCurrentIpAddress());
        businessLog.setUserAgent(getCurrentUserAgent());
        businessLog.setErrorMessage(errorMessage);
        businessLog.setStackTrace(getStackTrace(exception));
        
        businessLogRepository.save(businessLog);
        
        log.error("记录错误操作日志: 用户[{}] 操作[{}], 错误: {}", userName, operation, errorMessage, exception);
    }
    
    private String getCurrentIpAddress() {
        // TODO: 从请求上下文获取IP地址
        return "127.0.0.1";
    }
    
    private String getCurrentUserAgent() {
        // TODO: 从请求上下文获取User-Agent
        return "Unknown";
    }
    
    private String getStackTrace(Exception exception) {
        if (exception == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(exception.getClass().getName()).append(": ").append(exception.getMessage()).append("\n");
        
        for (StackTraceElement element : exception.getStackTrace()) {
            sb.append("\tat ").append(element.toString()).append("\n");
            if (sb.length() > 4000) { // 限制长度
                sb.append("...(truncated)");
                break;
            }
        }
        
        return sb.toString();
    }
}