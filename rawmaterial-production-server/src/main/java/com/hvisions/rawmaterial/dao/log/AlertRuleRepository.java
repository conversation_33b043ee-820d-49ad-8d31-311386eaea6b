package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdAlertRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 告警规则数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface AlertRuleRepository extends JpaRepository<TMpdAlertRule, Integer> {
    
    /**
     * 根据规则类型和启用状态查询规则
     * 
     * @param ruleType 规则类型
     * @param enabled 是否启用
     * @return 规则列表
     */
    List<TMpdAlertRule> findByRuleTypeAndEnabled(String ruleType, Boolean enabled);
    
    /**
     * 根据规则名称查询规则
     * 
     * @param ruleName 规则名称
     * @return 规则
     */
    TMpdAlertRule findByRuleName(String ruleName);
    
    /**
     * 根据指标名称查询规则
     * 
     * @param metricName 指标名称
     * @return 规则列表
     */
    List<TMpdAlertRule> findByMetricName(String metricName);
    
    /**
     * 根据启用状态查询规则
     * 
     * @param enabled 是否启用
     * @return 规则列表
     */
    List<TMpdAlertRule> findByEnabled(Boolean enabled);
}