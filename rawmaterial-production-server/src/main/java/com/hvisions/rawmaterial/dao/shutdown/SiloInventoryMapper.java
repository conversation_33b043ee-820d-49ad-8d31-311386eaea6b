package com.hvisions.rawmaterial.dao.shutdown;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.shutdown.TMpdSiloInventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 筒仓库存数据访问接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface SiloInventoryMapper extends BaseMapper<TMpdSiloInventory> {

    /**
     * 根据物料类型查询筒仓库存
     *
     * @param materialType 物料类型
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByMaterialType(@Param("materialType") String materialType);

    /**
     * 根据筒仓编码查询库存
     *
     * @param siloCode 筒仓编码
     * @return 筒仓库存
     */
    TMpdSiloInventory selectBySiloCode(@Param("siloCode") String siloCode);

    /**
     * 根据筒仓类型查询库存列表
     *
     * @param siloType 筒仓类型
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectBySiloType(@Param("siloType") String siloType);

    /**
     * 根据车间编码查询筒仓库存
     *
     * @param workshopCode 车间编码
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByWorkshopCode(@Param("workshopCode") String workshopCode);

    /**
     * 根据物料类型和筒仓类型查询库存
     *
     * @param materialType 物料类型
     * @param siloType 筒仓类型
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByMaterialAndSiloType(
            @Param("materialType") String materialType,
            @Param("siloType") String siloType
    );

    /**
     * 根据物料类型和车间编码查询库存
     *
     * @param materialType 物料类型
     * @param workshopCode 车间编码
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByMaterialTypeAndWorkshop(
            @Param("materialType") String materialType,
            @Param("workshopCode") String workshopCode
    );

    /**
     * 查询高粱物料的筒仓库存（按层级排序）
     *
     * @return 高粱筒仓库存列表
     */
    List<TMpdSiloInventory> selectSorghumSiloInventoryByHierarchy();

    /**
     * 查询稻壳物料的筒仓库存（按层级排序）
     *
     * @return 稻壳筒仓库存列表
     */
    List<TMpdSiloInventory> selectRiceHuskSiloInventoryByHierarchy();

    /**
     * 根据筒仓编码列表查询库存
     *
     * @param siloCodes 筒仓编码列表
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectBySiloCodes(@Param("siloCodes") List<String> siloCodes);

    /**
     * 查询最新更新时间的库存记录
     *
     * @param siloCode 筒仓编码
     * @return 最新库存记录
     */
    TMpdSiloInventory selectLatestBySiloCode(@Param("siloCode") String siloCode);

    /**
     * 批量更新库存量
     *
     * @param inventoryList 库存列表
     * @return 更新数量
     */
    int batchUpdateStockQuantity(@Param("inventoryList") List<TMpdSiloInventory> inventoryList);

    /**
     * 根据数据来源查询库存
     *
     * @param dataSource 数据来源
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByDataSource(@Param("dataSource") String dataSource);

    /**
     * 查询指定时间范围内更新的库存记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByUpdateTimeRange(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 根据启用状态查询库存
     *
     * @param enabled 是否启用
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectByEnabled(@Param("enabled") Boolean enabled);

    /**
     * 查询所有启用的筒仓库存（按排序序号排序）
     *
     * @return 筒仓库存列表
     */
    List<TMpdSiloInventory> selectAllEnabledOrderBySortOrder();
}