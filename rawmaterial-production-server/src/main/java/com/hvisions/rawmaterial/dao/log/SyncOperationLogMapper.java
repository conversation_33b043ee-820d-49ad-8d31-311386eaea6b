package com.hvisions.rawmaterial.dao.log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.log.TMpdSyncOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 同步操作日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Mapper
public interface SyncOperationLogMapper extends BaseMapper<TMpdSyncOperationLog> {

    /**
     * 根据条件查询操作日志
     *
     * @param moduleName 模块名称
     * @param businessType 业务类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志列表
     */
    List<TMpdSyncOperationLog> selectByCondition(
        @Param("moduleName") String moduleName,
        @Param("businessType") Integer businessType,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime);

    /**
     * 根据任务号查询操作日志
     *
     * @param taskNo 任务号
     * @return 日志列表
     */
    List<TMpdSyncOperationLog> selectByTaskNo(@Param("taskNo") String taskNo);

    /**
     * 根据工单号查询操作日志
     *
     * @param orderNo 工单号
     * @return 日志列表
     */
    List<TMpdSyncOperationLog> selectByOrderNo(@Param("orderNo") String orderNo);
}
