package com.hvisions.rawmaterial.dao.shutdown;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.shutdown.TMpdMaterialDispenseRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物料发放记录数据访问接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface MaterialDispenseRecordMapper extends BaseMapper<TMpdMaterialDispenseRecord> {

    /**
     * 根据发放单号查询记录
     *
     * @param dispenseNo 发放单号
     * @return 发放记录
     */
    TMpdMaterialDispenseRecord selectByDispenseNo(@Param("dispenseNo") String dispenseNo);

    /**
     * 根据车间编码查询发放记录
     *
     * @param workshopCode 车间编码
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByWorkshopCode(@Param("workshopCode") String workshopCode);

    /**
     * 根据筒仓编码查询发放记录
     *
     * @param siloCode 筒仓编码
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectBySiloCode(@Param("siloCode") String siloCode);

    /**
     * 根据物料类型查询发放记录
     *
     * @param materialType 物料类型
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByMaterialType(@Param("materialType") String materialType);

    /**
     * 根据发放时间范围查询记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByDispenseTimeRange(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 根据车间和时间范围查询发放记录
     *
     * @param workshopCode 车间编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByWorkshopAndTimeRange(
            @Param("workshopCode") String workshopCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 根据筒仓和时间范围查询发放记录
     *
     * @param siloCode 筒仓编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectBySiloAndTimeRange(
            @Param("siloCode") String siloCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 统计车间在指定时间范围内的发放量
     *
     * @param workshopCode 车间编码
     * @param materialType 物料类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放量汇总
     */
    BigDecimal sumDispenseQuantityByWorkshopAndTime(
            @Param("workshopCode") String workshopCode,
            @Param("materialType") String materialType,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 统计筒仓在指定时间范围内的发放量
     *
     * @param siloCode 筒仓编码
     * @param materialType 物料类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放量汇总
     */
    BigDecimal sumDispenseQuantityBySiloAndTime(
            @Param("siloCode") String siloCode,
            @Param("materialType") String materialType,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 根据物料类型和时间范围统计发放量
     *
     * @param materialType 物料类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放量汇总
     */
    BigDecimal sumDispenseQuantityByMaterialAndTime(
            @Param("materialType") String materialType,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 查询高粱物料的中心碎料斗发放记录
     *
     * @param workshopCode 车间编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectSorghumCenterCrushedDispenseRecords(
            @Param("workshopCode") String workshopCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 查询稻壳物料的中心缓存仓发放记录
     *
     * @param workshopCode 车间编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectRiceHuskCenterCacheDispenseRecords(
            @Param("workshopCode") String workshopCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 根据业务类型查询发放记录
     *
     * @param businessType 业务类型
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByBusinessType(@Param("businessType") String businessType);

    /**
     * 根据发放状态查询记录
     *
     * @param dispenseStatus 发放状态
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByDispenseStatus(@Param("dispenseStatus") Integer dispenseStatus);

    /**
     * 批量插入发放记录
     *
     * @param recordList 发放记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("recordList") List<TMpdMaterialDispenseRecord> recordList);

    /**
     * 根据关联单据号查询发放记录
     *
     * @param relatedDocNo 关联单据号
     * @return 发放记录列表
     */
    List<TMpdMaterialDispenseRecord> selectByRelatedDocNo(@Param("relatedDocNo") String relatedDocNo);

    /**
     * 查询指定筒仓类型的发放记录汇总
     *
     * @param siloType 筒仓类型
     * @param materialType 物料类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发放量汇总
     */
    BigDecimal sumDispenseQuantityBySiloTypeAndTime(
            @Param("siloType") String siloType,
            @Param("materialType") String materialType,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );
}