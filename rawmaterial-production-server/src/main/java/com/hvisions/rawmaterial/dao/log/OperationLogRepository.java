package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdOperationLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 操作日志数据访问接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Repository
public interface OperationLogRepository extends JpaRepository<TMpdOperationLog, Integer> {
    
    /**
     * 根据用户ID查询操作日志
     */
    List<TMpdOperationLog> findByUserIdOrderByOperationTimeDesc(Integer userId);
    
    /**
     * 根据模块查询操作日志
     */
    List<TMpdOperationLog> findByModuleOrderByOperationTimeDesc(String module);
    
    /**
     * 根据时间范围查询操作日志
     */
    List<TMpdOperationLog> findByOperationTimeBetweenOrderByOperationTimeDesc(Date startTime, Date endTime);
    
    /**
     * 根据用户和时间范围查询操作日志
     */
    List<TMpdOperationLog> findByUserIdAndOperationTimeBetweenOrderByOperationTimeDesc(
        Integer userId, Date startTime, Date endTime);
    
    /**
     * 根据模块和时间范围查询操作日志
     */
    List<TMpdOperationLog> findByModuleAndOperationTimeBetweenOrderByOperationTimeDesc(
        String module, Date startTime, Date endTime);
    
    /**
     * 统计用户操作次数
     */
    @Query("SELECT COUNT(o) FROM TMpdOperationLog o WHERE o.userId = :userId AND o.operationTime >= :startTime")
    Long countByUserIdAndOperationTimeAfter(@Param("userId") Integer userId, @Param("startTime") Date startTime);
    
    /**
     * 统计模块操作次数
     */
    @Query("SELECT COUNT(o) FROM TMpdOperationLog o WHERE o.module = :module AND o.operationTime >= :startTime")
    Long countByModuleAndOperationTimeAfter(@Param("module") String module, @Param("startTime") Date startTime);
    
    /**
     * 统计用户成功操作次数
     */
    @Query("SELECT COUNT(o) FROM TMpdOperationLog o WHERE o.userId = :userId AND o.status = 1 AND o.operationTime >= :startTime")
    Long countSuccessOperationsByUserIdAndOperationTimeAfter(@Param("userId") Integer userId, @Param("startTime") Date startTime);
    
    /**
     * 统计用户失败操作次数
     */
    @Query("SELECT COUNT(o) FROM TMpdOperationLog o WHERE o.userId = :userId AND o.status = 0 AND o.operationTime >= :startTime")
    Long countFailedOperationsByUserIdAndOperationTimeAfter(@Param("userId") Integer userId, @Param("startTime") Date startTime);
    
    /**
     * 删除过期日志
     */
    @Query("DELETE FROM TMpdOperationLog o WHERE o.operationTime < :expireTime")
    int deleteByOperationTimeBefore(@Param("expireTime") Date expireTime);
    
    /**
     * 获取用户操作类型统计
     */
    @Query("SELECT o.operationType, COUNT(o) FROM TMpdOperationLog o WHERE o.userId = :userId AND o.operationTime >= :startTime GROUP BY o.operationType")
    List<Object[]> getOperationTypeStatisticsByUserId(@Param("userId") Integer userId, @Param("startTime") Date startTime);
    
    /**
     * 获取模块操作类型统计
     */
    @Query("SELECT o.operationType, COUNT(o) FROM TMpdOperationLog o WHERE o.module = :module AND o.operationTime >= :startTime GROUP BY o.operationType")
    List<Object[]> getOperationTypeStatisticsByModule(@Param("module") String module, @Param("startTime") Date startTime);
}