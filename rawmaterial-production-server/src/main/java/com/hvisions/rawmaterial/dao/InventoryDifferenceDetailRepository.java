package com.hvisions.rawmaterial.dao;

import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理明细仓库接口
 * @Date: 2024/07/14
 */
@Repository
public interface InventoryDifferenceDetailRepository extends JpaRepository<TMpdInventoryDifferenceDetail, Integer>, JpaSpecificationExecutor<TMpdInventoryDifferenceDetail> {

    /**
     * 根据差异处理ID查询明细列表
     * @param differenceId 差异处理ID
     * @return 明细列表
     */
    List<TMpdInventoryDifferenceDetail> findByDifferenceId(Integer differenceId);

    /**
     * 根据差异处理ID删除明细
     * @param differenceId 差异处理ID
     */
    void deleteByDifferenceId(Integer differenceId);

    /**
     * 根据物料编码查询明细列表
     * @param materialCode 物料编码
     * @return 明细列表
     */
    List<TMpdInventoryDifferenceDetail> findByMaterialCode(String materialCode);

    /**
     * 根据处理类型查询明细列表
     * @param processType 处理类型
     * @return 明细列表
     */
    List<TMpdInventoryDifferenceDetail> findByProcessType(Integer processType);
}
