package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdDataAccessLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 数据访问日志数据访问接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Repository
public interface DataAccessLogRepository extends JpaRepository<TMpdDataAccessLog, Integer> {
    
    /**
     * 根据用户ID查询数据访问日志
     */
    List<TMpdDataAccessLog> findByUserIdOrderByAccessTimeDesc(Integer userId);
    
    /**
     * 根据表名查询数据访问日志
     */
    List<TMpdDataAccessLog> findByTableNameOrderByAccessTimeDesc(String tableName);
    
    /**
     * 根据数据ID查询数据访问日志
     */
    List<TMpdDataAccessLog> findByDataIdOrderByAccessTimeDesc(String dataId);
    
    /**
     * 根据时间范围查询数据访问日志
     */
    List<TMpdDataAccessLog> findByAccessTimeBetweenOrderByAccessTimeDesc(Date startTime, Date endTime);
    
    /**
     * 根据用户和时间范围查询数据访问日志
     */
    List<TMpdDataAccessLog> findByUserIdAndAccessTimeBetweenOrderByAccessTimeDesc(
        Integer userId, Date startTime, Date endTime);
    
    /**
     * 根据表名和时间范围查询数据访问日志
     */
    List<TMpdDataAccessLog> findByTableNameAndAccessTimeBetweenOrderByAccessTimeDesc(
        String tableName, Date startTime, Date endTime);
    
    /**
     * 统计用户数据访问次数
     */
    @Query("SELECT COUNT(d) FROM TMpdDataAccessLog d WHERE d.userId = :userId AND d.accessTime >= :startTime")
    Long countByUserIdAndAccessTimeAfter(@Param("userId") Integer userId, @Param("startTime") Date startTime);
    
    /**
     * 统计表数据访问次数
     */
    @Query("SELECT COUNT(d) FROM TMpdDataAccessLog d WHERE d.tableName = :tableName AND d.accessTime >= :startTime")
    Long countByTableNameAndAccessTimeAfter(@Param("tableName") String tableName, @Param("startTime") Date startTime);
    
    /**
     * 删除过期日志
     */
    @Query("DELETE FROM TMpdDataAccessLog d WHERE d.accessTime < :expireTime")
    int deleteByAccessTimeBefore(@Param("expireTime") Date expireTime);
    
    /**
     * 获取用户数据访问统计
     */
    @Query("SELECT d.operationType, COUNT(d) FROM TMpdDataAccessLog d WHERE d.userId = :userId AND d.accessTime >= :startTime GROUP BY d.operationType")
    List<Object[]> getAccessTypeStatisticsByUserId(@Param("userId") Integer userId, @Param("startTime") Date startTime);
    
    /**
     * 获取表数据访问统计
     */
    @Query("SELECT d.operationType, COUNT(d) FROM TMpdDataAccessLog d WHERE d.tableName = :tableName AND d.accessTime >= :startTime GROUP BY d.operationType")
    List<Object[]> getAccessTypeStatisticsByTableName(@Param("tableName") String tableName, @Param("startTime") Date startTime);
}