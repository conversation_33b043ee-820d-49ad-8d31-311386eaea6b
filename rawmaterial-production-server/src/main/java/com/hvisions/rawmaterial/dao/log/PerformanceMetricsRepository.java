package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdPerformanceMetrics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 性能指标数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface PerformanceMetricsRepository extends JpaRepository<TMpdPerformanceMetrics, Integer> {
    
    /**
     * 根据指标类型和名称查询指标
     * 
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标列表
     */
    List<TMpdPerformanceMetrics> findByMetricTypeAndMetricNameAndRecordTimeBetween(
        String metricType, String metricName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据指标类型查询指标
     * 
     * @param metricType 指标类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指标列表
     */
    List<TMpdPerformanceMetrics> findByMetricTypeAndRecordTimeBetween(
        String metricType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查询慢查询
     * 
     * @param metricType 指标类型
     * @param threshold 阈值(毫秒)
     * @param limit 限制数量
     * @return 慢查询列表
     */
    @Query(value = "SELECT * FROM t_mpd_performance_metrics WHERE metric_type = :metricType " +
           "AND execution_time > :threshold ORDER BY execution_time DESC LIMIT :limit", 
           nativeQuery = true)
    List<TMpdPerformanceMetrics> findSlowQueries(@Param("metricType") String metricType,
                                                @Param("threshold") long threshold,
                                                @Param("limit") int limit);
    
    /**
     * 统计指定时间范围内的平均执行时间
     * 
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均执行时间
     */
    @Query("SELECT AVG(p.executionTime) FROM TMpdPerformanceMetrics p WHERE p.metricType = :metricType " +
           "AND p.metricName = :metricName AND p.recordTime BETWEEN :startTime AND :endTime")
    Double getAverageExecutionTime(@Param("metricType") String metricType,
                                  @Param("metricName") String metricName,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的最大执行时间
     * 
     * @param metricType 指标类型
     * @param metricName 指标名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 最大执行时间
     */
    @Query("SELECT MAX(p.executionTime) FROM TMpdPerformanceMetrics p WHERE p.metricType = :metricType " +
           "AND p.metricName = :metricName AND p.recordTime BETWEEN :startTime AND :endTime")
    Long getMaxExecutionTime(@Param("metricType") String metricType,
                            @Param("metricName") String metricName,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的指标数据
     * 
     * @param beforeTime 时间点
     */
    void deleteByRecordTimeBefore(LocalDateTime beforeTime);
}