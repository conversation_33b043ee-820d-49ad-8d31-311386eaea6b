package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdBusinessLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务日志数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface BusinessLogRepository extends JpaRepository<TMpdBusinessLog, Integer> {
    
    /**
     * 根据操作类型查询日志
     * 
     * @param operationType 操作类型
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<TMpdBusinessLog> findByOperationTypeOrderByOperationTimeDesc(String operationType, Pageable pageable);
    
    /**
     * 根据用户ID查询日志
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<TMpdBusinessLog> findByUserIdOrderByOperationTimeDesc(Integer userId, Pageable pageable);
    
    /**
     * 根据时间范围查询日志
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<TMpdBusinessLog> findByOperationTimeBetweenOrderByOperationTimeDesc(
        LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 查询错误日志
     * 
     * @param pageable 分页参数
     * @return 错误日志分页结果
     */
    Page<TMpdBusinessLog> findByOperationTypeOrderByOperationTimeDesc(Pageable pageable);
    
    /**
     * 统计指定时间范围内的操作数量
     * 
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作数量
     */
    @Query("SELECT COUNT(b) FROM TMpdBusinessLog b WHERE b.operationType = :operationType " +
           "AND b.operationTime BETWEEN :startTime AND :endTime")
    Long countByOperationTypeAndTimeRange(@Param("operationType") String operationType,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询最近的错误日志
     * 
     * @param limit 限制数量
     * @return 错误日志列表
     */
    @Query("SELECT b FROM TMpdBusinessLog b WHERE b.operationType = 'ERROR_OPERATION' " +
           "ORDER BY b.operationTime DESC")
    List<TMpdBusinessLog> findRecentErrorLogs(Pageable pageable);
    
    /**
     * 删除指定时间之前的日志
     * 
     * @param beforeTime 时间点
     */
    void deleteByOperationTimeBefore(LocalDateTime beforeTime);
}