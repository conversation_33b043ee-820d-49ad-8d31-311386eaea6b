package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdSapInterfaceCallLog;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SAP接口调用日志数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface SapInterfaceCallLogRepository extends JpaRepository<TMpdSapInterfaceCallLog, Integer> {
    
    /**
     * 根据接口名称和时间范围查询调用日志
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用日志列表
     */
    List<TMpdSapInterfaceCallLog> findByInterfaceNameAndStartTimeBetween(
        String interfaceName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据接口名称和时间范围查询调用日志(分页)
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 调用日志列表
     */
    List<TMpdSapInterfaceCallLog> findByInterfaceNameAndStartTimeBetweenOrderByStartTimeDesc(
        String interfaceName, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据调用状态和时间范围查询调用日志
     * 
     * @param callStatus 调用状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用日志列表
     */
    List<TMpdSapInterfaceCallLog> findByCallStatusAndStartTimeBetweenOrderByStartTimeDesc(
        String callStatus, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据时间范围查询调用日志
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用日志列表
     */
    List<TMpdSapInterfaceCallLog> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定接口在时间范围内的调用次数
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用次数
     */
    @Query("SELECT COUNT(s) FROM TMpdSapInterfaceCallLog s WHERE s.interfaceName = :interfaceName " +
           "AND s.startTime BETWEEN :startTime AND :endTime")
    Long countByInterfaceNameAndTimeRange(@Param("interfaceName") String interfaceName,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定接口在时间范围内的成功调用次数
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功调用次数
     */
    @Query("SELECT COUNT(s) FROM TMpdSapInterfaceCallLog s WHERE s.interfaceName = :interfaceName " +
           "AND s.callStatus = 'SUCCESS' AND s.startTime BETWEEN :startTime AND :endTime")
    Long countSuccessCallsByInterfaceNameAndTimeRange(@Param("interfaceName") String interfaceName,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取指定接口在时间范围内的平均响应时间
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均响应时间
     */
    @Query("SELECT AVG(s.responseTime) FROM TMpdSapInterfaceCallLog s WHERE s.interfaceName = :interfaceName " +
           "AND s.responseTime IS NOT NULL AND s.startTime BETWEEN :startTime AND :endTime")
    Double getAverageResponseTimeByInterfaceNameAndTimeRange(@Param("interfaceName") String interfaceName,
                                                           @Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的日志
     * 
     * @param beforeTime 时间点
     */
    void deleteByStartTimeBefore(LocalDateTime beforeTime);
}