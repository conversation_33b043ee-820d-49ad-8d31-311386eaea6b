package com.hvisions.rawmaterial.dao.log;

import com.hvisions.rawmaterial.entity.log.TMpdAlertNotification;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警通知数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public interface AlertNotificationRepository extends JpaRepository<TMpdAlertNotification, Integer> {
    
    /**
     * 查询所有告警通知(按时间倒序)
     * 
     * @param pageable 分页参数
     * @return 告警通知列表
     */
    List<TMpdAlertNotification> findAllByOrderByAlertTimeDesc(Pageable pageable);
    
    /**
     * 根据告警类型查询通知
     * 
     * @param alertType 告警类型
     * @param pageable 分页参数
     * @return 告警通知列表
     */
    List<TMpdAlertNotification> findByAlertTypeOrderByAlertTimeDesc(String alertType, Pageable pageable);
    
    /**
     * 根据告警级别查询通知
     * 
     * @param alertLevel 告警级别
     * @param pageable 分页参数
     * @return 告警通知列表
     */
    List<TMpdAlertNotification> findByAlertLevelOrderByAlertTimeDesc(String alertLevel, Pageable pageable);
    
    /**
     * 根据告警状态查询通知
     * 
     * @param alertStatus 告警状态
     * @param pageable 分页参数
     * @return 告警通知列表
     */
    List<TMpdAlertNotification> findByAlertStatusOrderByAlertTimeDesc(String alertStatus, Pageable pageable);
    
    /**
     * 根据时间范围查询通知
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 告警通知列表
     */
    List<TMpdAlertNotification> findByAlertTimeBetweenOrderByAlertTimeDesc(
        LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 统计未确认的告警数量
     * 
     * @return 未确认告警数量
     */
    Long countByAlertStatus(String alertStatus);
    
    /**
     * 删除指定时间之前的告警通知
     * 
     * @param beforeTime 时间点
     */
    void deleteByAlertTimeBefore(LocalDateTime beforeTime);
}