package com.hvisions.rawmaterial.dto.shutdown;

import com.hvisions.rawmaterial.dto.validation.ShutdownClearanceValidation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 停产物料清仓DTO验证测试
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
class ShutdownClearanceDTOValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testSiloClearanceQueryDTO_ValidData() {
        // 准备有效数据
        SiloClearanceQueryDTO dto = new SiloClearanceQueryDTO();
        dto.setMaterialType("高粱");
        dto.setStatisticsTime(new Date());
        dto.setShutdownStartTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)); // 1天前
        dto.setWorkshopNames(Arrays.asList("一车间", "二车间"));
        dto.setSiloTypes(Arrays.asList("中心碎料斗", "中心碎料仓"));
        dto.setIncludeZeroStock(false);
        dto.setRealTimeRefresh(true);

        // 验证
        Set<ConstraintViolation<SiloClearanceQueryDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "有效数据不应该有验证错误");
    }

    @Test
    void testSiloClearanceQueryDTO_InvalidMaterialType() {
        SiloClearanceQueryDTO dto = new SiloClearanceQueryDTO();
        dto.setMaterialType("无效物料");
        dto.setStatisticsTime(new Date());

        Set<ConstraintViolation<SiloClearanceQueryDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "无效物料类型应该有验证错误");
        
        boolean hasInvalidMaterialTypeError = violations.stream()
                .anyMatch(v -> v.getMessage().contains("物料类型"));
        assertTrue(hasInvalidMaterialTypeError, "应该包含物料类型验证错误");
    }

    @Test
    void testSiloClearanceQueryDTO_EmptyMaterialType() {
        SiloClearanceQueryDTO dto = new SiloClearanceQueryDTO();
        dto.setMaterialType("");
        dto.setStatisticsTime(new Date());

        Set<ConstraintViolation<SiloClearanceQueryDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "空物料类型应该有验证错误");
    }

    @Test
    void testSiloClearanceQueryDTO_NullStatisticsTime() {
        SiloClearanceQueryDTO dto = new SiloClearanceQueryDTO();
        dto.setMaterialType("高粱");
        dto.setStatisticsTime(null);

        Set<ConstraintViolation<SiloClearanceQueryDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "空统计时间应该有验证错误");
    }

    @Test
    void testSiloClearanceQueryDTO_InvalidTimeRange() {
        SiloClearanceQueryDTO dto = new SiloClearanceQueryDTO();
        dto.setMaterialType("高粱");
        dto.setStatisticsTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)); // 1天前
        dto.setShutdownStartTime(new Date()); // 现在

        Set<ConstraintViolation<SiloClearanceQueryDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "无效时间范围应该有验证错误");
    }

    @Test
    void testSiloClearanceExportDTO_ValidData() {
        SiloClearanceExportDTO dto = new SiloClearanceExportDTO();
        dto.setExportType("1");
        dto.setExportFormat("excel");
        dto.setFileName("测试文件");
        dto.setMaterialType("稻壳");
        dto.setStatisticsTime(new Date());
        dto.setIncludeSummary(true);

        Set<ConstraintViolation<SiloClearanceExportDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "有效数据不应该有验证错误");
    }

    @Test
    void testSiloClearanceExportDTO_InvalidExportType() {
        SiloClearanceExportDTO dto = new SiloClearanceExportDTO();
        dto.setExportType("4"); // 无效类型
        dto.setMaterialType("高粱");

        Set<ConstraintViolation<SiloClearanceExportDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "无效导出类型应该有验证错误");
    }

    @Test
    void testSiloClearanceExportDTO_InvalidExportFormat() {
        SiloClearanceExportDTO dto = new SiloClearanceExportDTO();
        dto.setExportFormat("pdf"); // 无效格式
        dto.setMaterialType("高粱");

        Set<ConstraintViolation<SiloClearanceExportDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "无效导出格式应该有验证错误");
    }

    @Test
    void testSiloRemainingDispenseDTO_ValidData() {
        SiloRemainingDispenseDTO dto = new SiloRemainingDispenseDTO();
        dto.setMaterialType("高粱");
        dto.setStatisticsTime(new Date());
        dto.setShutdownStartTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));

        // 创建有效的车间需求DTO
        SiloRemainingDispenseDTO.WorkshopDemandDTO workshopDto = new SiloRemainingDispenseDTO.WorkshopDemandDTO();
        workshopDto.setWorkshopName("一车间");
        workshopDto.setDemandQuantity(new BigDecimal("1000.000"));
        workshopDto.setDispensedQuantity(new BigDecimal("800.000"));
        workshopDto.setStockQuantity(new BigDecimal("200.000"));
        workshopDto.setRemainingDispenseQuantity(new BigDecimal("200.000"));
        workshopDto.setDispenseProgress(new BigDecimal("0.8000"));
        workshopDto.setUnit("吨");
        dto.setWorkshopDemands(Arrays.asList(workshopDto));

        // 创建有效的筒仓信息DTO
        SiloRemainingDispenseDTO.SiloInfoDTO siloDto = new SiloRemainingDispenseDTO.SiloInfoDTO();
        siloDto.setSiloType("中心碎料斗");
        siloDto.setSiloCode("SILO001");
        siloDto.setSiloName("中心碎料斗1号仓");
        siloDto.setStockQuantity(new BigDecimal("500.000"));
        siloDto.setRemainingDispenseQuantity(new BigDecimal("300.000"));
        siloDto.setCalculationDescription("需求量-已发放量");
        siloDto.setUnit("吨");
        siloDto.setSortOrder(1);
        siloDto.setEnabled(true);
        dto.setSiloInfos(Arrays.asList(siloDto));

        Set<ConstraintViolation<SiloRemainingDispenseDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "有效数据不应该有验证错误");
    }

    @Test
    void testWorkshopDemandDTO_InvalidData() {
        SiloRemainingDispenseDTO.WorkshopDemandDTO dto = new SiloRemainingDispenseDTO.WorkshopDemandDTO();
        dto.setWorkshopName(""); // 空车间名称
        dto.setDemandQuantity(new BigDecimal("-100")); // 负数需求量
        dto.setDispenseProgress(new BigDecimal("1.5")); // 超过1的进度
        dto.setUnit(""); // 空单位

        Set<ConstraintViolation<SiloRemainingDispenseDTO.WorkshopDemandDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "无效数据应该有验证错误");
        
        // 检查具体的验证错误
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("车间名称")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("需求量")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("发放进度")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("单位")));
    }

    @Test
    void testSiloInfoDTO_InvalidData() {
        SiloRemainingDispenseDTO.SiloInfoDTO dto = new SiloRemainingDispenseDTO.SiloInfoDTO();
        dto.setSiloType(""); // 空筒仓类型
        dto.setSiloCode(""); // 空筒仓编码
        dto.setSiloName(""); // 空筒仓名称
        dto.setStockQuantity(new BigDecimal("-50")); // 负库存
        dto.setUnit(""); // 空单位
        dto.setSortOrder(0); // 无效排序序号
        dto.setEnabled(null); // 空启用状态

        Set<ConstraintViolation<SiloRemainingDispenseDTO.SiloInfoDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "无效数据应该有验证错误");
        
        // 检查具体的验证错误
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("筒仓类型")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("筒仓编码")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("筒仓名称")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("库存量")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("单位")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("排序序号")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("是否启用")));
    }

    @Test
    void testShutdownClearanceValidation_MaterialType() {
        assertTrue(ShutdownClearanceValidation.isValidMaterialType("高粱"));
        assertTrue(ShutdownClearanceValidation.isValidMaterialType("稻壳"));
        assertFalse(ShutdownClearanceValidation.isValidMaterialType("无效物料"));
        assertFalse(ShutdownClearanceValidation.isValidMaterialType(""));
        assertFalse(ShutdownClearanceValidation.isValidMaterialType(null));
    }

    @Test
    void testShutdownClearanceValidation_SiloType() {
        // 高粱物料的筒仓类型
        assertTrue(ShutdownClearanceValidation.isValidSiloType("中心碎料斗", "高粱"));
        assertTrue(ShutdownClearanceValidation.isValidSiloType("中心碎料仓", "高粱"));
        assertFalse(ShutdownClearanceValidation.isValidSiloType("熟稻壳缓存仓", "高粱"));

        // 稻壳物料的筒仓类型
        assertTrue(ShutdownClearanceValidation.isValidSiloType("熟稻壳缓存仓", "稻壳"));
        assertTrue(ShutdownClearanceValidation.isValidSiloType("中心缓存仓", "稻壳"));
        assertFalse(ShutdownClearanceValidation.isValidSiloType("中心碎料斗", "稻壳"));
    }

    @Test
    void testShutdownClearanceValidation_ExportType() {
        assertTrue(ShutdownClearanceValidation.isValidExportType("1"));
        assertTrue(ShutdownClearanceValidation.isValidExportType("2"));
        assertTrue(ShutdownClearanceValidation.isValidExportType("3"));
        assertFalse(ShutdownClearanceValidation.isValidExportType("4"));
        assertFalse(ShutdownClearanceValidation.isValidExportType(""));
    }

    @Test
    void testShutdownClearanceValidation_ExportFormat() {
        assertTrue(ShutdownClearanceValidation.isValidExportFormat("excel"));
        assertTrue(ShutdownClearanceValidation.isValidExportFormat("csv"));
        assertFalse(ShutdownClearanceValidation.isValidExportFormat("pdf"));
        assertFalse(ShutdownClearanceValidation.isValidExportFormat(""));
    }

    @Test
    void testShutdownClearanceValidation_TimeRange() {
        Date now = new Date();
        Date yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        Date tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

        // 有效时间范围
        assertTrue(ShutdownClearanceValidation.isValidTimeRange(yesterday, now, 365));
        
        // 无效时间范围（开始时间晚于结束时间）
        assertFalse(ShutdownClearanceValidation.isValidTimeRange(now, yesterday, 365));
        
        // 无效时间范围（结束时间是未来时间）
        assertFalse(ShutdownClearanceValidation.isValidTimeRange(yesterday, tomorrow, 365));
    }

    @Test
    void testShutdownClearanceValidation_FileName() {
        assertTrue(ShutdownClearanceValidation.isValidFileName("正常文件名"));
        assertTrue(ShutdownClearanceValidation.isValidFileName("normal_filename"));
        assertTrue(ShutdownClearanceValidation.isValidFileName("")); // 空文件名允许
        assertTrue(ShutdownClearanceValidation.isValidFileName(null)); // null允许
        
        // 包含非法字符
        assertFalse(ShutdownClearanceValidation.isValidFileName("file\\name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file/name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file:name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file*name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file?name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file\"name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file<name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file>name"));
        assertFalse(ShutdownClearanceValidation.isValidFileName("file|name"));
        
        // 文件名过长
        String longFileName = "a".repeat(101);
        assertFalse(ShutdownClearanceValidation.isValidFileName(longFileName));
    }
}