package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dao.log.SapInterfaceCallLogRepository;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceCallLog;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceMetrics;
import com.hvisions.rawmaterial.entity.log.TMpdSapInterfaceCallLog;
import com.hvisions.rawmaterial.service.monitoring.impl.SapInterfaceMonitorServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * SAP接口监控服务测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class SapInterfaceMonitorServiceTest {
    
    @Mock
    private SapInterfaceCallLogRepository sapInterfaceCallLogRepository;
    
    @InjectMocks
    private SapInterfaceMonitorServiceImpl sapInterfaceMonitorService;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testRecordInterfaceCallStart() {
        // 准备测试数据
        String interfaceName = "getSapStock";
        String requestParams = "{\"materialCode\":\"M001\"}";
        
        // 执行测试
        String callId = sapInterfaceMonitorService.recordInterfaceCallStart(interfaceName, requestParams);
        
        // 验证结果
        assertNotNull(callId);
        assertFalse(callId.isEmpty());
    }
    
    @Test
    void testRecordInterfaceCallEnd() {
        // 准备测试数据
        String interfaceName = "getSapStock";
        String requestParams = "{\"materialCode\":\"M001\"}";
        String callId = sapInterfaceMonitorService.recordInterfaceCallStart(interfaceName, requestParams);
        
        boolean success = true;
        String responseData = "{\"stock\":100}";
        String errorMessage = null;
        
        when(sapInterfaceCallLogRepository.save(any(TMpdSapInterfaceCallLog.class)))
            .thenReturn(new TMpdSapInterfaceCallLog());
        
        // 执行测试
        sapInterfaceMonitorService.recordInterfaceCallEnd(callId, success, responseData, errorMessage);
        
        // 验证结果
        verify(sapInterfaceCallLogRepository, times(1)).save(any(TMpdSapInterfaceCallLog.class));
    }
    
    @Test
    void testGetInterfaceMetrics() {
        // 准备测试数据
        String interfaceName = "getSapStock";
        LocalDateTime startTime = LocalDateTime.now().minusHours(1);
        LocalDateTime endTime = LocalDateTime.now();
        
        TMpdSapInterfaceCallLog log1 = new TMpdSapInterfaceCallLog();
        log1.setCallStatus("SUCCESS");
        log1.setResponseTime(1000L);
        
        TMpdSapInterfaceCallLog log2 = new TMpdSapInterfaceCallLog();
        log2.setCallStatus("SUCCESS");
        log2.setResponseTime(1500L);
        
        TMpdSapInterfaceCallLog log3 = new TMpdSapInterfaceCallLog();
        log3.setCallStatus("FAILED");
        log3.setResponseTime(3000L);
        
        List<TMpdSapInterfaceCallLog> logs = Arrays.asList(log1, log2, log3);
        
        when(sapInterfaceCallLogRepository.findByInterfaceNameAndStartTimeBetween(
            interfaceName, startTime, endTime)).thenReturn(logs);
        
        // 执行测试
        SapInterfaceMetrics metrics = sapInterfaceMonitorService.getInterfaceMetrics(interfaceName, startTime, endTime);
        
        // 验证结果
        assertNotNull(metrics);
        assertEquals(interfaceName, metrics.getInterfaceName());
        assertEquals(3, metrics.getTotalCalls());
        assertEquals(2, metrics.getSuccessCalls());
        assertEquals(1, metrics.getFailedCalls());
        assertEquals(66.67, metrics.getSuccessRate(), 0.01);
        assertEquals(1833.33, metrics.getAverageResponseTime(), 0.01);
        assertEquals(3000L, metrics.getMaxResponseTime());
    }
    
    @Test
    void testGetFailedInterfaceCalls() {
        // 准备测试数据
        LocalDateTime startTime = LocalDateTime.now().minusHours(1);
        LocalDateTime endTime = LocalDateTime.now();
        
        TMpdSapInterfaceCallLog failedLog = new TMpdSapInterfaceCallLog();
        failedLog.setCallId("call-001");
        failedLog.setInterfaceName("getSapStock");
        failedLog.setCallStatus("FAILED");
        failedLog.setErrorMessage("Connection timeout");
        
        List<TMpdSapInterfaceCallLog> failedLogs = Arrays.asList(failedLog);
        
        when(sapInterfaceCallLogRepository.findByCallStatusAndStartTimeBetweenOrderByStartTimeDesc(
            "FAILED", startTime, endTime)).thenReturn(failedLogs);
        
        // 执行测试
        List<SapInterfaceCallLog> result = sapInterfaceMonitorService.getFailedInterfaceCalls(startTime, endTime);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("call-001", result.get(0).getCallId());
        assertEquals("FAILED", result.get(0).getCallStatus());
        assertEquals("Connection timeout", result.get(0).getErrorMessage());
    }
    
    @Test
    void testCheckSapInterfaceHealth() {
        // 准备测试数据 - 健康状态
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        LocalDateTime now = LocalDateTime.now();
        
        TMpdSapInterfaceCallLog log1 = new TMpdSapInterfaceCallLog();
        log1.setCallStatus("SUCCESS");
        
        TMpdSapInterfaceCallLog log2 = new TMpdSapInterfaceCallLog();
        log2.setCallStatus("SUCCESS");
        
        TMpdSapInterfaceCallLog log3 = new TMpdSapInterfaceCallLog();
        log3.setCallStatus("SUCCESS");
        
        TMpdSapInterfaceCallLog log4 = new TMpdSapInterfaceCallLog();
        log4.setCallStatus("SUCCESS");
        
        TMpdSapInterfaceCallLog log5 = new TMpdSapInterfaceCallLog();
        log5.setCallStatus("FAILED");
        
        List<TMpdSapInterfaceCallLog> logs = Arrays.asList(log1, log2, log3, log4, log5);
        
        when(sapInterfaceCallLogRepository.findByStartTimeBetween(any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(logs);
        
        // 执行测试
        boolean isHealthy = sapInterfaceMonitorService.checkSapInterfaceHealth();
        
        // 验证结果 - 成功率80%，应该是健康的
        assertTrue(isHealthy);
    }
    
    @Test
    void testCheckSapInterfaceHealthUnhealthy() {
        // 准备测试数据 - 不健康状态
        TMpdSapInterfaceCallLog log1 = new TMpdSapInterfaceCallLog();
        log1.setCallStatus("SUCCESS");
        
        TMpdSapInterfaceCallLog log2 = new TMpdSapInterfaceCallLog();
        log2.setCallStatus("FAILED");
        
        TMpdSapInterfaceCallLog log3 = new TMpdSapInterfaceCallLog();
        log3.setCallStatus("FAILED");
        
        TMpdSapInterfaceCallLog log4 = new TMpdSapInterfaceCallLog();
        log4.setCallStatus("FAILED");
        
        TMpdSapInterfaceCallLog log5 = new TMpdSapInterfaceCallLog();
        log5.setCallStatus("FAILED");
        
        List<TMpdSapInterfaceCallLog> logs = Arrays.asList(log1, log2, log3, log4, log5);
        
        when(sapInterfaceCallLogRepository.findByStartTimeBetween(any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(logs);
        
        // 执行测试
        boolean isHealthy = sapInterfaceMonitorService.checkSapInterfaceHealth();
        
        // 验证结果 - 成功率20%，应该是不健康的
        assertFalse(isHealthy);
    }
    
    @Test
    void testGetResponseTimeStatistics() {
        // 准备测试数据
        String interfaceName = "getSapStock";
        int hours = 2;
        
        TMpdSapInterfaceCallLog log1 = new TMpdSapInterfaceCallLog();
        log1.setResponseTime(1000L);
        
        TMpdSapInterfaceCallLog log2 = new TMpdSapInterfaceCallLog();
        log2.setResponseTime(1500L);
        
        TMpdSapInterfaceCallLog log3 = new TMpdSapInterfaceCallLog();
        log3.setResponseTime(2000L);
        
        List<TMpdSapInterfaceCallLog> logs = Arrays.asList(log1, log2, log3);
        
        when(sapInterfaceCallLogRepository.findByInterfaceNameAndStartTimeBetween(
            eq(interfaceName), any(LocalDateTime.class), any(LocalDateTime.class))).thenReturn(logs);
        
        // 执行测试
        List<Double> responseTimeStats = sapInterfaceMonitorService.getResponseTimeStatistics(interfaceName, hours);
        
        // 验证结果
        assertNotNull(responseTimeStats);
        assertEquals(3, responseTimeStats.size());
        assertEquals(1000.0, responseTimeStats.get(0));
        assertEquals(1500.0, responseTimeStats.get(1));
        assertEquals(2000.0, responseTimeStats.get(2));
    }
}