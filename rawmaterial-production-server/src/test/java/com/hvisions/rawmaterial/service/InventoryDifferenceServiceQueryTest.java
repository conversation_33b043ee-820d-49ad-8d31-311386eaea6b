package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.consts.InventoryDifferenceConstants;
import com.hvisions.rawmaterial.dao.InventoryDifferenceDetailRepository;
import com.hvisions.rawmaterial.dao.InventoryDifferenceRepository;
import com.hvisions.rawmaterial.dto.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceDetailDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceQueryDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifference;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail;
import com.hvisions.rawmaterial.mapper.InventoryDifferenceMapper;
import com.hvisions.rawmaterial.service.impl.InventoryDifferenceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 库存差异处理服务查询功能测试类
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class InventoryDifferenceServiceQueryTest {

    @Mock
    private InventoryDifferenceRepository inventoryDifferenceRepository;

    @Mock
    private InventoryDifferenceDetailRepository inventoryDifferenceDetailRepository;

    @Mock
    private InventoryDifferenceMapper inventoryDifferenceMapper;

    @InjectMocks
    private InventoryDifferenceServiceImpl inventoryDifferenceService;

    private SimpleDateFormat dateFormat;

    @BeforeEach
    void setUp() {
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    @Test
    void testQueryInventoryDifferencesWithMaterialCodeFilter() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("RM001");
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithMaterialCode("RM001");
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        result.getContent().forEach(dto -> 
            assertTrue(dto.getMaterialCode().contains("RM001"))
        );
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithMaterialNameFilter() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialName("原料");
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithMaterialName("原料");
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getContent().size());
        result.getContent().forEach(dto -> 
            assertTrue(dto.getMaterialName().contains("原料"))
        );
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithWarehouseFilter() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setErpWarehouseCode("WH001");
        queryDTO.setErpWarehouseName("主仓库");
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithWarehouse("WH001", "主仓库");
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        result.getContent().forEach(dto -> {
            assertEquals("WH001", dto.getErpWarehouseCode());
            assertEquals("主仓库", dto.getErpWarehouseName());
        });
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithStatusFilter() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithStatus(InventoryDifferenceConstants.STATUS_PENDING);
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getContent().size());
        result.getContent().forEach(dto -> 
            assertEquals(InventoryDifferenceConstants.STATUS_PENDING, dto.getStatus())
        );
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithDateRangeFilter() throws Exception {
        // Given
        Date startDate = dateFormat.parse("2025-07-01 00:00:00");
        Date endDate = dateFormat.parse("2025-07-31 23:59:59");
        
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setStatisticsStartDateBegin(startDate);
        queryDTO.setStatisticsStartDateEnd(endDate);
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithDateRange(startDate, endDate);
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        result.getContent().forEach(dto -> {
            assertNotNull(dto.getStatisticsStartDate());
            assertTrue(dto.getStatisticsStartDate().compareTo(startDate) >= 0);
            assertTrue(dto.getStatisticsStartDate().compareTo(endDate) <= 0);
        });
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithProcessorFilter() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setProcessorName("张三");
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithProcessor("张三");
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        result.getContent().forEach(dto -> 
            assertEquals("张三", dto.getProcessorName())
        );
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithMultipleFilters() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("RM");
        queryDTO.setDepartment("原辅料管理部");
        queryDTO.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockDataWithMultipleFilters();
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        
        InventoryDifferenceDTO dto = result.getContent().get(0);
        assertTrue(dto.getMaterialCode().contains("RM"));
        assertEquals("原辅料管理部", dto.getDepartment());
        assertEquals(InventoryDifferenceConstants.STATUS_PROCESSED, dto.getStatus());
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testGetInventoryDifferenceById() {
        // Given
        Integer id = 1;
        InventoryDifferenceDTO mockDTO = createMockDTOWithId(id);
        List<TMpdInventoryDifferenceDetail> mockDetails = createMockDetails(id);
        
        when(inventoryDifferenceMapper.getInventoryDifferenceById(id)).thenReturn(mockDTO);
        when(inventoryDifferenceDetailRepository.findByDifferenceId(id)).thenReturn(mockDetails);

        // When
        InventoryDifferenceDTO result = inventoryDifferenceService.getInventoryDifferenceById(id);

        // Then
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("RM001", result.getMaterialCode());
        assertEquals("原料A", result.getMaterialName());
        
        // 验证明细列表
        assertNotNull(result.getDetailList());
        assertEquals(2, result.getDetailList().size());
        
        verify(inventoryDifferenceMapper).getInventoryDifferenceById(id);
        verify(inventoryDifferenceDetailRepository).findByDifferenceId(id);
    }

    @Test
    void testGetInventoryDifferenceByIdNotFound() {
        // Given
        Integer id = 999;
        when(inventoryDifferenceMapper.getInventoryDifferenceById(id)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> 
            inventoryDifferenceService.getInventoryDifferenceById(id)
        );
        
        assertEquals("库存差异处理记录不存在", exception.getMessage());
        verify(inventoryDifferenceMapper).getInventoryDifferenceById(id);
    }

    @Test
    void testQueryInventoryDifferencesWithPagination() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPage(2);
        queryDTO.setPageSize(5);
        
        List<InventoryDifferenceDTO> mockData = createMockPaginatedData();
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(5, result.getContent().size());
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesEmptyResult() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("NONEXISTENT");
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(new ArrayList<>());

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testQueryInventoryDifferencesWithSorting() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockSortedData();
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO)).thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getContent().size());
        
        // 验证按创建时间降序排列
        List<InventoryDifferenceDTO> content = result.getContent();
        assertTrue(content.get(0).getCreateTime().after(content.get(1).getCreateTime()));
        assertTrue(content.get(1).getCreateTime().after(content.get(2).getCreateTime()));
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    // Helper methods to create mock data
    private List<InventoryDifferenceDTO> createMockDataWithMaterialCode(String materialCode) {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto1 = new InventoryDifferenceDTO();
        dto1.setId(1);
        dto1.setMaterialCode(materialCode);
        dto1.setMaterialName("原料A");
        dto1.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto1);
        
        InventoryDifferenceDTO dto2 = new InventoryDifferenceDTO();
        dto2.setId(2);
        dto2.setMaterialCode(materialCode + "B");
        dto2.setMaterialName("原料B");
        dto2.setDifferenceQuantity(new BigDecimal("50.000"));
        data.add(dto2);
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockDataWithMaterialName(String materialName) {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
            dto.setId(i);
            dto.setMaterialCode("RM00" + i);
            dto.setMaterialName(materialName + i);
            dto.setDifferenceQuantity(new BigDecimal(i * 50 + ".000"));
            data.add(dto);
        }
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockDataWithWarehouse(String warehouseCode, String warehouseName) {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto1 = new InventoryDifferenceDTO();
        dto1.setId(1);
        dto1.setMaterialCode("RM001");
        dto1.setMaterialName("原料A");
        dto1.setErpWarehouseCode(warehouseCode);
        dto1.setErpWarehouseName(warehouseName);
        dto1.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto1);
        
        InventoryDifferenceDTO dto2 = new InventoryDifferenceDTO();
        dto2.setId(2);
        dto2.setMaterialCode("RM002");
        dto2.setMaterialName("原料B");
        dto2.setErpWarehouseCode(warehouseCode);
        dto2.setErpWarehouseName(warehouseName);
        dto2.setDifferenceQuantity(new BigDecimal("50.000"));
        data.add(dto2);
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockDataWithStatus(Integer status) {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
            dto.setId(i);
            dto.setMaterialCode("RM00" + i);
            dto.setMaterialName("原料" + i);
            dto.setStatus(status);
            dto.setStatusDesc(status == 1 ? "待处理" : "已处理");
            dto.setDifferenceQuantity(new BigDecimal(i * 30 + ".000"));
            data.add(dto);
        }
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockDataWithDateRange(Date startDate, Date endDate) {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto1 = new InventoryDifferenceDTO();
        dto1.setId(1);
        dto1.setMaterialCode("RM001");
        dto1.setMaterialName("原料A");
        dto1.setStatisticsStartDate(new Date(startDate.getTime() + 86400000)); // 1 day after start
        dto1.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto1);
        
        InventoryDifferenceDTO dto2 = new InventoryDifferenceDTO();
        dto2.setId(2);
        dto2.setMaterialCode("RM002");
        dto2.setMaterialName("原料B");
        dto2.setStatisticsStartDate(new Date(endDate.getTime() - 86400000)); // 1 day before end
        dto2.setDifferenceQuantity(new BigDecimal("50.000"));
        data.add(dto2);
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockDataWithProcessor(String processorName) {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto1 = new InventoryDifferenceDTO();
        dto1.setId(1);
        dto1.setMaterialCode("RM001");
        dto1.setMaterialName("原料A");
        dto1.setProcessorName(processorName);
        dto1.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        dto1.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto1);
        
        InventoryDifferenceDTO dto2 = new InventoryDifferenceDTO();
        dto2.setId(2);
        dto2.setMaterialCode("RM002");
        dto2.setMaterialName("原料B");
        dto2.setProcessorName(processorName);
        dto2.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        dto2.setDifferenceQuantity(new BigDecimal("50.000"));
        data.add(dto2);
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockDataWithMultipleFilters() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
        dto.setId(1);
        dto.setMaterialCode("RM001");
        dto.setMaterialName("原料A");
        dto.setDepartment("原辅料管理部");
        dto.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        dto.setProcessorName("张三");
        dto.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto);
        
        return data;
    }
    
    private InventoryDifferenceDTO createMockDTOWithId(Integer id) {
        InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
        dto.setId(id);
        dto.setMaterialCode("RM001");
        dto.setMaterialName("原料A");
        dto.setErpWarehouseCode("WH001");
        dto.setErpWarehouseName("主仓库");
        dto.setDepartment("原辅料管理部");
        dto.setMesCurrentStock(new BigDecimal("1000.000"));
        dto.setMesInitialStock(new BigDecimal("900.000"));
        dto.setWeighbridgeReceiptQuantity(new BigDecimal("200.000"));
        dto.setSolidWasteQuantity(new BigDecimal("50.000"));
        dto.setIssuedQuantity(new BigDecimal("150.000"));
        dto.setUnit("吨");
        dto.setSapStock(new BigDecimal("950.000"));
        dto.setDifferenceQuantity(new BigDecimal("50.000"));
        dto.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        dto.setStatusDesc("待处理");
        dto.setCreateTime(new Date());
        return dto;
    }
    
    private List<TMpdInventoryDifferenceDetail> createMockDetails(Integer differenceId) {
        List<TMpdInventoryDifferenceDetail> details = new ArrayList<>();
        
        TMpdInventoryDifferenceDetail detail1 = new TMpdInventoryDifferenceDetail();
        detail1.setId(1);
        detail1.setDifferenceId(differenceId);
        detail1.setMaterialCode("RM001");
        detail1.setMaterialName("原料A");
        detail1.setUnit("吨");
        detail1.setDifferenceQuantity(new BigDecimal("30.000"));
        detail1.setProcessQuantity(new BigDecimal("30.000"));
        detail1.setProcessType(InventoryDifferenceConstants.PROCESS_TYPE_SURPLUS);
        detail1.setProcessRemark("盘盈处理");
        detail1.setProcessTime(new Date());
        details.add(detail1);
        
        TMpdInventoryDifferenceDetail detail2 = new TMpdInventoryDifferenceDetail();
        detail2.setId(2);
        detail2.setDifferenceId(differenceId);
        detail2.setMaterialCode("RM001");
        detail2.setMaterialName("原料A");
        detail2.setUnit("吨");
        detail2.setDifferenceQuantity(new BigDecimal("20.000"));
        detail2.setProcessQuantity(new BigDecimal("20.000"));
        detail2.setProcessType(InventoryDifferenceConstants.PROCESS_TYPE_SURPLUS);
        detail2.setProcessRemark("盘盈处理");
        detail2.setProcessTime(new Date());
        details.add(detail2);
        
        return details;
    }
    
    private List<InventoryDifferenceDTO> createMockPaginatedData() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        for (int i = 6; i <= 10; i++) { // 第二页数据
            InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
            dto.setId(i);
            dto.setMaterialCode("RM00" + i);
            dto.setMaterialName("原料" + i);
            dto.setDifferenceQuantity(new BigDecimal(i * 10 + ".000"));
            data.add(dto);
        }
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockSortedData() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        // 创建按时间降序排列的数据
        long currentTime = System.currentTimeMillis();
        
        InventoryDifferenceDTO dto1 = new InventoryDifferenceDTO();
        dto1.setId(1);
        dto1.setMaterialCode("RM001");
        dto1.setMaterialName("原料A");
        dto1.setCreateTime(new Date(currentTime)); // 最新
        dto1.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto1);
        
        InventoryDifferenceDTO dto2 = new InventoryDifferenceDTO();
        dto2.setId(2);
        dto2.setMaterialCode("RM002");
        dto2.setMaterialName("原料B");
        dto2.setCreateTime(new Date(currentTime - 86400000)); // 1天前
        dto2.setDifferenceQuantity(new BigDecimal("50.000"));
        data.add(dto2);
        
        InventoryDifferenceDTO dto3 = new InventoryDifferenceDTO();
        dto3.setId(3);
        dto3.setMaterialCode("RM003");
        dto3.setMaterialName("原料C");
        dto3.setCreateTime(new Date(currentTime - 172800000)); // 2天前
        dto3.setDifferenceQuantity(new BigDecimal("30.000"));
        data.add(dto3);
        
        return data;
    }
}