package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dao.log.BusinessLogRepository;
import com.hvisions.rawmaterial.dto.InventoryDifferenceProcessDTO;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.entity.log.TMpdBusinessLog;
import com.hvisions.rawmaterial.service.monitoring.impl.BusinessLogServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 业务日志服务测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class BusinessLogServiceTest {
    
    @Mock
    private BusinessLogRepository businessLogRepository;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private BusinessLogServiceImpl businessLogService;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testLogDifferenceProcessing() throws Exception {
        // 准备测试数据
        InventoryDifferenceProcessDTO processDTO = new InventoryDifferenceProcessDTO();
        processDTO.setId(1);
        processDTO.setProcessQuantity(100.0);
        
        String result = "处理成功";
        Integer userId = 1;
        String userName = "testUser";
        
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"id\":1,\"processQuantity\":100.0}");
        when(businessLogRepository.save(any(TMpdBusinessLog.class))).thenReturn(new TMpdBusinessLog());
        
        // 执行测试
        businessLogService.logDifferenceProcessing(processDTO, result, userId, userName);
        
        // 验证结果
        verify(businessLogRepository, times(1)).save(any(TMpdBusinessLog.class));
        verify(objectMapper, times(1)).writeValueAsString(processDTO);
    }
    
    @Test
    void testLogSapStockSync() throws Exception {
        // 准备测试数据
        SapStockSyncDTO syncResult = new SapStockSyncDTO();
        syncResult.setSyncStatus("SUCCESS");
        syncResult.setSuccessCount(10);
        syncResult.setFailureCount(0);
        
        Integer userId = 1;
        String userName = "testUser";
        
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"syncStatus\":\"SUCCESS\",\"successCount\":10}");
        when(businessLogRepository.save(any(TMpdBusinessLog.class))).thenReturn(new TMpdBusinessLog());
        
        // 执行测试
        businessLogService.logSapStockSync(syncResult, userId, userName);
        
        // 验证结果
        verify(businessLogRepository, times(1)).save(any(TMpdBusinessLog.class));
        verify(objectMapper, times(1)).writeValueAsString(syncResult);
    }
    
    @Test
    void testLogDifferenceRecordGeneration() {
        // 准备测试数据
        int recordCount = 5;
        Integer userId = 1;
        String userName = "testUser";
        
        when(businessLogRepository.save(any(TMpdBusinessLog.class))).thenReturn(new TMpdBusinessLog());
        
        // 执行测试
        businessLogService.logDifferenceRecordGeneration(recordCount, userId, userName);
        
        // 验证结果
        verify(businessLogRepository, times(1)).save(any(TMpdBusinessLog.class));
    }
    
    @Test
    void testLogQueryOperation() {
        // 准备测试数据
        String queryType = "DIFFERENCE_QUERY";
        String queryParams = "{\"materialCode\":\"M001\"}";
        int resultCount = 10;
        Integer userId = 1;
        String userName = "testUser";
        
        when(businessLogRepository.save(any(TMpdBusinessLog.class))).thenReturn(new TMpdBusinessLog());
        
        // 执行测试
        businessLogService.logQueryOperation(queryType, queryParams, resultCount, userId, userName);
        
        // 验证结果
        verify(businessLogRepository, times(1)).save(any(TMpdBusinessLog.class));
    }
    
    @Test
    void testLogErrorOperation() {
        // 准备测试数据
        String operation = "DIFFERENCE_PROCESSING";
        String errorMessage = "数据库连接失败";
        Exception exception = new RuntimeException("Connection timeout");
        Integer userId = 1;
        String userName = "testUser";
        
        when(businessLogRepository.save(any(TMpdBusinessLog.class))).thenReturn(new TMpdBusinessLog());
        
        // 执行测试
        businessLogService.logErrorOperation(operation, errorMessage, exception, userId, userName);
        
        // 验证结果
        verify(businessLogRepository, times(1)).save(any(TMpdBusinessLog.class));
    }
}