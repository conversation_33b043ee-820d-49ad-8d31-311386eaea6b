package com.hvisions.rawmaterial.service.difference;

import com.hvisions.rawmaterial.dao.difference.MaterialDifferenceMapper;
import com.hvisions.rawmaterial.dto.difference.MaterialDifferenceDTO;
import com.hvisions.rawmaterial.dto.difference.MaterialDifferenceProcessDTO;
import com.hvisions.rawmaterial.dto.difference.MaterialDifferenceQueryDTO;
import com.hvisions.rawmaterial.dto.difference.SapStockSyncDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdMaterialDifference;
import com.hvisions.rawmaterial.service.MesDataService;
import com.hvisions.rawmaterial.service.SapIntegrationService;
import com.hvisions.rawmaterial.service.difference.impl.MaterialDifferenceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 原辅料差异处理服务单元测试
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class MaterialDifferenceServiceTest {

    @Mock
    private MaterialDifferenceMapper materialDifferenceMapper;

    @Mock
    private MesDataService mesDataService;

    @Mock
    private SapIntegrationService sapIntegrationService;

    @InjectMocks
    private MaterialDifferenceServiceImpl materialDifferenceService;

    private TMpdMaterialDifference testEntity;
    private MaterialDifferenceProcessDTO testProcessDTO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testEntity = TMpdMaterialDifference.builder()
            .id(1)
            .materialCode("MAT001")
            .materialName("测试物料")
            .erpWarehouseCode("WH001")
            .erpWarehouseName("测试仓库")
            .department("原辅料管理部")
            .mesCurrentStock(new BigDecimal("1000.000"))
            .mesInitialStock(new BigDecimal("800.000"))
            .weighbridgeReceiptQty(new BigDecimal("300.000"))
            .solidWasteQty(new BigDecimal("50.000"))
            .issueQty(new BigDecimal("100.000"))
            .unit("KG")
            .sapStock(new BigDecimal("950.000"))
            .differenceQty(new BigDecimal("50.000"))
            .statisticsStartDate(new Date(System.currentTimeMillis() - 86400000)) // 昨天
            .statisticsEndDate(null)
            .status(0) // 待处理
            .createTime(new Date())
            .updateTime(new Date())
            .deleted(false)
            .build();

        testProcessDTO = new MaterialDifferenceProcessDTO();
        testProcessDTO.setId(1);
        testProcessDTO.setStatisticsEndDate(new Date());
        testProcessDTO.setProcessor("测试用户");
        testProcessDTO.setProcessorId(1001);
        testProcessDTO.setProcessRemark("测试处理");
    }

    @Test
    void testFindByCondition_Success() {
        // Given
        MaterialDifferenceQueryDTO queryDTO = new MaterialDifferenceQueryDTO();
        queryDTO.setMaterialCode("MAT001");
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        List<TMpdMaterialDifference> entities = Arrays.asList(testEntity);
        when(materialDifferenceMapper.selectByCondition(queryDTO)).thenReturn(entities);

        // When
        Page<MaterialDifferenceDTO> result = materialDifferenceService.findByCondition(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("MAT001", result.getContent().get(0).getMaterialCode());
        verify(materialDifferenceMapper).selectByCondition(queryDTO);
    }

    @Test
    void testFindById_Success() {
        // Given
        when(materialDifferenceMapper.selectById(1)).thenReturn(testEntity);

        // When
        MaterialDifferenceDTO result = materialDifferenceService.findById(1);

        // Then
        assertNotNull(result);
        assertEquals("MAT001", result.getMaterialCode());
        assertEquals("测试物料", result.getMaterialName());
        assertEquals(0, result.getStatus());
        assertEquals("待处理", result.getStatusDesc());
        verify(materialDifferenceMapper).selectById(1);
    }

    @Test
    void testFindById_NotFound() {
        // Given
        when(materialDifferenceMapper.selectById(999)).thenReturn(null);

        // When
        MaterialDifferenceDTO result = materialDifferenceService.findById(999);

        // Then
        assertNull(result);
        verify(materialDifferenceMapper).selectById(999);
    }

    @Test
    void testProcessDifference_Success() {
        // Given
        when(materialDifferenceMapper.selectById(1)).thenReturn(testEntity);
        when(materialDifferenceMapper.updateById(any(TMpdMaterialDifference.class))).thenReturn(1);
        when(materialDifferenceMapper.selectOne(any())).thenReturn(null); // 不存在待处理记录
        when(materialDifferenceMapper.insert(any(TMpdMaterialDifference.class))).thenReturn(1);

        // Mock MES数据服务
        when(mesDataService.getMesCurrentStock(anyString(), anyString())).thenReturn(new BigDecimal("1050.000"));
        when(mesDataService.getWeighbridgeReceiptQuantity(anyString(), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("100.000"));
        when(mesDataService.getSolidWasteQuantity(anyString(), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("20.000"));
        when(mesDataService.getIssuedQuantity(anyString(), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("30.000"));

        // When
        String result = materialDifferenceService.processDifference(testProcessDTO);

        // Then
        assertEquals("处理成功", result);
        verify(materialDifferenceMapper).selectById(1);
        verify(materialDifferenceMapper).updateById(any(TMpdMaterialDifference.class));
        verify(materialDifferenceMapper).insert(any(TMpdMaterialDifference.class)); // 生成下一周期记录
    }

    @Test
    void testProcessDifference_RecordNotFound() {
        // Given
        when(materialDifferenceMapper.selectById(999)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            MaterialDifferenceProcessDTO processDTO = new MaterialDifferenceProcessDTO();
            processDTO.setId(999);
            processDTO.setStatisticsEndDate(new Date());
            processDTO.setProcessor("测试用户");
            processDTO.setProcessorId(1001);
            materialDifferenceService.processDifference(processDTO);
        });

        assertTrue(exception.getMessage().contains("差异记录不存在"));
        verify(materialDifferenceMapper).selectById(999);
    }

    @Test
    void testProcessDifference_AlreadyProcessed() {
        // Given
        testEntity.setStatus(1); // 已处理
        when(materialDifferenceMapper.selectById(1)).thenReturn(testEntity);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            materialDifferenceService.processDifference(testProcessDTO);
        });

        assertTrue(exception.getMessage().contains("该差异记录已处理"));
        verify(materialDifferenceMapper).selectById(1);
    }

    @Test
    void testProcessDifference_InvalidEndDate() {
        // Given
        testProcessDTO.setStatisticsEndDate(new Date(System.currentTimeMillis() - 172800000)); // 前天，早于开始日期
        when(materialDifferenceMapper.selectById(1)).thenReturn(testEntity);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            materialDifferenceService.processDifference(testProcessDTO);
        });

        assertTrue(exception.getMessage().contains("统计结束日期不能早于开始日期"));
    }

    @Test
    void testRecalculateDifference_Success() {
        // Given
        when(materialDifferenceMapper.selectById(1)).thenReturn(testEntity);
        when(materialDifferenceMapper.updateById(any(TMpdMaterialDifference.class))).thenReturn(1);

        // Mock MES数据服务
        when(mesDataService.getMesCurrentStock("MAT001", "WH001")).thenReturn(new BigDecimal("1100.000"));
        when(mesDataService.getWeighbridgeReceiptQuantity(eq("MAT001"), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("350.000"));
        when(mesDataService.getSolidWasteQuantity(eq("MAT001"), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("60.000"));
        when(mesDataService.getIssuedQuantity(eq("MAT001"), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("120.000"));

        // Mock SAP数据
        when(materialDifferenceMapper.selectSapStock("MAT001", "WH001")).thenReturn(new BigDecimal("980.000"));

        // When
        MaterialDifferenceDTO result = materialDifferenceService.recalculateDifference(1);

        // Then
        assertNotNull(result);
        assertEquals(new BigDecimal("1100.000"), result.getMesCurrentStock());
        assertEquals(new BigDecimal("350.000"), result.getWeighbridgeReceiptQty());
        assertEquals(new BigDecimal("60.000"), result.getSolidWasteQty());
        assertEquals(new BigDecimal("120.000"), result.getIssueQty());
        assertEquals(new BigDecimal("980.000"), result.getSapStock());
        
        // 验证差异数量计算：当前库存(1100) - 理论库存(800+350-60-120=970) = 130
        assertEquals(new BigDecimal("130.000"), result.getDifferenceQty());
        
        verify(materialDifferenceMapper).selectById(1);
        verify(materialDifferenceMapper).updateById(any(TMpdMaterialDifference.class));
    }

    @Test
    void testRecalculateDifference_RecordNotFound() {
        // Given
        when(materialDifferenceMapper.selectById(999)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            materialDifferenceService.recalculateDifference(999);
        });

        assertTrue(exception.getMessage().contains("差异记录不存在"));
    }

    @Test
    void testRecalculateDifference_AlreadyProcessed() {
        // Given
        testEntity.setStatus(1); // 已处理
        when(materialDifferenceMapper.selectById(1)).thenReturn(testEntity);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            materialDifferenceService.recalculateDifference(1);
        });

        assertTrue(exception.getMessage().contains("已处理的差异记录不能重新计算"));
    }

    @Test
    void testGenerateDifferenceRecords_Success() {
        // Given
        Map<String, Object> combination = new HashMap<>();
        combination.put("material_code", "MAT001");
        combination.put("material_name", "测试物料");
        combination.put("erp_warehouse_code", "WH001");
        combination.put("erp_warehouse_name", "测试仓库");
        combination.put("unit", "KG");

        when(materialDifferenceMapper.selectMaterialWarehouseCombinations())
            .thenReturn(Arrays.asList(combination));
        when(materialDifferenceMapper.selectOne(any())).thenReturn(null); // 不存在待处理记录
        when(materialDifferenceMapper.selectLatestProcessedRecord("MAT001", "WH001")).thenReturn(null);
        when(materialDifferenceMapper.batchInsert(anyList())).thenReturn(1);

        // Mock MES数据服务
        when(mesDataService.getMesCurrentStock("MAT001", "WH001")).thenReturn(new BigDecimal("1000.000"));
        when(mesDataService.getWeighbridgeReceiptQuantity(eq("MAT001"), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("200.000"));
        when(mesDataService.getSolidWasteQuantity(eq("MAT001"), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("30.000"));
        when(mesDataService.getIssuedQuantity(eq("MAT001"), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("80.000"));

        // When
        String result = materialDifferenceService.generateDifferenceRecords();

        // Then
        assertTrue(result.contains("共生成1条记录"));
        verify(materialDifferenceMapper).selectMaterialWarehouseCombinations();
        verify(materialDifferenceMapper).batchInsert(anyList());
    }

    @Test
    void testGenerateDifferenceRecords_NoMaterialCombinations() {
        // Given
        when(materialDifferenceMapper.selectMaterialWarehouseCombinations()).thenReturn(Collections.emptyList());

        // When
        String result = materialDifferenceService.generateDifferenceRecords();

        // Then
        assertEquals("未找到需要生成差异记录的物料仓库组合", result);
        verify(materialDifferenceMapper).selectMaterialWarehouseCombinations();
        verify(materialDifferenceMapper, never()).batchInsert(anyList());
    }

    @Test
    void testSyncSapStock_Success() {
        // Given
        Map<String, Object> combination = new HashMap<>();
        combination.put("material_code", "MAT001");
        combination.put("material_name", "测试物料");
        combination.put("erp_warehouse_code", "WH001");
        combination.put("erp_warehouse_name", "测试仓库");
        combination.put("unit", "KG");

        when(materialDifferenceMapper.selectMaterialWarehouseCombinations())
            .thenReturn(Arrays.asList(combination));
        when(materialDifferenceMapper.updateSapStock(anyString(), anyString(), any(BigDecimal.class), any(Date.class)))
            .thenReturn(1);

        // When
        SapStockSyncDTO result = materialDifferenceService.syncSapStock();

        // Then
        assertNotNull(result);
        assertEquals("success", result.getSyncStatus());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailedCount());
        verify(materialDifferenceMapper).selectMaterialWarehouseCombinations();
    }

    @Test
    void testGetDifferenceStatistics_Success() {
        // Given
        Map<String, Object> pendingStats = new HashMap<>();
        pendingStats.put("total_count", 5L);
        pendingStats.put("total_difference", new BigDecimal("1000.000"));

        Map<String, Object> processedStats = new HashMap<>();
        processedStats.put("total_count", 10L);
        processedStats.put("total_difference", new BigDecimal("2000.000"));

        when(materialDifferenceMapper.selectPendingStatistics()).thenReturn(pendingStats);
        when(materialDifferenceMapper.selectProcessedStatistics(any(Date.class), any(Date.class)))
            .thenReturn(processedStats);

        // When
        Map<String, Object> result = materialDifferenceService.getDifferenceStatistics();

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("pending"));
        assertTrue(result.containsKey("processed"));
        assertEquals(pendingStats, result.get("pending"));
        assertEquals(processedStats, result.get("processed"));
    }

    @Test
    void testHasPendingDifferences_True() {
        // Given
        Map<String, Object> pendingStats = new HashMap<>();
        pendingStats.put("total_count", 5L);
        when(materialDifferenceMapper.selectPendingStatistics()).thenReturn(pendingStats);

        // When
        Boolean result = materialDifferenceService.hasPendingDifferences();

        // Then
        assertTrue(result);
    }

    @Test
    void testHasPendingDifferences_False() {
        // Given
        Map<String, Object> pendingStats = new HashMap<>();
        pendingStats.put("total_count", 0L);
        when(materialDifferenceMapper.selectPendingStatistics()).thenReturn(pendingStats);

        // When
        Boolean result = materialDifferenceService.hasPendingDifferences();

        // Then
        assertFalse(result);
    }

    @Test
    void testBatchProcessDifference_Success() {
        // Given
        List<Integer> ids = Arrays.asList(1, 2);
        TMpdMaterialDifference entity1 = TMpdMaterialDifference.builder()
            .id(1).status(0).deleted(false).build();
        TMpdMaterialDifference entity2 = TMpdMaterialDifference.builder()
            .id(2).status(0).deleted(false).build();

        when(materialDifferenceMapper.selectById(1)).thenReturn(entity1);
        when(materialDifferenceMapper.selectById(2)).thenReturn(entity2);
        when(materialDifferenceMapper.updateById(any(TMpdMaterialDifference.class))).thenReturn(1);
        when(materialDifferenceMapper.selectOne(any())).thenReturn(null); // 不存在待处理记录
        when(materialDifferenceMapper.insert(any(TMpdMaterialDifference.class))).thenReturn(1);

        // Mock MES数据服务
        when(mesDataService.getMesCurrentStock(anyString(), anyString())).thenReturn(new BigDecimal("1000.000"));
        when(mesDataService.getWeighbridgeReceiptQuantity(anyString(), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("100.000"));
        when(mesDataService.getSolidWasteQuantity(anyString(), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("20.000"));
        when(mesDataService.getIssuedQuantity(anyString(), any(Date.class), any(Date.class)))
            .thenReturn(new BigDecimal("30.000"));

        // When
        String result = materialDifferenceService.batchProcessDifference(ids, "测试用户", 1001, "批量处理");

        // Then
        assertTrue(result.contains("共处理2条记录"));
        verify(materialDifferenceMapper, times(2)).updateById(any(TMpdMaterialDifference.class));
    }

    @Test
    void testBatchProcessDifference_EmptyIds() {
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            materialDifferenceService.batchProcessDifference(Collections.emptyList(), "测试用户", 1001, "批量处理");
        });

        assertTrue(exception.getMessage().contains("请选择要处理的差异记录"));
    }

    @Test
    void testValidateProcessRequest_NullRequest() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            materialDifferenceService.processDifference(null);
        });

        assertTrue(exception.getMessage().contains("处理请求不能为空"));
    }

    @Test
    void testValidateProcessRequest_NullId() {
        // Given
        MaterialDifferenceProcessDTO processDTO = new MaterialDifferenceProcessDTO();
        processDTO.setId(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            materialDifferenceService.processDifference(processDTO);
        });

        assertTrue(exception.getMessage().contains("差异记录ID不能为空"));
    }

    @Test
    void testValidateProcessRequest_FutureEndDate() {
        // Given
        MaterialDifferenceProcessDTO processDTO = new MaterialDifferenceProcessDTO();
        processDTO.setId(1);
        processDTO.setStatisticsEndDate(new Date(System.currentTimeMillis() + 86400000)); // 明天
        processDTO.setProcessor("测试用户");
        processDTO.setProcessorId(1001);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            materialDifferenceService.processDifference(processDTO);
        });

        assertTrue(exception.getMessage().contains("统计结束日期不能晚于当前时间"));
    }
}