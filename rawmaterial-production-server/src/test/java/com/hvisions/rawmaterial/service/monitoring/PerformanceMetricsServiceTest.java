package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dao.log.PerformanceMetricsRepository;
import com.hvisions.rawmaterial.dto.monitoring.PerformanceMetrics;
import com.hvisions.rawmaterial.dto.monitoring.SystemMetrics;
import com.hvisions.rawmaterial.entity.log.TMpdPerformanceMetrics;
import com.hvisions.rawmaterial.service.monitoring.impl.PerformanceMetricsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 性能指标监控服务测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class PerformanceMetricsServiceTest {
    
    @Mock
    private PerformanceMetricsRepository performanceMetricsRepository;
    
    @InjectMocks
    private PerformanceMetricsServiceImpl performanceMetricsService;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testRecordMethodPerformance() {
        // 准备测试数据
        String methodName = "com.hvisions.rawmaterial.service.difference.MaterialDifferenceService.processDifference";
        long executionTime = 1500L;
        boolean success = true;
        String parameters = "{\"id\":1,\"processQuantity\":100.0}";
        
        when(performanceMetricsRepository.save(any(TMpdPerformanceMetrics.class)))
            .thenReturn(new TMpdPerformanceMetrics());
        
        // 执行测试
        performanceMetricsService.recordMethodPerformance(methodName, executionTime, success, parameters);
        
        // 验证结果
        verify(performanceMetricsRepository, times(1)).save(any(TMpdPerformanceMetrics.class));
    }
    
    @Test
    void testRecordDatabaseQueryPerformance() {
        // 准备测试数据
        String queryType = "SELECT_DIFFERENCE_RECORDS";
        long executionTime = 800L;
        int recordCount = 50;
        String sqlStatement = "SELECT * FROM t_mpd_inventory_difference WHERE status = ?";
        
        when(performanceMetricsRepository.save(any(TMpdPerformanceMetrics.class)))
            .thenReturn(new TMpdPerformanceMetrics());
        
        // 执行测试
        performanceMetricsService.recordDatabaseQueryPerformance(queryType, executionTime, recordCount, sqlStatement);
        
        // 验证结果
        verify(performanceMetricsRepository, times(1)).save(any(TMpdPerformanceMetrics.class));
    }
    
    @Test
    void testRecordCachePerformance() {
        // 准备测试数据
        String operation = "GET";
        String cacheKey = "sap_stock:M001:W001";
        boolean hit = true;
        long executionTime = 10L;
        
        when(performanceMetricsRepository.save(any(TMpdPerformanceMetrics.class)))
            .thenReturn(new TMpdPerformanceMetrics());
        
        // 执行测试
        performanceMetricsService.recordCachePerformance(operation, cacheKey, hit, executionTime);
        
        // 验证结果
        verify(performanceMetricsRepository, times(1)).save(any(TMpdPerformanceMetrics.class));
    }
    
    @Test
    void testGetMethodPerformanceMetrics() {
        // 准备测试数据
        String methodName = "processDifference";
        LocalDateTime startTime = LocalDateTime.now().minusHours(1);
        LocalDateTime endTime = LocalDateTime.now();
        
        TMpdPerformanceMetrics metric1 = new TMpdPerformanceMetrics();
        metric1.setExecutionTime(1000L);
        metric1.setSuccess(true);
        
        TMpdPerformanceMetrics metric2 = new TMpdPerformanceMetrics();
        metric2.setExecutionTime(1500L);
        metric2.setSuccess(true);
        
        TMpdPerformanceMetrics metric3 = new TMpdPerformanceMetrics();
        metric3.setExecutionTime(2000L);
        metric3.setSuccess(false);
        
        List<TMpdPerformanceMetrics> metrics = Arrays.asList(metric1, metric2, metric3);
        
        when(performanceMetricsRepository.findByMetricTypeAndMetricNameAndRecordTimeBetween(
            "METHOD_EXECUTION", methodName, startTime, endTime)).thenReturn(metrics);
        
        // 执行测试
        PerformanceMetrics result = performanceMetricsService.getMethodPerformanceMetrics(methodName, startTime, endTime);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(methodName, result.getMetricName());
        assertEquals(3, result.getTotalCount());
        assertEquals(2, result.getSuccessCount());
        assertEquals(1, result.getFailureCount());
        assertEquals(66.67, result.getSuccessRate(), 0.01);
        assertEquals(1500.0, result.getAverageExecutionTime(), 0.01);
        assertEquals(2000L, result.getMaxExecutionTime());
        assertEquals(1000L, result.getMinExecutionTime());
    }
    
    @Test
    void testGetCachePerformanceMetrics() {
        // 准备测试数据
        LocalDateTime startTime = LocalDateTime.now().minusHours(1);
        LocalDateTime endTime = LocalDateTime.now();
        
        TMpdPerformanceMetrics metric1 = new TMpdPerformanceMetrics();
        metric1.setExecutionTime(10L);
        metric1.setCacheHit(true);
        
        TMpdPerformanceMetrics metric2 = new TMpdPerformanceMetrics();
        metric2.setExecutionTime(15L);
        metric2.setCacheHit(true);
        
        TMpdPerformanceMetrics metric3 = new TMpdPerformanceMetrics();
        metric3.setExecutionTime(100L);
        metric3.setCacheHit(false);
        
        List<TMpdPerformanceMetrics> metrics = Arrays.asList(metric1, metric2, metric3);
        
        when(performanceMetricsRepository.findByMetricTypeAndRecordTimeBetween(
            "CACHE_OPERATION", startTime, endTime)).thenReturn(metrics);
        
        // 执行测试
        Map<String, Object> result = performanceMetricsService.getCachePerformanceMetrics(startTime, endTime);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3L, result.get("totalOperations"));
        assertEquals(2L, result.get("hitCount"));
        assertEquals(1L, result.get("missCount"));
        assertEquals(66.67, (Double) result.get("hitRate"), 0.01);
        assertEquals(41.67, (Double) result.get("averageExecutionTime"), 0.01);
    }
    
    @Test
    void testGetSystemMetrics() {
        // 执行测试
        SystemMetrics result = performanceMetricsService.getSystemMetrics();
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getUsedMemory());
        assertNotNull(result.getMaxMemory());
        assertNotNull(result.getMemoryUsagePercent());
        assertNotNull(result.getCpuUsagePercent());
        assertNotNull(result.getAvailableProcessors());
        assertNotNull(result.getActiveThreadCount());
        assertNotNull(result.getRecordTime());
        
        assertTrue(result.getUsedMemory() > 0);
        assertTrue(result.getMaxMemory() > 0);
        assertTrue(result.getMemoryUsagePercent() >= 0 && result.getMemoryUsagePercent() <= 100);
        assertTrue(result.getAvailableProcessors() > 0);
        assertTrue(result.getActiveThreadCount() > 0);
    }
    
    @Test
    void testGetSlowQueries() {
        // 准备测试数据
        long threshold = 3000L;
        int limit = 10;
        
        TMpdPerformanceMetrics slowQuery1 = new TMpdPerformanceMetrics();
        slowQuery1.setMetricName("SELECT_LARGE_DATA");
        slowQuery1.setExecutionTime(5000L);
        slowQuery1.setSqlStatement("SELECT * FROM large_table WHERE condition = ?");
        
        TMpdPerformanceMetrics slowQuery2 = new TMpdPerformanceMetrics();
        slowQuery2.setMetricName("COMPLEX_JOIN_QUERY");
        slowQuery2.setExecutionTime(4000L);
        slowQuery2.setSqlStatement("SELECT * FROM table1 t1 JOIN table2 t2 ON t1.id = t2.id");
        
        List<TMpdPerformanceMetrics> slowQueries = Arrays.asList(slowQuery1, slowQuery2);
        
        when(performanceMetricsRepository.findSlowQueries("DATABASE_QUERY", threshold, limit))
            .thenReturn(slowQueries);
        
        // 执行测试
        List<PerformanceMetrics> result = performanceMetricsService.getSlowQueries(threshold, limit);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("SELECT_LARGE_DATA", result.get(0).getMetricName());
        assertEquals(5000.0, result.get(0).getExecutionTime());
        assertEquals("COMPLEX_JOIN_QUERY", result.get(1).getMetricName());
        assertEquals(4000.0, result.get(1).getExecutionTime());
    }
    
    @Test
    void testGetPerformanceTrend() {
        // 准备测试数据
        String metricType = "METHOD_EXECUTION";
        int hours = 24;
        
        TMpdPerformanceMetrics metric1 = new TMpdPerformanceMetrics();
        metric1.setExecutionTime(1000L);
        metric1.setRecordTime(LocalDateTime.now().withHour(10));
        
        TMpdPerformanceMetrics metric2 = new TMpdPerformanceMetrics();
        metric2.setExecutionTime(1500L);
        metric2.setRecordTime(LocalDateTime.now().withHour(10));
        
        TMpdPerformanceMetrics metric3 = new TMpdPerformanceMetrics();
        metric3.setExecutionTime(2000L);
        metric3.setRecordTime(LocalDateTime.now().withHour(11));
        
        List<TMpdPerformanceMetrics> metrics = Arrays.asList(metric1, metric2, metric3);
        
        when(performanceMetricsRepository.findByMetricTypeAndRecordTimeBetween(
            eq(metricType), any(LocalDateTime.class), any(LocalDateTime.class))).thenReturn(metrics);
        
        // 执行测试
        List<Map<String, Object>> result = performanceMetricsService.getPerformanceTrend(metricType, hours);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(hours, result.size());
        
        // 验证每个小时的数据结构
        for (Map<String, Object> hourData : result) {
            assertTrue(hourData.containsKey("hour"));
            assertTrue(hourData.containsKey("count"));
            assertTrue(hourData.containsKey("averageExecutionTime"));
        }
    }
}