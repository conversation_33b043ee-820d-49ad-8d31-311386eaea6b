package com.hvisions.rawmaterial.service.async;

import com.hvisions.rawmaterial.configuration.InventoryDifferenceRabbitConfig;
import com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage;
import com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage;
import com.hvisions.rawmaterial.dto.async.TaskStatusMessage;
import com.hvisions.rawmaterial.service.async.impl.AsyncTaskServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 消息处理器集成测试
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
public class MessageProcessorIntegrationTest {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private AsyncTaskServiceImpl asyncTaskService;
    
    @Test
    public void testSapSyncMessageProcessing() throws InterruptedException {
        // 准备测试数据
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("PARTIAL");
        message.setMaterialCodes(Arrays.asList("TEST_MAT001", "TEST_MAT002"));
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        message.setForceSync(false);
        
        // 初始化任务状态
        TaskStatusMessage initialStatus = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.PENDING
        );
        initialStatus.setStatusDescription("任务已提交，等待处理");
        asyncTaskService.updateTaskStatus(initialStatus);
        
        // 发送消息到队列
        rabbitTemplate.convertAndSend(
            InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
            InventoryDifferenceRabbitConfig.SAP_SYNC_ROUTING_KEY,
            message
        );
        
        // 等待消息处理
        Thread.sleep(5000);
        
        // 验证任务状态已更新
        TaskStatusMessage finalStatus = asyncTaskService.getTaskStatus(message.getTaskId());
        assertNotNull(finalStatus);
        assertEquals(message.getTaskId(), finalStatus.getTaskId());
        
        // 状态应该是RUNNING或SUCCESS或FAILED（取决于实际处理结果）
        assertTrue(finalStatus.getStatus() == TaskStatusMessage.TaskStatus.RUNNING ||
                  finalStatus.getStatus() == TaskStatusMessage.TaskStatus.SUCCESS ||
                  finalStatus.getStatus() == TaskStatusMessage.TaskStatus.FAILED);
    }
    
    @Test
    public void testBatchGenerateMessageProcessing() throws InterruptedException {
        // 准备测试数据
        BatchGenerateTaskMessage message = new BatchGenerateTaskMessage();
        message.setStatisticsStartDate(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
        message.setStatisticsEndDate(new Date());
        message.setMaterialCodes(Arrays.asList("TEST_MAT001", "TEST_MAT002"));
        message.setWarehouseCodes(Arrays.asList("TEST_WH001"));
        message.setBatchSize(10);
        message.setEstimatedCount(20);
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        
        // 初始化任务状态
        TaskStatusMessage initialStatus = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.PENDING
        );
        initialStatus.setStatusDescription("任务已提交，等待处理");
        initialStatus.setTotalCount(message.getEstimatedCount());
        asyncTaskService.updateTaskStatus(initialStatus);
        
        // 发送消息到队列
        rabbitTemplate.convertAndSend(
            InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
            InventoryDifferenceRabbitConfig.BATCH_GENERATE_ROUTING_KEY,
            message
        );
        
        // 等待消息处理
        Thread.sleep(8000);
        
        // 验证任务状态已更新
        TaskStatusMessage finalStatus = asyncTaskService.getTaskStatus(message.getTaskId());
        assertNotNull(finalStatus);
        assertEquals(message.getTaskId(), finalStatus.getTaskId());
        
        // 状态应该是RUNNING或SUCCESS或FAILED（取决于实际处理结果）
        assertTrue(finalStatus.getStatus() == TaskStatusMessage.TaskStatus.RUNNING ||
                  finalStatus.getStatus() == TaskStatusMessage.TaskStatus.SUCCESS ||
                  finalStatus.getStatus() == TaskStatusMessage.TaskStatus.FAILED);
    }
    
    @Test
    public void testMessageRetryMechanism() throws InterruptedException {
        // 准备一个会失败的消息（通过设置无效参数）
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("INVALID_TYPE"); // 无效的同步类型
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        message.setMaxRetryCount(2); // 设置较小的重试次数以便测试
        
        // 初始化任务状态
        TaskStatusMessage initialStatus = new TaskStatusMessage(
            message.getTaskId(), 
            message.getTaskType(), 
            TaskStatusMessage.TaskStatus.PENDING
        );
        asyncTaskService.updateTaskStatus(initialStatus);
        
        // 发送消息到队列
        rabbitTemplate.convertAndSend(
            InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
            InventoryDifferenceRabbitConfig.SAP_SYNC_ROUTING_KEY,
            message
        );
        
        // 等待重试完成
        Thread.sleep(15000);
        
        // 验证最终状态为失败
        TaskStatusMessage finalStatus = asyncTaskService.getTaskStatus(message.getTaskId());
        assertNotNull(finalStatus);
        assertEquals(TaskStatusMessage.TaskStatus.FAILED, finalStatus.getStatus());
        assertNotNull(finalStatus.getErrorMessage());
    }
    
    @Test
    public void testTaskStatusUpdate() throws InterruptedException {
        // 准备测试数据
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("FULL");
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        
        String taskId = message.getTaskId();
        
        // 测试不同状态的更新
        TaskStatusMessage pendingStatus = new TaskStatusMessage(taskId, "SAP_SYNC", TaskStatusMessage.TaskStatus.PENDING);
        pendingStatus.setStatusDescription("任务等待处理");
        asyncTaskService.updateTaskStatus(pendingStatus);
        
        TaskStatusMessage runningStatus = new TaskStatusMessage(taskId, "SAP_SYNC", TaskStatusMessage.TaskStatus.RUNNING);
        runningStatus.setStatusDescription("任务执行中");
        runningStatus.setProgress(50);
        runningStatus.setTotalCount(100);
        runningStatus.setProcessedCount(50);
        asyncTaskService.updateTaskStatus(runningStatus);
        
        TaskStatusMessage successStatus = new TaskStatusMessage(taskId, "SAP_SYNC", TaskStatusMessage.TaskStatus.SUCCESS);
        successStatus.setStatusDescription("任务执行成功");
        successStatus.setProgress(100);
        successStatus.setTotalCount(100);
        successStatus.setProcessedCount(100);
        successStatus.setSuccessCount(100);
        successStatus.setFailureCount(0);
        asyncTaskService.updateTaskStatus(successStatus);
        
        // 验证最终状态
        TaskStatusMessage finalStatus = asyncTaskService.getTaskStatus(taskId);
        assertNotNull(finalStatus);
        assertEquals(TaskStatusMessage.TaskStatus.SUCCESS, finalStatus.getStatus());
        assertEquals(100, finalStatus.getProgress());
        assertEquals(100, finalStatus.getSuccessCount());
        assertEquals(0, finalStatus.getFailureCount());
    }
    
    @Test
    public void testConcurrentMessageProcessing() throws InterruptedException {
        // 准备多个消息
        int messageCount = 5;
        String[] taskIds = new String[messageCount];
        
        for (int i = 0; i < messageCount; i++) {
            SapSyncTaskMessage message = new SapSyncTaskMessage();
            message.setSyncType("PARTIAL");
            message.setMaterialCodes(Arrays.asList("CONCURRENT_MAT" + i));
            message.setCreatorId(1);
            message.setCreatorName("测试用户");
            
            taskIds[i] = message.getTaskId();
            
            // 初始化任务状态
            TaskStatusMessage initialStatus = new TaskStatusMessage(
                message.getTaskId(), 
                message.getTaskType(), 
                TaskStatusMessage.TaskStatus.PENDING
            );
            asyncTaskService.updateTaskStatus(initialStatus);
            
            // 发送消息到队列
            rabbitTemplate.convertAndSend(
                InventoryDifferenceRabbitConfig.INVENTORY_DIFFERENCE_EXCHANGE,
                InventoryDifferenceRabbitConfig.SAP_SYNC_ROUTING_KEY,
                message
            );
        }
        
        // 等待所有消息处理完成
        Thread.sleep(10000);
        
        // 验证所有任务都被处理
        for (String taskId : taskIds) {
            TaskStatusMessage status = asyncTaskService.getTaskStatus(taskId);
            assertNotNull(status);
            assertNotEquals(TaskStatusMessage.TaskStatus.PENDING, status.getStatus());
        }
    }
}