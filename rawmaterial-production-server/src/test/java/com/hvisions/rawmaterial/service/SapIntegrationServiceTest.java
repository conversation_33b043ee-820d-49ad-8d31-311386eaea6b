package com.hvisions.rawmaterial.service;

import com.hvisions.brewage.purchase.dto.SapBaseResponseDto;
import com.hvisions.rawmaterial.dto.*;
import com.hvisions.rawmaterial.service.impl.SapIntegrationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: SAP集成服务单元测试
 * @Date: 2024/07/28
 */
@ExtendWith(MockitoExtension.class)
class SapIntegrationServiceTest {

    @Mock
    private RequestSap requestSap;

    @InjectMocks
    private SapIntegrationServiceImpl sapIntegrationService;

    private SapBaseResponseDto mockSuccessResponse;
    private SapBaseResponseDto mockFailureResponse;
    private InventoryDifferenceDTO mockDifference;

    @BeforeEach
    void setUp() {
        // 设置成功响应
        mockSuccessResponse = new SapBaseResponseDto();
        mockSuccessResponse.setStatus("S");
        mockSuccessResponse.setMessage("成功");

        // 设置失败响应
        mockFailureResponse = new SapBaseResponseDto();
        mockFailureResponse.setStatus("E");
        mockFailureResponse.setMessage("SAP处理失败");

        // 设置模拟差异数据
        mockDifference = new InventoryDifferenceDTO();
        mockDifference.setId(1);
        mockDifference.setMaterialCode("MAT001");
        mockDifference.setMaterialName("测试物料");
        mockDifference.setErpWarehouseCode("WH001");
        mockDifference.setErpWarehouseName("测试仓库");
        mockDifference.setDifferenceQuantity(new BigDecimal("100.500"));
        mockDifference.setUnit("吨");
    }

    @Test
    void testGetSapStockData_Success() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        List<SapStockSyncDTO> result = sapIntegrationService.getSapStockData("MAT001", "WH001");

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("MAT001", result.get(0).getMaterialCode());
        assertEquals("WH001", result.get(0).getErpWarehouseCode());
        assertEquals(Integer.valueOf(1), result.get(0).getSyncStatus());
        
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testGetSapStockData_WithEmptyMaterialCode() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.getSapStockData("", "WH001");
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testGetSapStockData_WithEmptyWarehouseCode() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.getSapStockData("MAT001", "");
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testGetSapStockData_SapFailure() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenThrow(new RuntimeException("SAP连接失败"));

        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.getSapStockData("MAT001", "WH001");
        });
        
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncDifferenceToSap_Success() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        SapSyncResultDTO result = sapIntegrationService.syncDifferenceToSap(mockDifference);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getSyncStatus());
        assertEquals("同步成功", result.getSyncStatusDesc());
        assertEquals(Integer.valueOf(1), result.getSuccessCount());
        assertEquals(Integer.valueOf(0), result.getFailureCount());
        assertNotNull(result.getSyncTaskId());
        assertNotNull(result.getSyncStartTime());
        assertNotNull(result.getSyncEndTime());
        
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncDifferenceToSap_Failure() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockFailureResponse);

        // When
        SapSyncResultDTO result = sapIntegrationService.syncDifferenceToSap(mockDifference);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(3), result.getSyncStatus());
        assertEquals("同步失败", result.getSyncStatusDesc());
        assertEquals(Integer.valueOf(0), result.getSuccessCount());
        assertEquals(Integer.valueOf(1), result.getFailureCount());
        assertEquals("SAP处理失败", result.getErrorMessage());
        
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncDifferenceToSap_WithNullDifference() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.syncDifferenceToSap(null);
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncDifferenceToSap_WithInvalidDifference() {
        // Given
        InventoryDifferenceDTO invalidDifference = new InventoryDifferenceDTO();
        invalidDifference.setId(1);
        // 缺少必要字段

        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.syncDifferenceToSap(invalidDifference);
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testBatchSyncStock_Success() {
        // Given
        List<String> materialCodes = Arrays.asList("MAT001", "MAT002", "MAT003");
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        SapSyncResultDTO result = sapIntegrationService.batchSyncStock(materialCodes);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(3), result.getTotalCount());
        assertTrue(result.getSuccessCount() >= 0);
        assertTrue(result.getFailureCount() >= 0);
        assertEquals(Integer.valueOf(3), result.getSuccessCount() + result.getFailureCount());
        assertNotNull(result.getSyncTaskId());
        assertNotNull(result.getSyncStartTime());
        assertNotNull(result.getSyncEndTime());
        assertNotNull(result.getSuccessRate());
    }

    @Test
    void testBatchSyncStock_WithEmptyList() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.batchSyncStock(Arrays.asList());
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testBatchSyncStock_WithNullList() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.batchSyncStock(null);
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testBatchGetSapStockData_Success() {
        // Given
        List<String> materialCodes = Arrays.asList("MAT001", "MAT002");
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        List<SapStockSyncDTO> result = sapIntegrationService.batchGetSapStockData(materialCodes, "WH001");

        // Then
        assertNotNull(result);
        // 由于是批量处理，结果数量可能与输入不完全一致
        assertTrue(result.size() >= 0);
    }

    @Test
    void testBatchGetSapStockData_WithEmptyMaterialCodes() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.batchGetSapStockData(Arrays.asList(), "WH001");
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testBatchGetSapStockData_WithEmptyWarehouseCode() {
        // Given
        List<String> materialCodes = Arrays.asList("MAT001", "MAT002");

        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.batchGetSapStockData(materialCodes, "");
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncSingleDifference_Success() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        SapSyncResultDTO result = sapIntegrationService.syncSingleDifference(
                "MAT001", "WH001", new BigDecimal("100.500"), "701");

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getSyncStatus());
        assertEquals("同步成功", result.getSyncStatusDesc());
        assertEquals(Integer.valueOf(1), result.getTotalCount());
        assertEquals(Integer.valueOf(1), result.getSuccessCount());
        assertEquals(Integer.valueOf(0), result.getFailureCount());
        
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncSingleDifference_WithZeroQuantity() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.syncSingleDifference("MAT001", "WH001", BigDecimal.ZERO, "701");
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testSyncSingleDifference_WithNullQuantity() {
        // When & Then
        assertThrows(Exception.class, () -> {
            sapIntegrationService.syncSingleDifference("MAT001", "WH001", null, "701");
        });
        
        verify(requestSap, never()).dockingSap(any(), any(), any());
    }

    @Test
    void testTestSapConnection_Success() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        boolean result = sapIntegrationService.testSapConnection();

        // Then
        assertTrue(result);
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testTestSapConnection_Failure() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockFailureResponse);

        // When
        boolean result = sapIntegrationService.testSapConnection();

        // Then
        assertFalse(result);
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testTestSapConnection_Exception() {
        // Given
        when(requestSap.dockingSap(any(), any(), any())).thenThrow(new RuntimeException("连接超时"));

        // When
        boolean result = sapIntegrationService.testSapConnection();

        // Then
        assertFalse(result);
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testRetryMechanism() {
        // Given
        when(requestSap.dockingSap(any(), any(), any()))
                .thenThrow(new RuntimeException("第一次失败"))
                .thenThrow(new RuntimeException("第二次失败"))
                .thenReturn(mockSuccessResponse);

        // When
        List<SapStockSyncDTO> result = sapIntegrationService.getSapStockData("MAT001", "WH001");

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证重试了3次
        verify(requestSap, times(3)).dockingSap(any(), any(), any());
    }

    @Test
    void testPositiveDifferenceMovementType() {
        // Given
        InventoryDifferenceDTO positiveDifference = new InventoryDifferenceDTO();
        positiveDifference.setId(1);
        positiveDifference.setMaterialCode("MAT001");
        positiveDifference.setErpWarehouseCode("WH001");
        positiveDifference.setDifferenceQuantity(new BigDecimal("100.500")); // 正数，盘盈
        positiveDifference.setUnit("吨");

        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        SapSyncResultDTO result = sapIntegrationService.syncDifferenceToSap(positiveDifference);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getSyncStatus());
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }

    @Test
    void testNegativeDifferenceMovementType() {
        // Given
        InventoryDifferenceDTO negativeDifference = new InventoryDifferenceDTO();
        negativeDifference.setId(1);
        negativeDifference.setMaterialCode("MAT001");
        negativeDifference.setErpWarehouseCode("WH001");
        negativeDifference.setDifferenceQuantity(new BigDecimal("-50.250")); // 负数，盘亏
        negativeDifference.setUnit("吨");

        when(requestSap.dockingSap(any(), any(), any())).thenReturn(mockSuccessResponse);

        // When
        SapSyncResultDTO result = sapIntegrationService.syncDifferenceToSap(negativeDifference);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getSyncStatus());
        verify(requestSap, times(1)).dockingSap(any(), any(), any());
    }
}