package com.hvisions.rawmaterial.service.async;

import com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage;
import com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage;
import com.hvisions.rawmaterial.dto.async.TaskStatusMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 异步任务服务集成测试
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
public class AsyncTaskServiceIntegrationTest {
    
    @Autowired
    private AsyncTaskService asyncTaskService;
    
    @Test
    public void testSubmitSapSyncTask() {
        // 准备测试数据
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("PARTIAL");
        message.setMaterialCodes(Arrays.asList("MAT001", "MAT002", "MAT003"));
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        message.setForceSync(false);
        
        // 执行测试
        String taskId = asyncTaskService.submitSapSyncTask(message);
        
        // 验证结果
        assertNotNull(taskId);
        assertTrue(taskId.startsWith("TASK_"));
        
        // 验证任务状态
        TaskStatusMessage status = asyncTaskService.getTaskStatus(taskId);
        assertNotNull(status);
        assertEquals(taskId, status.getTaskId());
        assertEquals("SAP_SYNC", status.getTaskType());
        assertNotNull(status.getStatus());
    }
    
    @Test
    public void testSubmitBatchGenerateTask() {
        // 准备测试数据
        BatchGenerateTaskMessage message = new BatchGenerateTaskMessage();
        message.setStatisticsStartDate(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)); // 昨天
        message.setStatisticsEndDate(new Date()); // 今天
        message.setMaterialCodes(Arrays.asList("MAT001", "MAT002"));
        message.setWarehouseCodes(Arrays.asList("WH001", "WH002"));
        message.setDepartments(Arrays.asList("DEPT001"));
        message.setOverrideExisting(false);
        message.setBatchSize(50);
        message.setEstimatedCount(100);
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        
        // 执行测试
        String taskId = asyncTaskService.submitBatchGenerateTask(message);
        
        // 验证结果
        assertNotNull(taskId);
        assertTrue(taskId.startsWith("TASK_"));
        
        // 验证任务状态
        TaskStatusMessage status = asyncTaskService.getTaskStatus(taskId);
        assertNotNull(status);
        assertEquals(taskId, status.getTaskId());
        assertEquals("BATCH_GENERATE", status.getTaskType());
        assertNotNull(status.getStatus());
    }
    
    @Test
    public void testGetTaskStatus() {
        // 先提交一个任务
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("FULL");
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        
        String taskId = asyncTaskService.submitSapSyncTask(message);
        
        // 查询任务状态
        TaskStatusMessage status = asyncTaskService.getTaskStatus(taskId);
        
        // 验证结果
        assertNotNull(status);
        assertEquals(taskId, status.getTaskId());
        assertEquals("SAP_SYNC", status.getTaskType());
        assertNotNull(status.getStatus());
        assertNotNull(status.getUpdateTime());
    }
    
    @Test
    public void testGetUserTasks() {
        Integer userId = 1;
        
        // 先提交几个任务
        SapSyncTaskMessage sapMessage = new SapSyncTaskMessage();
        sapMessage.setSyncType("FULL");
        sapMessage.setCreatorId(userId);
        sapMessage.setCreatorName("测试用户");
        asyncTaskService.submitSapSyncTask(sapMessage);
        
        BatchGenerateTaskMessage batchMessage = new BatchGenerateTaskMessage();
        batchMessage.setStatisticsStartDate(new Date());
        batchMessage.setStatisticsEndDate(new Date());
        batchMessage.setCreatorId(userId);
        batchMessage.setCreatorName("测试用户");
        asyncTaskService.submitBatchGenerateTask(batchMessage);
        
        // 查询用户任务
        List<TaskStatusMessage> allTasks = asyncTaskService.getUserTasks(userId, null);
        List<TaskStatusMessage> sapTasks = asyncTaskService.getUserTasks(userId, "SAP_SYNC");
        List<TaskStatusMessage> batchTasks = asyncTaskService.getUserTasks(userId, "BATCH_GENERATE");
        
        // 验证结果
        assertNotNull(allTasks);
        assertTrue(allTasks.size() >= 2);
        
        assertNotNull(sapTasks);
        assertTrue(sapTasks.size() >= 1);
        assertTrue(sapTasks.stream().allMatch(task -> "SAP_SYNC".equals(task.getTaskType())));
        
        assertNotNull(batchTasks);
        assertTrue(batchTasks.size() >= 1);
        assertTrue(batchTasks.stream().allMatch(task -> "BATCH_GENERATE".equals(task.getTaskType())));
    }
    
    @Test
    public void testCancelTask() {
        // 提交一个任务
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("FULL");
        message.setCreatorId(1);
        message.setCreatorName("测试用户");
        
        String taskId = asyncTaskService.submitSapSyncTask(message);
        
        // 取消任务
        boolean cancelled = asyncTaskService.cancelTask(taskId);
        
        // 验证结果
        assertTrue(cancelled);
        
        // 验证任务状态已更新
        TaskStatusMessage status = asyncTaskService.getTaskStatus(taskId);
        assertNotNull(status);
        assertEquals(TaskStatusMessage.TaskStatus.CANCELLED, status.getStatus());
    }
    
    @Test
    public void testGetNonExistentTaskStatus() {
        String nonExistentTaskId = "NON_EXISTENT_TASK_ID";
        
        TaskStatusMessage status = asyncTaskService.getTaskStatus(nonExistentTaskId);
        
        assertNotNull(status);
        assertEquals(nonExistentTaskId, status.getTaskId());
        assertEquals(TaskStatusMessage.TaskStatus.FAILED, status.getStatus());
        assertEquals("任务不存在或已过期", status.getStatusDescription());
    }
    
    @Test
    public void testCancelNonExistentTask() {
        String nonExistentTaskId = "NON_EXISTENT_TASK_ID";
        
        boolean cancelled = asyncTaskService.cancelTask(nonExistentTaskId);
        
        assertFalse(cancelled);
    }
}