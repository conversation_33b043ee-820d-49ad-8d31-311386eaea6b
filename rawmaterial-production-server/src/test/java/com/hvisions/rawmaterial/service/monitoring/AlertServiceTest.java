package com.hvisions.rawmaterial.service.monitoring;

import com.hvisions.rawmaterial.dao.log.AlertNotificationRepository;
import com.hvisions.rawmaterial.dao.log.AlertRuleRepository;
import com.hvisions.rawmaterial.dto.monitoring.AlertNotification;
import com.hvisions.rawmaterial.dto.monitoring.AlertRule;
import com.hvisions.rawmaterial.dto.monitoring.SapInterfaceMetrics;
import com.hvisions.rawmaterial.entity.log.TMpdAlertNotification;
import com.hvisions.rawmaterial.entity.log.TMpdAlertRule;
import com.hvisions.rawmaterial.service.monitoring.impl.AlertServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 告警服务测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class AlertServiceTest {
    
    @Mock
    private AlertRuleRepository alertRuleRepository;
    
    @Mock
    private AlertNotificationRepository alertNotificationRepository;
    
    @Mock
    private SapInterfaceMonitorService sapInterfaceMonitorService;
    
    @Mock
    private PerformanceMetricsService performanceMetricsService;
    
    @InjectMocks
    private AlertServiceImpl alertService;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testSendAlert() {
        // 准备测试数据
        AlertNotification notification = new AlertNotification();
        notification.setAlertType("SAP_INTERFACE");
        notification.setAlertLevel("ERROR");
        notification.setAlertMessage("SAP接口调用失败");
        notification.setMetricName("getSapStock");
        notification.setCurrentValue("false");
        notification.setThresholdValue("true");
        
        when(alertNotificationRepository.save(any(TMpdAlertNotification.class)))
            .thenReturn(new TMpdAlertNotification());
        
        // 执行测试
        alertService.sendAlert(notification);
        
        // 验证结果
        verify(alertNotificationRepository, times(1)).save(any(TMpdAlertNotification.class));
    }
    
    @Test
    void testCreateAlertRule() {
        // 准备测试数据
        AlertRule rule = new AlertRule();
        rule.setRuleName("SAP接口成功率告警");
        rule.setRuleType("SAP_INTERFACE");
        rule.setMetricName("SAP_SUCCESS_RATE");
        rule.setThresholdValue(80.0);
        rule.setOperator("LT");
        rule.setAlertLevel("WARNING");
        rule.setCheckInterval(5);
        rule.setDescription("当SAP接口成功率低于80%时触发告警");
        
        when(alertRuleRepository.save(any(TMpdAlertRule.class)))
            .thenReturn(new TMpdAlertRule());
        
        // 执行测试
        alertService.createAlertRule(rule);
        
        // 验证结果
        verify(alertRuleRepository, times(1)).save(any(TMpdAlertRule.class));
    }
    
    @Test
    void testUpdateAlertRule() {
        // 准备测试数据
        AlertRule rule = new AlertRule();
        rule.setId(1);
        rule.setRuleName("SAP接口成功率告警");
        rule.setRuleType("SAP_INTERFACE");
        rule.setMetricName("SAP_SUCCESS_RATE");
        rule.setThresholdValue(75.0); // 修改阈值
        rule.setOperator("LT");
        rule.setAlertLevel("ERROR"); // 修改告警级别
        rule.setCheckInterval(3); // 修改检查间隔
        rule.setDescription("当SAP接口成功率低于75%时触发告警");
        
        TMpdAlertRule existingRule = new TMpdAlertRule();
        existingRule.setId(1);
        existingRule.setRuleName("SAP接口成功率告警");
        existingRule.setCreateTime(LocalDateTime.now().minusDays(1));
        
        when(alertRuleRepository.findById(1)).thenReturn(Optional.of(existingRule));
        when(alertRuleRepository.save(any(TMpdAlertRule.class))).thenReturn(existingRule);
        
        // 执行测试
        alertService.updateAlertRule(rule);
        
        // 验证结果
        verify(alertRuleRepository, times(1)).findById(1);
        verify(alertRuleRepository, times(1)).save(any(TMpdAlertRule.class));
    }
    
    @Test
    void testDeleteAlertRule() {
        // 准备测试数据
        Integer ruleId = 1;
        
        // 执行测试
        alertService.deleteAlertRule(ruleId);
        
        // 验证结果
        verify(alertRuleRepository, times(1)).deleteById(ruleId);
    }
    
    @Test
    void testGetAllAlertRules() {
        // 准备测试数据
        TMpdAlertRule rule1 = new TMpdAlertRule();
        rule1.setId(1);
        rule1.setRuleName("SAP接口成功率告警");
        rule1.setRuleType("SAP_INTERFACE");
        rule1.setEnabled(true);
        
        TMpdAlertRule rule2 = new TMpdAlertRule();
        rule2.setId(2);
        rule2.setRuleName("方法执行时间告警");
        rule2.setRuleType("PERFORMANCE");
        rule2.setEnabled(true);
        
        List<TMpdAlertRule> rules = Arrays.asList(rule1, rule2);
        
        when(alertRuleRepository.findAll()).thenReturn(rules);
        
        // 执行测试
        List<AlertRule> result = alertService.getAllAlertRules();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("SAP接口成功率告警", result.get(0).getRuleName());
        assertEquals("方法执行时间告警", result.get(1).getRuleName());
    }
    
    @Test
    void testGetAlertHistory() {
        // 准备测试数据
        int pageNum = 1;
        int pageSize = 10;
        
        TMpdAlertNotification notification1 = new TMpdAlertNotification();
        notification1.setId(1);
        notification1.setAlertType("SAP_INTERFACE");
        notification1.setAlertLevel("ERROR");
        notification1.setAlertMessage("SAP接口调用失败");
        notification1.setAlertTime(LocalDateTime.now());
        notification1.setAlertStatus("PENDING");
        
        TMpdAlertNotification notification2 = new TMpdAlertNotification();
        notification2.setId(2);
        notification2.setAlertType("PERFORMANCE");
        notification2.setAlertLevel("WARNING");
        notification2.setAlertMessage("方法执行时间过长");
        notification2.setAlertTime(LocalDateTime.now().minusMinutes(30));
        notification2.setAlertStatus("ACKNOWLEDGED");
        
        List<TMpdAlertNotification> notifications = Arrays.asList(notification1, notification2);
        
        when(alertNotificationRepository.findAllByOrderByAlertTimeDesc(any(Pageable.class)))
            .thenReturn(notifications);
        
        // 执行测试
        List<AlertNotification> result = alertService.getAlertHistory(pageNum, pageSize);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("SAP接口调用失败", result.get(0).getAlertMessage());
        assertEquals("方法执行时间过长", result.get(1).getAlertMessage());
    }
    
    @Test
    void testAcknowledgeAlert() {
        // 准备测试数据
        Integer alertId = 1;
        Integer userId = 100;
        String userName = "admin";
        String remark = "已处理，问题已解决";
        
        TMpdAlertNotification notification = new TMpdAlertNotification();
        notification.setId(alertId);
        notification.setAlertStatus("PENDING");
        
        when(alertNotificationRepository.findById(alertId)).thenReturn(Optional.of(notification));
        when(alertNotificationRepository.save(any(TMpdAlertNotification.class))).thenReturn(notification);
        
        // 执行测试
        alertService.acknowledgeAlert(alertId, userId, userName, remark);
        
        // 验证结果
        verify(alertNotificationRepository, times(1)).findById(alertId);
        verify(alertNotificationRepository, times(1)).save(any(TMpdAlertNotification.class));
        
        assertEquals("ACKNOWLEDGED", notification.getAlertStatus());
        assertEquals(userId, notification.getAcknowledgeUserId());
        assertEquals(userName, notification.getAcknowledgeUserName());
        assertEquals(remark, notification.getAcknowledgeRemark());
        assertNotNull(notification.getAcknowledgeTime());
    }
    
    @Test
    void testCheckSapInterfaceAlerts() {
        // 准备测试数据
        TMpdAlertRule sapRule = new TMpdAlertRule();
        sapRule.setId(1);
        sapRule.setRuleName("SAP健康检查");
        sapRule.setRuleType("SAP_INTERFACE");
        sapRule.setMetricName("SAP_HEALTH_CHECK");
        sapRule.setThresholdValue(1.0);
        sapRule.setOperator("EQ");
        sapRule.setAlertLevel("ERROR");
        sapRule.setCheckInterval(5);
        sapRule.setEnabled(true);
        
        List<TMpdAlertRule> sapRules = Arrays.asList(sapRule);
        
        when(alertRuleRepository.findByRuleTypeAndEnabled("SAP_INTERFACE", true))
            .thenReturn(sapRules);
        when(sapInterfaceMonitorService.checkSapInterfaceHealth()).thenReturn(false);
        when(alertNotificationRepository.save(any(TMpdAlertNotification.class)))
            .thenReturn(new TMpdAlertNotification());
        
        // 执行测试
        alertService.checkSapInterfaceAlerts();
        
        // 验证结果
        verify(alertRuleRepository, times(1)).findByRuleTypeAndEnabled("SAP_INTERFACE", true);
        verify(sapInterfaceMonitorService, times(1)).checkSapInterfaceHealth();
        verify(alertNotificationRepository, times(1)).save(any(TMpdAlertNotification.class));
    }
    
    @Test
    void testCheckSapInterfaceAlertsSuccessRate() {
        // 准备测试数据
        TMpdAlertRule sapRule = new TMpdAlertRule();
        sapRule.setId(2);
        sapRule.setRuleName("SAP接口成功率告警");
        sapRule.setRuleType("SAP_INTERFACE");
        sapRule.setMetricName("SAP_SUCCESS_RATE");
        sapRule.setThresholdValue(80.0);
        sapRule.setOperator("LT");
        sapRule.setAlertLevel("WARNING");
        sapRule.setCheckInterval(5);
        sapRule.setEnabled(true);
        
        List<TMpdAlertRule> sapRules = Arrays.asList(sapRule);
        
        SapInterfaceMetrics metrics = new SapInterfaceMetrics();
        metrics.setSuccessRate(70.0); // 低于阈值
        
        when(alertRuleRepository.findByRuleTypeAndEnabled("SAP_INTERFACE", true))
            .thenReturn(sapRules);
        when(sapInterfaceMonitorService.getInterfaceMetrics(eq("ALL"), any(LocalDateTime.class), any(LocalDateTime.class)))
            .thenReturn(metrics);
        when(alertNotificationRepository.save(any(TMpdAlertNotification.class)))
            .thenReturn(new TMpdAlertNotification());
        
        // 执行测试
        alertService.checkSapInterfaceAlerts();
        
        // 验证结果
        verify(alertRuleRepository, times(1)).findByRuleTypeAndEnabled("SAP_INTERFACE", true);
        verify(sapInterfaceMonitorService, times(1)).getInterfaceMetrics(eq("ALL"), any(LocalDateTime.class), any(LocalDateTime.class));
        verify(alertNotificationRepository, times(1)).save(any(TMpdAlertNotification.class));
    }
}