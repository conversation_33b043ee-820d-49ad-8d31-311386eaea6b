package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.consts.InventoryDifferenceConstants;
import com.hvisions.rawmaterial.dao.InventoryDifferenceDetailRepository;
import com.hvisions.rawmaterial.dao.InventoryDifferenceRepository;
import com.hvisions.rawmaterial.dto.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceQueryDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceStatisticsDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifference;
import com.hvisions.rawmaterial.mapper.InventoryDifferenceMapper;
import com.hvisions.rawmaterial.service.impl.InventoryDifferenceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 库存差异处理服务统计功能测试类
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class InventoryDifferenceServiceStatisticsTest {

    @Mock
    private InventoryDifferenceRepository inventoryDifferenceRepository;

    @Mock
    private InventoryDifferenceDetailRepository inventoryDifferenceDetailRepository;

    @Mock
    private InventoryDifferenceMapper inventoryDifferenceMapper;

    @InjectMocks
    private InventoryDifferenceServiceImpl inventoryDifferenceService;

    private List<TMpdInventoryDifference> mockPendingDifferences;
    private List<TMpdInventoryDifference> mockProcessedDifferences;

    @BeforeEach
    void setUp() {
        // 准备待处理差异测试数据
        mockPendingDifferences = createMockPendingDifferences();
        
        // 准备已处理差异测试数据
        mockProcessedDifferences = createMockProcessedDifferences();
    }

    @Test
    void testGetDifferenceStatistics() {
        // Given
        when(inventoryDifferenceRepository.findByStatus(InventoryDifferenceConstants.STATUS_PENDING))
                .thenReturn(mockPendingDifferences);
        when(inventoryDifferenceRepository.findAll())
                .thenReturn(mockProcessedDifferences);

        // When
        InventoryDifferenceStatisticsDTO statistics = inventoryDifferenceService.getDifferenceStatistics();

        // Then
        assertNotNull(statistics);
        assertEquals(3, statistics.getPendingCount());
        assertEquals(new BigDecimal("150.000"), statistics.getPendingTotalDifference());
        assertEquals(2, statistics.getProcessedCount());
        
        // 验证部门统计
        assertNotNull(statistics.getDepartmentStatistics());
        assertTrue(statistics.getDepartmentStatistics().containsKey("原辅料管理部"));
        
        // 验证物料类型统计
        assertNotNull(statistics.getMaterialTypeStatistics());
        assertTrue(statistics.getMaterialTypeStatistics().containsKey("原料"));
    }

    @Test
    void testGetPendingDifferenceStatistics() {
        // Given
        Map<String, Object> mockBasicStats = createMockBasicStats();
        List<Map<String, Object>> mockDepartmentStats = createMockDepartmentStats();
        List<Map<String, Object>> mockMaterialTypeStats = createMockMaterialTypeStats();
        
        when(inventoryDifferenceMapper.getPendingStatistics()).thenReturn(mockBasicStats);
        when(inventoryDifferenceMapper.getPendingStatisticsByDepartment()).thenReturn(mockDepartmentStats);
        when(inventoryDifferenceMapper.getPendingStatisticsByMaterialType()).thenReturn(mockMaterialTypeStats);

        // When
        Map<String, Object> statistics = inventoryDifferenceService.getPendingDifferenceStatistics();

        // Then
        assertNotNull(statistics);
        assertEquals(3L, statistics.get("total_count"));
        assertEquals(new BigDecimal("150.000"), statistics.get("total_difference_quantity"));
        assertEquals(2L, statistics.get("surplus_count"));
        assertEquals(1L, statistics.get("deficit_count"));
        
        // 验证部门统计
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> departmentStats = (List<Map<String, Object>>) statistics.get("departmentStatistics");
        assertNotNull(departmentStats);
        assertEquals(1, departmentStats.size());
        assertEquals("原辅料管理部", departmentStats.get(0).get("department"));
        
        // 验证物料类型统计
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> materialTypeStats = (List<Map<String, Object>>) statistics.get("materialTypeStatistics");
        assertNotNull(materialTypeStats);
        assertEquals(2, materialTypeStats.size());
    }

    @Test
    void testGetProcessedDifferenceStatistics() {
        // Given
        Integer days = 30;
        Map<String, Object> mockBasicStats = createMockProcessedBasicStats();
        List<Map<String, Object>> mockProcessorStats = createMockProcessorStats();
        List<Map<String, Object>> mockDateStats = createMockDateStats();
        
        when(inventoryDifferenceMapper.getProcessedStatistics(any(Date.class), any(Date.class)))
                .thenReturn(mockBasicStats);
        when(inventoryDifferenceMapper.getProcessedStatisticsByProcessor(any(Date.class), any(Date.class)))
                .thenReturn(mockProcessorStats);
        when(inventoryDifferenceMapper.getProcessedStatisticsByDate(any(Date.class), any(Date.class)))
                .thenReturn(mockDateStats);

        // When
        Map<String, Object> statistics = inventoryDifferenceService.getProcessedDifferenceStatistics(days);

        // Then
        assertNotNull(statistics);
        assertEquals(2L, statistics.get("total_count"));
        assertEquals(new BigDecimal("80.000"), statistics.get("total_difference_quantity"));
        assertEquals(days, statistics.get("statisticsDays"));
        assertNotNull(statistics.get("startDate"));
        assertNotNull(statistics.get("endDate"));
        
        // 验证处理人统计
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> processorStats = (List<Map<String, Object>>) statistics.get("processorStatistics");
        assertNotNull(processorStats);
        assertEquals(2, processorStats.size());
        
        // 验证日期统计
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> dateStats = (List<Map<String, Object>>) statistics.get("dateStatistics");
        assertNotNull(dateStats);
        assertEquals(2, dateStats.size());
    }

    @Test
    void testQueryHistoryDifferences() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("RM001");
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockHistoryData = createMockHistoryData();
        Page<InventoryDifferenceDTO> mockPage = new PageImpl<>(mockHistoryData);
        
        when(inventoryDifferenceMapper.queryInventoryDifferences(any(InventoryDifferenceQueryDTO.class)))
                .thenReturn(mockHistoryData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryHistoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        
        // 验证查询条件中状态被设置为已处理
        verify(inventoryDifferenceMapper).queryInventoryDifferences(argThat(dto -> 
            dto.getStatus().equals(InventoryDifferenceConstants.STATUS_PROCESSED)
        ));
    }

    @Test
    void testQueryInventoryDifferencesWithFilters() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("RM001");
        queryDTO.setMaterialName("原料A");
        queryDTO.setErpWarehouseCode("WH001");
        queryDTO.setDepartment("原辅料管理部");
        queryDTO.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        List<InventoryDifferenceDTO> mockData = createMockQueryData();
        
        when(inventoryDifferenceMapper.queryInventoryDifferences(queryDTO))
                .thenReturn(mockData);

        // When
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        
        InventoryDifferenceDTO dto = result.getContent().get(0);
        assertEquals("RM001", dto.getMaterialCode());
        assertEquals("原料A", dto.getMaterialName());
        assertEquals("WH001", dto.getErpWarehouseCode());
        assertEquals("原辅料管理部", dto.getDepartment());
        assertEquals(InventoryDifferenceConstants.STATUS_PENDING, dto.getStatus());
        
        verify(inventoryDifferenceMapper).queryInventoryDifferences(queryDTO);
    }

    @Test
    void testGetPendingDifferences() {
        // Given
        List<InventoryDifferenceDTO> mockPendingData = createMockPendingData();
        when(inventoryDifferenceMapper.getPendingDifferences()).thenReturn(mockPendingData);

        // When
        List<InventoryDifferenceDTO> result = inventoryDifferenceService.getPendingDifferences();

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证所有记录都是待处理状态
        result.forEach(dto -> 
            assertEquals(InventoryDifferenceConstants.STATUS_PENDING, dto.getStatus())
        );
        
        verify(inventoryDifferenceMapper).getPendingDifferences();
    }

    @Test
    void testExportDifferences() {
        // Given
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("RM");
        
        List<InventoryDifferenceDTO> mockExportData = createMockExportData();
        when(inventoryDifferenceMapper.queryInventoryDifferences(any(InventoryDifferenceQueryDTO.class)))
                .thenReturn(mockExportData);

        // When
        List<InventoryDifferenceDTO> result = inventoryDifferenceService.exportDifferences(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // 验证导出时设置了大的页面大小
        verify(inventoryDifferenceMapper).queryInventoryDifferences(argThat(dto -> 
            dto.getPage() == 1 && dto.getPageSize() == 10000
        ));
    }

    @Test
    void testGetPendingDifferenceStatisticsException() {
        // Given
        when(inventoryDifferenceMapper.getPendingStatistics())
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> 
            inventoryDifferenceService.getPendingDifferenceStatistics()
        );
        
        assertTrue(exception.getMessage().contains("获取待处理差异统计信息失败"));
    }

    @Test
    void testGetProcessedDifferenceStatisticsException() {
        // Given
        when(inventoryDifferenceMapper.getProcessedStatistics(any(Date.class), any(Date.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> 
            inventoryDifferenceService.getProcessedDifferenceStatistics(30)
        );
        
        assertTrue(exception.getMessage().contains("获取已处理差异统计信息失败"));
    }

    // Helper methods to create mock data
    private List<TMpdInventoryDifference> createMockPendingDifferences() {
        List<TMpdInventoryDifference> differences = new ArrayList<>();
        
        TMpdInventoryDifference diff1 = new TMpdInventoryDifference();
        diff1.setId(1);
        diff1.setMaterialCode("RM001");
        diff1.setMaterialName("原料A");
        diff1.setDepartment("原辅料管理部");
        diff1.setDifferenceQuantity(new BigDecimal("100.000"));
        diff1.setMesCurrentStock(new BigDecimal("1000.000"));
        diff1.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        diff1.setCreateTime(new Date());
        differences.add(diff1);
        
        TMpdInventoryDifference diff2 = new TMpdInventoryDifference();
        diff2.setId(2);
        diff2.setMaterialCode("PM001");
        diff2.setMaterialName("包装材料B");
        diff2.setDepartment("原辅料管理部");
        diff2.setDifferenceQuantity(new BigDecimal("50.000"));
        diff2.setMesCurrentStock(new BigDecimal("500.000"));
        diff2.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        diff2.setCreateTime(new Date());
        differences.add(diff2);
        
        TMpdInventoryDifference diff3 = new TMpdInventoryDifference();
        diff3.setId(3);
        diff3.setMaterialCode("AM001");
        diff3.setMaterialName("辅助材料C");
        diff3.setDepartment("原辅料管理部");
        diff3.setDifferenceQuantity(new BigDecimal("-50.000"));
        diff3.setMesCurrentStock(new BigDecimal("200.000"));
        diff3.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        diff3.setCreateTime(new Date());
        differences.add(diff3);
        
        return differences;
    }
    
    private List<TMpdInventoryDifference> createMockProcessedDifferences() {
        List<TMpdInventoryDifference> differences = new ArrayList<>();
        
        TMpdInventoryDifference diff1 = new TMpdInventoryDifference();
        diff1.setId(4);
        diff1.setMaterialCode("RM002");
        diff1.setMaterialName("原料D");
        diff1.setDepartment("原辅料管理部");
        diff1.setDifferenceQuantity(new BigDecimal("30.000"));
        diff1.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        diff1.setProcessorName("张三");
        diff1.setCreateTime(new Date(System.currentTimeMillis() - 86400000)); // 1 day ago
        diff1.setProcessTime(new Date());
        differences.add(diff1);
        
        TMpdInventoryDifference diff2 = new TMpdInventoryDifference();
        diff2.setId(5);
        diff2.setMaterialCode("PM002");
        diff2.setMaterialName("包装材料E");
        diff2.setDepartment("原辅料管理部");
        diff2.setDifferenceQuantity(new BigDecimal("50.000"));
        diff2.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        diff2.setProcessorName("李四");
        diff2.setCreateTime(new Date(System.currentTimeMillis() - 172800000)); // 2 days ago
        diff2.setProcessTime(new Date());
        differences.add(diff2);
        
        return differences;
    }
    
    private Map<String, Object> createMockBasicStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total_count", 3L);
        stats.put("total_difference_quantity", new BigDecimal("150.000"));
        stats.put("surplus_count", 2L);
        stats.put("deficit_count", 1L);
        stats.put("total_surplus_quantity", new BigDecimal("200.000"));
        stats.put("total_deficit_quantity", new BigDecimal("50.000"));
        stats.put("average_difference_quantity", new BigDecimal("50.000"));
        return stats;
    }
    
    private List<Map<String, Object>> createMockDepartmentStats() {
        List<Map<String, Object>> stats = new ArrayList<>();
        Map<String, Object> dept1 = new HashMap<>();
        dept1.put("department", "原辅料管理部");
        dept1.put("count", 3L);
        dept1.put("total_difference_quantity", new BigDecimal("150.000"));
        stats.add(dept1);
        return stats;
    }
    
    private List<Map<String, Object>> createMockMaterialTypeStats() {
        List<Map<String, Object>> stats = new ArrayList<>();
        
        Map<String, Object> type1 = new HashMap<>();
        type1.put("material_type", "原料");
        type1.put("count", 2L);
        type1.put("total_difference_quantity", new BigDecimal("100.000"));
        stats.add(type1);
        
        Map<String, Object> type2 = new HashMap<>();
        type2.put("material_type", "包装材料");
        type2.put("count", 1L);
        type2.put("total_difference_quantity", new BigDecimal("50.000"));
        stats.add(type2);
        
        return stats;
    }
    
    private Map<String, Object> createMockProcessedBasicStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total_count", 2L);
        stats.put("total_difference_quantity", new BigDecimal("80.000"));
        stats.put("surplus_count", 2L);
        stats.put("deficit_count", 0L);
        stats.put("total_surplus_quantity", new BigDecimal("80.000"));
        stats.put("total_deficit_quantity", new BigDecimal("0.000"));
        stats.put("average_difference_quantity", new BigDecimal("40.000"));
        stats.put("average_processing_time_hours", new BigDecimal("24.00"));
        return stats;
    }
    
    private List<Map<String, Object>> createMockProcessorStats() {
        List<Map<String, Object>> stats = new ArrayList<>();
        
        Map<String, Object> processor1 = new HashMap<>();
        processor1.put("processor_name", "张三");
        processor1.put("count", 1L);
        processor1.put("total_difference_quantity", new BigDecimal("30.000"));
        processor1.put("average_processing_time_hours", new BigDecimal("24.00"));
        stats.add(processor1);
        
        Map<String, Object> processor2 = new HashMap<>();
        processor2.put("processor_name", "李四");
        processor2.put("count", 1L);
        processor2.put("total_difference_quantity", new BigDecimal("50.000"));
        processor2.put("average_processing_time_hours", new BigDecimal("48.00"));
        stats.add(processor2);
        
        return stats;
    }
    
    private List<Map<String, Object>> createMockDateStats() {
        List<Map<String, Object>> stats = new ArrayList<>();
        
        Map<String, Object> date1 = new HashMap<>();
        date1.put("process_date", "2025-07-28");
        date1.put("count", 1L);
        date1.put("total_difference_quantity", new BigDecimal("30.000"));
        stats.add(date1);
        
        Map<String, Object> date2 = new HashMap<>();
        date2.put("process_date", "2025-07-27");
        date2.put("count", 1L);
        date2.put("total_difference_quantity", new BigDecimal("50.000"));
        stats.add(date2);
        
        return stats;
    }
    
    private List<InventoryDifferenceDTO> createMockHistoryData() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto1 = new InventoryDifferenceDTO();
        dto1.setId(1);
        dto1.setMaterialCode("RM001");
        dto1.setMaterialName("原料A");
        dto1.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        dto1.setProcessorName("张三");
        data.add(dto1);
        
        InventoryDifferenceDTO dto2 = new InventoryDifferenceDTO();
        dto2.setId(2);
        dto2.setMaterialCode("RM002");
        dto2.setMaterialName("原料B");
        dto2.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        dto2.setProcessorName("李四");
        data.add(dto2);
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockQueryData() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
        dto.setId(1);
        dto.setMaterialCode("RM001");
        dto.setMaterialName("原料A");
        dto.setErpWarehouseCode("WH001");
        dto.setErpWarehouseName("仓库A");
        dto.setDepartment("原辅料管理部");
        dto.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
        dto.setDifferenceQuantity(new BigDecimal("100.000"));
        data.add(dto);
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockPendingData() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
            dto.setId(i);
            dto.setMaterialCode("RM00" + i);
            dto.setMaterialName("原料" + i);
            dto.setStatus(InventoryDifferenceConstants.STATUS_PENDING);
            dto.setDifferenceQuantity(new BigDecimal(i * 50 + ".000"));
            data.add(dto);
        }
        
        return data;
    }
    
    private List<InventoryDifferenceDTO> createMockExportData() {
        List<InventoryDifferenceDTO> data = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            InventoryDifferenceDTO dto = new InventoryDifferenceDTO();
            dto.setId(i);
            dto.setMaterialCode("RM00" + i);
            dto.setMaterialName("原料" + i);
            dto.setStatus(i % 2 == 0 ? InventoryDifferenceConstants.STATUS_PROCESSED : InventoryDifferenceConstants.STATUS_PENDING);
            dto.setDifferenceQuantity(new BigDecimal(i * 20 + ".000"));
            data.add(dto);
        }
        
        return data;
    }
}