package com.hvisions.rawmaterial.security.service;

import com.hvisions.rawmaterial.dao.log.DataAccessLogRepository;
import com.hvisions.rawmaterial.dao.log.OperationLogRepository;
import com.hvisions.rawmaterial.entity.log.TMpdDataAccessLog;
import com.hvisions.rawmaterial.entity.log.TMpdOperationLog;
import com.hvisions.rawmaterial.security.dto.UserSecurityContext;
import com.hvisions.rawmaterial.security.service.impl.AuditLogServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 审计日志服务单元测试
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class AuditLogServiceTest {
    
    @Mock
    private OperationLogRepository operationLogRepository;
    
    @Mock
    private DataAccessLogRepository dataAccessLogRepository;
    
    @Mock
    private SecurityService securityService;
    
    @Mock
    private HttpServletRequest request;
    
    @InjectMocks
    private AuditLogServiceImpl auditLogService;
    
    private UserSecurityContext mockUserContext;
    
    @BeforeEach
    void setUp() {
        mockUserContext = UserSecurityContext.builder()
            .userId(1)
            .username("testuser")
            .realName("测试用户")
            .departmentId(1)
            .departmentName("测试部门")
            .build();
        
        // 设置HTTP请求模拟
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        when(request.getHeader("User-Agent")).thenReturn("Test-Agent");
        when(request.getRequestURL()).thenReturn(new StringBuffer("http://localhost/test"));
        when(request.getMethod()).thenReturn("POST");
    }
    
    @Test
    void testLogOperation_Success() {
        // 准备测试数据
        when(securityService.getUserContext(1)).thenReturn(mockUserContext);
        
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        
        // 执行测试
        auditLogService.logOperation("库存差异处理", "处理差异", "PROCESS", 
            1, "testuser", params, result, null);
        
        // 验证结果
        ArgumentCaptor<TMpdOperationLog> logCaptor = ArgumentCaptor.forClass(TMpdOperationLog.class);
        verify(operationLogRepository).save(logCaptor.capture());
        
        TMpdOperationLog savedLog = logCaptor.getValue();
        assertEquals("库存差异处理", savedLog.getModule());
        assertEquals("处理差异", savedLog.getOperation());
        assertEquals("PROCESS", savedLog.getOperationType());
        assertEquals(1, savedLog.getUserId());
        assertEquals("testuser", savedLog.getUsername());
        assertEquals("测试用户", savedLog.getRealName());
        assertEquals(1, savedLog.getDepartmentId());
        assertEquals("测试部门", savedLog.getDepartmentName());
        assertEquals(1, savedLog.getStatus()); // 成功状态
        assertNotNull(savedLog.getRequestParams());
        assertNotNull(savedLog.getResponseResult());
        assertNull(savedLog.getErrorMessage());
    }
    
    @Test
    void testLogOperation_Failure() {
        // 准备测试数据
        when(securityService.getUserContext(1)).thenReturn(mockUserContext);
        
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");
        
        // 执行测试
        auditLogService.logOperation("库存差异处理", "处理差异", "PROCESS", 
            1, "testuser", params, null, "处理失败");
        
        // 验证结果
        ArgumentCaptor<TMpdOperationLog> logCaptor = ArgumentCaptor.forClass(TMpdOperationLog.class);
        verify(operationLogRepository).save(logCaptor.capture());
        
        TMpdOperationLog savedLog = logCaptor.getValue();
        assertEquals(0, savedLog.getStatus()); // 失败状态
        assertEquals("处理失败", savedLog.getErrorMessage());
        assertNull(savedLog.getResponseResult());
    }
    
    @Test
    void testLogSuccess() {
        // 准备测试数据
        when(securityService.getUserContext(1)).thenReturn(mockUserContext);
        
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        
        // 执行测试
        auditLogService.logSuccess("库存差异处理", "查询差异", "QUERY", 
            1, "testuser", params, result);
        
        // 验证结果
        ArgumentCaptor<TMpdOperationLog> logCaptor = ArgumentCaptor.forClass(TMpdOperationLog.class);
        verify(operationLogRepository).save(logCaptor.capture());
        
        TMpdOperationLog savedLog = logCaptor.getValue();
        assertEquals(1, savedLog.getStatus()); // 成功状态
        assertNull(savedLog.getErrorMessage());
        assertNotNull(savedLog.getResponseResult());
    }
    
    @Test
    void testLogFailure() {
        // 准备测试数据
        when(securityService.getUserContext(1)).thenReturn(mockUserContext);
        
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");
        
        // 执行测试
        auditLogService.logFailure("库存差异处理", "删除差异", "DELETE", 
            1, "testuser", params, "权限不足");
        
        // 验证结果
        ArgumentCaptor<TMpdOperationLog> logCaptor = ArgumentCaptor.forClass(TMpdOperationLog.class);
        verify(operationLogRepository).save(logCaptor.capture());
        
        TMpdOperationLog savedLog = logCaptor.getValue();
        assertEquals(0, savedLog.getStatus()); // 失败状态
        assertEquals("权限不足", savedLog.getErrorMessage());
        assertNull(savedLog.getResponseResult());
    }
    
    @Test
    void testLogDataAccess() {
        // 准备测试数据
        when(securityService.getUserContext(1)).thenReturn(mockUserContext);
        
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("status", 0);
        
        Map<String, Object> newData = new HashMap<>();
        newData.put("status", 1);
        
        // 执行测试
        auditLogService.logDataAccess("t_mpd_inventory_difference", "UPDATE", 
            1, "testuser", "123", oldData, newData);
        
        // 验证结果
        ArgumentCaptor<TMpdDataAccessLog> logCaptor = ArgumentCaptor.forClass(TMpdDataAccessLog.class);
        verify(dataAccessLogRepository).save(logCaptor.capture());
        
        TMpdDataAccessLog savedLog = logCaptor.getValue();
        assertEquals("t_mpd_inventory_difference", savedLog.getTableName());
        assertEquals("UPDATE", savedLog.getOperationType());
        assertEquals("123", savedLog.getDataId());
        assertEquals(1, savedLog.getUserId());
        assertEquals("testuser", savedLog.getUsername());
        assertEquals("测试用户", savedLog.getRealName());
        assertNotNull(savedLog.getOldData());
        assertNotNull(savedLog.getNewData());
    }
    
    @Test
    void testQueryOperationLogs_ByUserId() {
        // 准备测试数据
        List<TMpdOperationLog> mockLogs = Arrays.asList(
            TMpdOperationLog.builder().userId(1).operation("test1").build(),
            TMpdOperationLog.builder().userId(1).operation("test2").build()
        );
        when(operationLogRepository.findByUserIdOrderByOperationTimeDesc(1)).thenReturn(mockLogs);
        
        Map<String, Object> params = new HashMap<>();
        params.put("userId", 1);
        
        // 执行测试
        List<TMpdOperationLog> result = auditLogService.queryOperationLogs(params);
        
        // 验证结果
        assertEquals(2, result.size());
        verify(operationLogRepository).findByUserIdOrderByOperationTimeDesc(1);
    }
    
    @Test
    void testQueryOperationLogs_ByModule() {
        // 准备测试数据
        List<TMpdOperationLog> mockLogs = Arrays.asList(
            TMpdOperationLog.builder().module("库存差异处理").operation("test1").build()
        );
        when(operationLogRepository.findByModuleOrderByOperationTimeDesc("库存差异处理")).thenReturn(mockLogs);
        
        Map<String, Object> params = new HashMap<>();
        params.put("module", "库存差异处理");
        
        // 执行测试
        List<TMpdOperationLog> result = auditLogService.queryOperationLogs(params);
        
        // 验证结果
        assertEquals(1, result.size());
        verify(operationLogRepository).findByModuleOrderByOperationTimeDesc("库存差异处理");
    }
    
    @Test
    void testGetUserOperationStatistics() {
        // 准备测试数据
        when(operationLogRepository.countByUserIdAndOperationTimeAfter(eq(1), any(Date.class))).thenReturn(100L);
        when(operationLogRepository.countSuccessOperationsByUserIdAndOperationTimeAfter(eq(1), any(Date.class))).thenReturn(90L);
        when(operationLogRepository.countFailedOperationsByUserIdAndOperationTimeAfter(eq(1), any(Date.class))).thenReturn(10L);
        
        List<Object[]> operationTypeStats = Arrays.asList(
            new Object[]{"QUERY", 50L},
            new Object[]{"PROCESS", 30L},
            new Object[]{"DELETE", 20L}
        );
        when(operationLogRepository.getOperationTypeStatisticsByUserId(eq(1), any(Date.class))).thenReturn(operationTypeStats);
        
        // 执行测试
        Map<String, Object> statistics = auditLogService.getUserOperationStatistics(1, 7);
        
        // 验证结果
        assertEquals(100L, statistics.get("totalCount"));
        assertEquals(90L, statistics.get("successCount"));
        assertEquals(10L, statistics.get("failedCount"));
        assertEquals(90.0, statistics.get("successRate"));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> operationTypeMap = (Map<String, Long>) statistics.get("operationTypeStatistics");
        assertEquals(50L, operationTypeMap.get("QUERY"));
        assertEquals(30L, operationTypeMap.get("PROCESS"));
        assertEquals(20L, operationTypeMap.get("DELETE"));
    }
    
    @Test
    void testGetModuleOperationStatistics() {
        // 准备测试数据
        when(operationLogRepository.countByModuleAndOperationTimeAfter(eq("库存差异处理"), any(Date.class))).thenReturn(200L);
        
        List<Object[]> operationTypeStats = Arrays.asList(
            new Object[]{"QUERY", 100L},
            new Object[]{"PROCESS", 80L},
            new Object[]{"SYNC", 20L}
        );
        when(operationLogRepository.getOperationTypeStatisticsByModule(eq("库存差异处理"), any(Date.class))).thenReturn(operationTypeStats);
        
        // 执行测试
        Map<String, Object> statistics = auditLogService.getModuleOperationStatistics("库存差异处理", 30);
        
        // 验证结果
        assertEquals(200L, statistics.get("totalCount"));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> operationTypeMap = (Map<String, Long>) statistics.get("operationTypeStatistics");
        assertEquals(100L, operationTypeMap.get("QUERY"));
        assertEquals(80L, operationTypeMap.get("PROCESS"));
        assertEquals(20L, operationTypeMap.get("SYNC"));
    }
    
    @Test
    void testCleanExpiredLogs() {
        // 准备测试数据
        when(operationLogRepository.deleteByOperationTimeBefore(any(Date.class))).thenReturn(50);
        when(dataAccessLogRepository.deleteByAccessTimeBefore(any(Date.class))).thenReturn(30);
        
        // 执行测试
        int deletedCount = auditLogService.cleanExpiredLogs(90);
        
        // 验证结果
        assertEquals(80, deletedCount);
        verify(operationLogRepository).deleteByOperationTimeBefore(any(Date.class));
        verify(dataAccessLogRepository).deleteByAccessTimeBefore(any(Date.class));
    }
    
    @Test
    void testLogOperation_WithException() {
        // 准备测试数据 - 模拟异常情况
        when(securityService.getUserContext(1)).thenThrow(new RuntimeException("Test exception"));
        
        // 执行测试 - 应该不抛出异常，而是记录错误日志
        assertDoesNotThrow(() -> {
            auditLogService.logOperation("库存差异处理", "处理差异", "PROCESS", 
                1, "testuser", null, null, null);
        });
        
        // 验证没有保存日志（因为异常被捕获）
        verify(operationLogRepository, never()).save(any(TMpdOperationLog.class));
    }
    
    @Test
    void testLogDataAccess_WithException() {
        // 准备测试数据 - 模拟异常情况
        when(securityService.getUserContext(1)).thenThrow(new RuntimeException("Test exception"));
        
        // 执行测试 - 应该不抛出异常，而是记录错误日志
        assertDoesNotThrow(() -> {
            auditLogService.logDataAccess("test_table", "UPDATE", 1, "testuser", "123", null, null);
        });
        
        // 验证没有保存日志（因为异常被捕获）
        verify(dataAccessLogRepository, never()).save(any(TMpdDataAccessLog.class));
    }
}