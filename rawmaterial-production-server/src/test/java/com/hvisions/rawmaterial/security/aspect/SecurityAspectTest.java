package com.hvisions.rawmaterial.security.aspect;

import com.hvisions.rawmaterial.security.annotation.AuditLog;
import com.hvisions.rawmaterial.security.annotation.RequirePermission;
import com.hvisions.rawmaterial.security.annotation.RequireRole;
import com.hvisions.rawmaterial.security.annotation.SensitiveOperation;
import com.hvisions.rawmaterial.security.dto.UserSecurityContext;
import com.hvisions.rawmaterial.security.exception.SecurityException;
import com.hvisions.rawmaterial.security.service.AuditLogService;
import com.hvisions.rawmaterial.security.service.SecurityService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 安全切面单元测试
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class SecurityAspectTest {
    
    @Mock
    private SecurityService securityService;
    
    @Mock
    private AuditLogService auditLogService;
    
    @Mock
    private JoinPoint joinPoint;
    
    @Mock
    private ProceedingJoinPoint proceedingJoinPoint;
    
    @Mock
    private Signature signature;
    
    @InjectMocks
    private SecurityAspect securityAspect;
    
    private UserSecurityContext mockUserContext;
    
    @BeforeEach
    void setUp() {
        Set<String> roles = new HashSet<>();
        roles.add("inventory_difference_admin");
        roles.add("inventory_difference_processor");
        
        Set<String> permissions = new HashSet<>();
        permissions.add("inventory_difference:view");
        permissions.add("inventory_difference:process");
        permissions.add("inventory_difference:admin");
        
        mockUserContext = UserSecurityContext.builder()
            .userId(1)
            .username("testuser")
            .realName("测试用户")
            .roles(roles)
            .permissions(permissions)
            .isAdmin(true)
            .build();
        
        when(joinPoint.getSignature()).thenReturn(signature);
        when(signature.getName()).thenReturn("testMethod");
    }
    
    @Test
    void testCheckPermission_WithPermission() throws NoSuchMethodException {
        // 准备测试数据
        when(securityService.getCurrentUserContext()).thenReturn(mockUserContext);
        
        // 模拟方法和注解
        TestController testController = new TestController();
        Method method = TestController.class.getMethod("methodWithPermission");
        when(joinPoint.getTarget()).thenReturn(testController);
        when(joinPoint.getArgs()).thenReturn(new Object[0]);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> securityAspect.checkPermission(joinPoint));
    }
    
    @Test
    void testCheckPermission_WithoutPermission() throws NoSuchMethodException {
        // 准备测试数据 - 用户没有所需权限
        Set<String> limitedPermissions = new HashSet<>();
        limitedPermissions.add("inventory_difference:view");
        
        UserSecurityContext limitedUserContext = UserSecurityContext.builder()
            .userId(2)
            .username("limiteduser")
            .permissions(limitedPermissions)
            .build();
        
        when(securityService.getCurrentUserContext()).thenReturn(limitedUserContext);
        
        // 模拟方法和注解
        TestController testController = new TestController();
        Method method = TestController.class.getMethod("methodWithPermission");
        when(joinPoint.getTarget()).thenReturn(testController);
        when(joinPoint.getArgs()).thenReturn(new Object[0]);
        
        // 执行测试 - 应该抛出SecurityException
        SecurityException exception = assertThrows(SecurityException.class, 
            () -> securityAspect.checkPermission(joinPoint));
        
        assertEquals("PERMISSION_DENIED", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("权限不足"));
    }
    
    @Test
    void testCheckPermission_NoUser() throws NoSuchMethodException {
        // 准备测试数据 - 没有用户上下文
        when(securityService.getCurrentUserContext()).thenReturn(null);
        
        // 模拟方法和注解
        TestController testController = new TestController();
        when(joinPoint.getTarget()).thenReturn(testController);
        when(joinPoint.getArgs()).thenReturn(new Object[0]);
        
        // 执行测试 - 应该抛出SecurityException
        SecurityException exception = assertThrows(SecurityException.class, 
            () -> securityAspect.checkPermission(joinPoint));
        
        assertEquals("AUTHENTICATION_REQUIRED", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("用户未认证"));
    }
    
    @Test
    void testCheckRole_WithRole() throws NoSuchMethodException {
        // 准备测试数据
        when(securityService.getCurrentUserContext()).thenReturn(mockUserContext);
        
        // 模拟方法和注解
        TestController testController = new TestController();
        Method method = TestController.class.getMethod("methodWithRole");
        when(joinPoint.getTarget()).thenReturn(testController);
        when(joinPoint.getArgs()).thenReturn(new Object[0]);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> securityAspect.checkRole(joinPoint));
    }
    
    @Test
    void testCheckRole_WithoutRole() throws NoSuchMethodException {
        // 准备测试数据 - 用户没有所需角色
        Set<String> limitedRoles = new HashSet<>();
        limitedRoles.add("inventory_difference_viewer");
        
        UserSecurityContext limitedUserContext = UserSecurityContext.builder()
            .userId(2)
            .username("limiteduser")
            .roles(limitedRoles)
            .build();
        
        when(securityService.getCurrentUserContext()).thenReturn(limitedUserContext);
        
        // 模拟方法和注解
        TestController testController = new TestController();
        Method method = TestController.class.getMethod("methodWithRole");
        when(joinPoint.getTarget()).thenReturn(testController);
        when(joinPoint.getArgs()).thenReturn(new Object[0]);
        
        // 执行测试 - 应该抛出SecurityException
        SecurityException exception = assertThrows(SecurityException.class, 
            () -> securityAspect.checkRole(joinPoint));
        
        assertEquals("ROLE_DENIED", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("角色权限不足"));
    }
    
    @Test
    void testHandleSensitiveOperation() throws Throwable {
        // 准备测试数据
        when(securityService.getCurrentUserContext()).thenReturn(mockUserContext);
        when(proceedingJoinPoint.getTarget()).thenReturn(new TestController());
        when(proceedingJoinPoint.getSignature()).thenReturn(signature);
        when(proceedingJoinPoint.getArgs()).thenReturn(new Object[0]);
        when(proceedingJoinPoint.proceed()).thenReturn("success");
        
        // 执行测试
        Object result = securityAspect.handleSensitiveOperation(proceedingJoinPoint);
        
        // 验证结果
        assertEquals("success", result);
        verify(proceedingJoinPoint).proceed();
    }
    
    @Test
    void testHandleAuditLog_Success() throws Throwable {
        // 准备测试数据
        when(securityService.getCurrentUserContext()).thenReturn(mockUserContext);
        when(proceedingJoinPoint.getTarget()).thenReturn(new TestController());
        when(proceedingJoinPoint.getSignature()).thenReturn(signature);
        when(proceedingJoinPoint.getArgs()).thenReturn(new Object[]{"param1", "param2"});
        when(proceedingJoinPoint.proceed()).thenReturn("success");
        
        // 执行测试
        Object result = securityAspect.handleAuditLog(proceedingJoinPoint);
        
        // 验证结果
        assertEquals("success", result);
        verify(proceedingJoinPoint).proceed();
        verify(auditLogService).logOperation(
            eq("库存差异处理"),
            eq("testMethod"),
            eq("TEST"),
            eq(1),
            eq("testuser"),
            any(),
            isNull(),
            isNull()
        );
    }
    
    @Test
    void testHandleAuditLog_WithException() throws Throwable {
        // 准备测试数据
        RuntimeException testException = new RuntimeException("Test exception");
        when(securityService.getCurrentUserContext()).thenReturn(mockUserContext);
        when(proceedingJoinPoint.getTarget()).thenReturn(new TestController());
        when(proceedingJoinPoint.getSignature()).thenReturn(signature);
        when(proceedingJoinPoint.getArgs()).thenReturn(new Object[0]);
        when(proceedingJoinPoint.proceed()).thenThrow(testException);
        
        // 执行测试 - 应该重新抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> securityAspect.handleAuditLog(proceedingJoinPoint));
        
        // 验证结果
        assertEquals("Test exception", exception.getMessage());
        verify(proceedingJoinPoint).proceed();
        verify(auditLogService).logOperation(
            eq("库存差异处理"),
            eq("testMethod"),
            eq("TEST"),
            eq(1),
            eq("testuser"),
            any(),
            isNull(),
            eq("Test exception")
        );
    }
    
    // 测试用的控制器类
    public static class TestController {
        
        @RequirePermission("inventory_difference:process")
        public void methodWithPermission() {
            // 测试方法
        }
        
        @RequireRole("inventory_difference_admin")
        public void methodWithRole() {
            // 测试方法
        }
        
        @SensitiveOperation(value = "敏感操作测试", type = SensitiveOperation.OperationType.PROCESS)
        public String methodWithSensitiveOperation() {
            return "success";
        }
        
        @AuditLog(value = "审计日志测试", module = "库存差异处理", operationType = "TEST")
        public String methodWithAuditLog() {
            return "success";
        }
    }
}