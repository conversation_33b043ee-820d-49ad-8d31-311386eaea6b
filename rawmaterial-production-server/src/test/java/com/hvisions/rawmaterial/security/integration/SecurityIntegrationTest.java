package com.hvisions.rawmaterial.security.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.InventoryDifferenceQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 安全机制集成测试
 * 测试权限控制和审计日志在实际HTTP请求中的工作情况
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class SecurityIntegrationTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }
    
    @Test
    void testQueryWithValidPermission() throws Exception {
        // 准备测试数据
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        // 执行测试 - 使用管理员用户ID
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk());
    }
    
    @Test
    void testQueryWithoutPermission() throws Exception {
        // 准备测试数据
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        // 执行测试 - 使用没有权限的用户ID
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "999")
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isForbidden());
    }
    
    @Test
    void testQueryWithoutAuthentication() throws Exception {
        // 准备测试数据
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        // 执行测试 - 不提供用户ID
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isUnauthorized());
    }
    
    @Test
    void testSensitiveOperationWithValidRole() throws Exception {
        // 执行测试 - 使用有权限的用户ID
        mockMvc.perform(post("/api/rawmaterial/material-difference/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1"))
                .andExpect(status().isOk());
    }
    
    @Test
    void testSensitiveOperationWithoutRole() throws Exception {
        // 执行测试 - 使用没有角色权限的用户ID
        mockMvc.perform(post("/api/rawmaterial/material-difference/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "3"))
                .andExpect(status().isForbidden());
    }
    
    @Test
    void testDeleteWithAdminRole() throws Exception {
        // 执行测试 - 使用管理员用户ID
        mockMvc.perform(delete("/api/rawmaterial/material-difference/1")
                .header("User-Id", "1"))
                .andExpect(status().isOk());
    }
    
    @Test
    void testDeleteWithoutAdminRole() throws Exception {
        // 执行测试 - 使用普通用户ID
        mockMvc.perform(delete("/api/rawmaterial/material-difference/1")
                .header("User-Id", "2"))
                .andExpect(status().isForbidden());
    }
    
    @Test
    void testViewOperationLogsAudit() throws Exception {
        // 执行测试 - 查看操作会被记录到审计日志
        mockMvc.perform(get("/api/rawmaterial/material-difference/1")
                .header("User-Id", "1"))
                .andExpect(status().isOk());
        
        // 这里可以添加验证审计日志是否正确记录的逻辑
        // 由于是集成测试，可以查询数据库验证日志记录
    }
    
    @Test
    void testBatchProcessWithSupervisorRole() throws Exception {
        // 准备测试数据
        String batchProcessData = "{}"; // 简化的批量处理数据
        
        // 执行测试 - 使用主管用户ID
        mockMvc.perform(post("/api/rawmaterial/material-difference/batch-process")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(batchProcessData))
                .andExpect(status().isOk());
    }
    
    @Test
    void testStatisticsWithViewPermission() throws Exception {
        // 执行测试 - 查看统计信息
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics")
                .header("User-Id", "1"))
                .andExpect(status().isOk());
    }
    
    @Test
    void testExportWithPermission() throws Exception {
        // 准备测试数据
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        
        // 执行测试 - 导出数据
        mockMvc.perform(post("/api/rawmaterial/material-difference/export")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk());
    }
    
    @Test
    void testMultiplePermissionRequirement() throws Exception {
        // 测试需要多个权限的操作
        mockMvc.perform(post("/api/rawmaterial/material-difference/sync-sap-stock")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content("[]"))
                .andExpect(status().isOk());
    }
    
    @Test
    void testRoleHierarchy() throws Exception {
        // 测试角色层次结构 - 管理员应该能执行所有操作
        mockMvc.perform(get("/api/rawmaterial/material-difference/pending")
                .header("User-Id", "1"))
                .andExpect(status().isOk());
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1"))
                .andExpect(status().isOk());
    }
    
    @Test
    void testSecurityExceptionHandling() throws Exception {
        // 测试安全异常的处理
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "999")
                .content("{}"))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.errorCode").value("PERMISSION_DENIED"));
    }
}