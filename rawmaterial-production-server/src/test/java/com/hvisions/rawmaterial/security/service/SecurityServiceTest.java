package com.hvisions.rawmaterial.security.service;

import com.hvisions.auth.client.BaseUserClient;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.security.dto.UserSecurityContext;
import com.hvisions.rawmaterial.security.exception.SecurityException;
import com.hvisions.rawmaterial.security.service.impl.SecurityServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 安全服务单元测试
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class SecurityServiceTest {
    
    @Mock
    private HttpServletRequest request;
    
    @Mock
    private StringRedisTemplate stringRedisTemplate;
    
    @Mock
    private ValueOperations<String, String> valueOperations;
    
    @Mock
    private BaseUserClient baseUserClient;
    
    @InjectMocks
    private SecurityServiceImpl securityService;
    
    private UserBaseDTO mockUser;
    private ResultVO<UserBaseDTO> mockUserResult;
    
    @BeforeEach
    void setUp() {
        // 设置模拟用户数据
        mockUser = new UserBaseDTO();
        mockUser.setUserId(1);
        mockUser.setUserName("testuser");
        mockUser.setRealName("测试用户");
        mockUser.setDepartmentId(1);
        mockUser.setDepartmentName("测试部门");
        
        mockUserResult = new ResultVO<>();
        mockUserResult.setSuccess(true);
        mockUserResult.setData(mockUser);
        
        // 设置Redis模拟
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }
    
    @Test
    void testGetCurrentUserContext_WithUserIdHeader() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        UserSecurityContext context = securityService.getCurrentUserContext();
        
        // 验证结果
        assertNotNull(context);
        assertEquals(1, context.getUserId());
        assertEquals("testuser", context.getUsername());
        assertEquals("测试用户", context.getRealName());
        assertEquals(1, context.getDepartmentId());
        assertEquals("测试部门", context.getDepartmentName());
        assertNotNull(context.getRoles());
        assertNotNull(context.getPermissions());
    }
    
    @Test
    void testGetCurrentUserContext_WithToken() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn(null);
        when(request.getHeader("AUTH_TOKEN")).thenReturn("test-token");
        when(valueOperations.get("AUTH_REDIS_PREFIX:test-token")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        UserSecurityContext context = securityService.getCurrentUserContext();
        
        // 验证结果
        assertNotNull(context);
        assertEquals(1, context.getUserId());
    }
    
    @Test
    void testGetCurrentUserContext_NoUser() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn(null);
        when(request.getHeader("AUTH_TOKEN")).thenReturn(null);
        
        // 执行测试
        UserSecurityContext context = securityService.getCurrentUserContext();
        
        // 验证结果
        assertNull(context);
    }
    
    @Test
    void testGetUserContext_ValidUser() {
        // 准备测试数据
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        UserSecurityContext context = securityService.getUserContext(1);
        
        // 验证结果
        assertNotNull(context);
        assertEquals(1, context.getUserId());
        assertEquals("testuser", context.getUsername());
        assertTrue(context.getIsAdmin());
        assertTrue(context.hasRole("inventory_difference_admin"));
        assertTrue(context.hasPermission("inventory_difference:admin"));
    }
    
    @Test
    void testGetUserContext_InvalidUser() {
        // 准备测试数据
        ResultVO<UserBaseDTO> failedResult = new ResultVO<>();
        failedResult.setSuccess(false);
        when(baseUserClient.getUserByUserId(999)).thenReturn(failedResult);
        
        // 执行测试
        UserSecurityContext context = securityService.getUserContext(999);
        
        // 验证结果
        assertNull(context);
    }
    
    @Test
    void testHasPermission_WithPermission() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasPermission = securityService.hasPermission("inventory_difference:admin");
        
        // 验证结果
        assertTrue(hasPermission);
    }
    
    @Test
    void testHasPermission_WithoutPermission() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("3");
        when(baseUserClient.getUserByUserId(3)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasPermission = securityService.hasPermission("inventory_difference:admin");
        
        // 验证结果
        assertFalse(hasPermission);
    }
    
    @Test
    void testHasAnyPermission_WithOnePermission() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasPermission = securityService.hasAnyPermission(
            "inventory_difference:admin", "non_existent_permission");
        
        // 验证结果
        assertTrue(hasPermission);
    }
    
    @Test
    void testHasAllPermissions_WithAllPermissions() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasPermissions = securityService.hasAllPermissions(
            "inventory_difference:view", "inventory_difference:query");
        
        // 验证结果
        assertTrue(hasPermissions);
    }
    
    @Test
    void testHasAllPermissions_WithoutAllPermissions() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("3");
        when(baseUserClient.getUserByUserId(3)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasPermissions = securityService.hasAllPermissions(
            "inventory_difference:view", "inventory_difference:admin");
        
        // 验证结果
        assertFalse(hasPermissions);
    }
    
    @Test
    void testHasRole_WithRole() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasRole = securityService.hasRole("inventory_difference_admin");
        
        // 验证结果
        assertTrue(hasRole);
    }
    
    @Test
    void testHasRole_WithoutRole() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("3");
        when(baseUserClient.getUserByUserId(3)).thenReturn(mockUserResult);
        
        // 执行测试
        boolean hasRole = securityService.hasRole("inventory_difference_admin");
        
        // 验证结果
        assertFalse(hasRole);
    }
    
    @Test
    void testValidatePermission_WithPermission() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> securityService.validatePermission("inventory_difference:admin"));
    }
    
    @Test
    void testValidatePermission_WithoutPermission() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("3");
        when(baseUserClient.getUserByUserId(3)).thenReturn(mockUserResult);
        
        // 执行测试 - 应该抛出异常
        SecurityException exception = assertThrows(SecurityException.class, 
            () -> securityService.validatePermission("inventory_difference:admin"));
        
        assertTrue(exception.getMessage().contains("权限不足"));
    }
    
    @Test
    void testValidateRole_WithRole() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        when(baseUserClient.getUserByUserId(1)).thenReturn(mockUserResult);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> securityService.validateRole("inventory_difference_admin"));
    }
    
    @Test
    void testValidateRole_WithoutRole() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("3");
        when(baseUserClient.getUserByUserId(3)).thenReturn(mockUserResult);
        
        // 执行测试 - 应该抛出异常
        SecurityException exception = assertThrows(SecurityException.class, 
            () -> securityService.validateRole("inventory_difference_admin"));
        
        assertTrue(exception.getMessage().contains("角色权限不足"));
    }
    
    @Test
    void testGenerateOperationToken() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        
        // 执行测试
        String token = securityService.generateOperationToken("test_operation", "test_data");
        
        // 验证结果
        assertNotNull(token);
        assertFalse(token.isEmpty());
        verify(valueOperations).set(eq("operation_token:" + token), anyString(), eq(5L), any());
    }
    
    @Test
    void testValidateOperationToken_ValidToken() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        String tokenData = "{\"operation\":\"test\",\"confirmationCode\":\"123456\",\"userId\":1}";
        when(valueOperations.get("operation_token:test-token")).thenReturn(tokenData);
        
        // 执行测试
        boolean isValid = securityService.validateOperationToken("test-token", "123456");
        
        // 验证结果
        assertTrue(isValid);
    }
    
    @Test
    void testValidateOperationToken_InvalidToken() {
        // 准备测试数据
        when(valueOperations.get("operation_token:invalid-token")).thenReturn(null);
        
        // 执行测试
        boolean isValid = securityService.validateOperationToken("invalid-token", "123456");
        
        // 验证结果
        assertFalse(isValid);
    }
    
    @Test
    void testValidateOperationToken_WrongConfirmationCode() {
        // 准备测试数据
        when(request.getHeader("User-Id")).thenReturn("1");
        String tokenData = "{\"operation\":\"test\",\"confirmationCode\":\"123456\",\"userId\":1}";
        when(valueOperations.get("operation_token:test-token")).thenReturn(tokenData);
        
        // 执行测试
        boolean isValid = securityService.validateOperationToken("test-token", "654321");
        
        // 验证结果
        assertFalse(isValid);
    }
    
    @Test
    void testClearOperationToken() {
        // 执行测试
        securityService.clearOperationToken("test-token");
        
        // 验证结果
        verify(stringRedisTemplate).delete("operation_token:test-token");
    }
}