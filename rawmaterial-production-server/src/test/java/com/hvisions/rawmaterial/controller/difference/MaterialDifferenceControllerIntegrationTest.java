package com.hvisions.rawmaterial.controller.difference;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 原辅料库存盘点差异处理控制器集成测试
 * 测试完整的请求-响应流程，包括数据库交互
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("原辅料库存盘点差异处理控制器集成测试")
class MaterialDifferenceControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("完整业务流程测试 - 查询、处理、统计")
    void testCompleteBusinessFlow() throws Exception {
        // 1. 测试分页查询
        InventoryDifferenceQueryDTO queryDTO = createTestQueryDTO();
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        // 2. 测试生成差异记录
        mockMvc.perform(post("/api/rawmaterial/material-difference/generate"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());

        // 3. 测试查询待处理记录
        mockMvc.perform(get("/api/rawmaterial/material-difference/pending"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());

        // 4. 测试获取统计信息
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics"))
                .andDo(print())
                .andExpect(status().isOk());

        // 5. 测试获取待处理统计
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics/pending"))
                .andDo(print())
                .andExpect(status().isOk());

        // 6. 测试获取已处理统计
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics/processed")
                .param("days", "7"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("SAP同步功能测试")
    void testSapSyncFunctionality() throws Exception {
        // 1. 测试批量同步SAP库存
        SapStockSyncDTO syncDTO = createTestSapStockSyncDTO();
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/sync-sap-stock")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(Arrays.asList(syncDTO))))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());

        // 2. 测试单个物料同步
        mockMvc.perform(post("/api/rawmaterial/material-difference/sync-single-sap-stock")
                .param("materialCode", "MAT001")
                .param("erpWarehouseCode", "WH001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());
    }

    @Test
    @DisplayName("差异处理功能测试")
    void testDifferenceProcessingFunctionality() throws Exception {
        // 1. 测试单个差异处理
        InventoryDifferenceProcessDTO processDTO = createTestProcessDTO();
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(processDTO))
                .header("User-Id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());

        // 2. 测试批量差异处理
        InventoryDifferenceBatchProcessDTO batchProcessDTO = createTestBatchProcessDTO();
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/batch-process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(batchProcessDTO))
                .header("User-Id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());
    }

    @Test
    @DisplayName("数据导出功能测试")
    void testDataExportFunctionality() throws Exception {
        // 测试导出差异记录
        InventoryDifferenceQueryDTO queryDTO = createTestQueryDTO();
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    @DisplayName("历史记录查询功能测试")
    void testHistoryQueryFunctionality() throws Exception {
        // 测试历史记录查询
        InventoryDifferenceQueryDTO queryDTO = createTestQueryDTO();
        queryDTO.setStatus(2); // 查询已处理记录
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/history")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    @DisplayName("错误处理和边界条件测试")
    void testErrorHandlingAndBoundaryConditions() throws Exception {
        // 1. 测试无效ID查询
        mockMvc.perform(get("/api/rawmaterial/material-difference/0"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        // 2. 测试不存在的记录查询
        mockMvc.perform(get("/api/rawmaterial/material-difference/999999"))
                .andDo(print())
                .andExpect(status().isNotFound());

        // 3. 测试无效参数的同步请求
        mockMvc.perform(post("/api/rawmaterial/material-difference/sync-single-sap-stock")
                .param("materialCode", "")
                .param("erpWarehouseCode", ""))
                .andDo(print())
                .andExpect(status().isBadRequest());

        // 4. 测试空的批量处理请求
        mockMvc.perform(post("/api/rawmaterial/material-difference/batch-process")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")
                .header("User-Id", "1"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("并发处理测试")
    void testConcurrentProcessing() throws Exception {
        // 模拟并发请求
        InventoryDifferenceQueryDTO queryDTO = createTestQueryDTO();
        
        // 并发执行多个查询请求
        for (int i = 0; i < 5; i++) {
            mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(queryDTO)))
                    .andExpect(status().isOk());
        }
    }

    @Test
    @DisplayName("性能测试 - 大数据量查询")
    void testPerformanceWithLargeDataSet() throws Exception {
        // 测试大数据量分页查询
        InventoryDifferenceQueryDTO queryDTO = createTestQueryDTO();
        queryDTO.setPageSize(1000); // 大页面大小
        
        long startTime = System.currentTimeMillis();
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andDo(print())
                .andExpect(status().isOk());
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证响应时间在合理范围内（例如小于5秒）
        assert duration < 5000 : "查询响应时间过长: " + duration + "ms";
    }

    @Test
    @DisplayName("数据一致性测试")
    void testDataConsistency() throws Exception {
        // 1. 生成差异记录
        mockMvc.perform(post("/api/rawmaterial/material-difference/generate"))
                .andExpect(status().isOk());

        // 2. 查询待处理记录数量
        String pendingResponse = mockMvc.perform(get("/api/rawmaterial/material-difference/statistics/pending"))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // 3. 查询总统计信息
        String totalResponse = mockMvc.perform(get("/api/rawmaterial/material-difference/statistics"))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // 验证数据一致性（这里只是示例，实际需要解析JSON并比较数值）
        assert pendingResponse.contains("pendingCount");
        assert totalResponse.contains("totalCount");
    }

    // 辅助方法：创建测试查询DTO
    private InventoryDifferenceQueryDTO createTestQueryDTO() {
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("MAT");
        queryDTO.setStatus(1);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        return queryDTO;
    }

    // 辅助方法：创建测试处理DTO
    private InventoryDifferenceProcessDTO createTestProcessDTO() {
        InventoryDifferenceProcessDTO processDTO = new InventoryDifferenceProcessDTO();
        processDTO.setId(1);
        processDTO.setStatisticsEndDate(new Date());
        processDTO.setProcessRemark("集成测试处理");
        return processDTO;
    }

    // 辅助方法：创建测试批量处理DTO
    private InventoryDifferenceBatchProcessDTO createTestBatchProcessDTO() {
        InventoryDifferenceBatchProcessDTO batchProcessDTO = new InventoryDifferenceBatchProcessDTO();
        batchProcessDTO.setDifferenceIds(Arrays.asList(1, 2));
        batchProcessDTO.setStatisticsEndDate(new Date());
        batchProcessDTO.setProcessRemark("批量处理集成测试");
        
        InventoryDifferenceBatchDetailDTO batchDetail = new InventoryDifferenceBatchDetailDTO();
        batchDetail.setDifferenceId(1);
        batchDetail.setProcessRemark("批量处理明细");
        batchDetail.setDetailList(Arrays.asList());
        
        batchProcessDTO.setBatchDetails(Arrays.asList(batchDetail));
        return batchProcessDTO;
    }

    // 辅助方法：创建测试SAP同步DTO
    private SapStockSyncDTO createTestSapStockSyncDTO() {
        SapStockSyncDTO syncDTO = new SapStockSyncDTO();
        syncDTO.setSyncTime(new Date());
        syncDTO.setSyncStatus("success");
        syncDTO.setSyncMessage("测试同步");
        syncDTO.setSuccessCount(1);
        syncDTO.setFailedCount(0);
        return syncDTO;
    }
}