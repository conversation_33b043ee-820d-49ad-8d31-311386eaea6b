package com.hvisions.rawmaterial.controller.difference;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.*;
import com.hvisions.rawmaterial.service.InventoryDifferenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 原辅料库存盘点差异处理控制器集成测试
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(MaterialDifferenceController.class)
@DisplayName("原辅料库存盘点差异处理控制器测试")
class MaterialDifferenceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private InventoryDifferenceService inventoryDifferenceService;

    @Autowired
    private ObjectMapper objectMapper;

    private InventoryDifferenceDTO testDifferenceDTO;
    private InventoryDifferenceQueryDTO testQueryDTO;
    private InventoryDifferenceProcessDTO testProcessDTO;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 初始化测试数据
        setupTestData();
    }

    private void setupTestData() {
        // 创建测试差异记录DTO
        testDifferenceDTO = new InventoryDifferenceDTO();
        testDifferenceDTO.setId(1);
        testDifferenceDTO.setMaterialCode("MAT001");
        testDifferenceDTO.setMaterialName("测试物料");
        testDifferenceDTO.setErpWarehouseCode("WH001");
        testDifferenceDTO.setErpWarehouseName("测试仓库");
        testDifferenceDTO.setDepartment("原辅料管理部");
        testDifferenceDTO.setMesCurrentStock(new BigDecimal("1000.000"));
        testDifferenceDTO.setMesInitialStock(new BigDecimal("800.000"));
        testDifferenceDTO.setWeighbridgeReceiptQuantity(new BigDecimal("500.000"));
        testDifferenceDTO.setSolidWasteQuantity(new BigDecimal("50.000"));
        testDifferenceDTO.setIssuedQuantity(new BigDecimal("300.000"));
        testDifferenceDTO.setUnit("吨");
        testDifferenceDTO.setSapStock(new BigDecimal("950.000"));
        testDifferenceDTO.setDifferenceQuantity(new BigDecimal("50.000"));
        testDifferenceDTO.setStatus(1);
        testDifferenceDTO.setStatusDesc("待处理");
        testDifferenceDTO.setCreateTime(new Date());

        // 创建测试查询DTO
        testQueryDTO = new InventoryDifferenceQueryDTO();
        testQueryDTO.setMaterialCode("MAT001");
        testQueryDTO.setStatus(1);
        testQueryDTO.setPageNum(1);
        testQueryDTO.setPageSize(10);

        // 创建测试处理DTO
        testProcessDTO = new InventoryDifferenceProcessDTO();
        testProcessDTO.setId(1);
        testProcessDTO.setStatisticsEndDate(new Date());
        testProcessDTO.setProcessRemark("测试处理");
    }

    @Test
    @DisplayName("分页查询库存差异处理记录 - 成功")
    void testQueryInventoryDifferences_Success() throws Exception {
        // 准备测试数据
        List<InventoryDifferenceDTO> content = Arrays.asList(testDifferenceDTO);
        Page<InventoryDifferenceDTO> page = new PageImpl<>(content, 
            org.springframework.data.domain.PageRequest.of(0, 10), 1);

        // Mock服务方法
        when(inventoryDifferenceService.queryInventoryDifferences(any(InventoryDifferenceQueryDTO.class)))
            .thenReturn(page);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testQueryDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.content[0].materialCode").value("MAT001"))
                .andExpect(jsonPath("$.totalElements").value(1));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1))
            .queryInventoryDifferences(any(InventoryDifferenceQueryDTO.class));
    }

    @Test
    @DisplayName("根据ID查询差异处理记录详情 - 成功")
    void testGetInventoryDifferenceById_Success() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.getInventoryDifferenceById(1))
            .thenReturn(testDifferenceDTO);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.materialCode").value("MAT001"))
                .andExpect(jsonPath("$.materialName").value("测试物料"));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getInventoryDifferenceById(1);
    }

    @Test
    @DisplayName("根据ID查询差异处理记录详情 - 记录不存在")
    void testGetInventoryDifferenceById_NotFound() throws Exception {
        // Mock服务方法返回null
        when(inventoryDifferenceService.getInventoryDifferenceById(999))
            .thenReturn(null);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/999"))
                .andDo(print())
                .andExpect(status().isNotFound());

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getInventoryDifferenceById(999);
    }

    @Test
    @DisplayName("查询待处理的差异记录 - 成功")
    void testGetPendingDifferences_Success() throws Exception {
        // 准备测试数据
        List<InventoryDifferenceDTO> pendingList = Arrays.asList(testDifferenceDTO);

        // Mock服务方法
        when(inventoryDifferenceService.getPendingDifferences()).thenReturn(pendingList);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/pending"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].status").value(1));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getPendingDifferences();
    }

    @Test
    @DisplayName("生成差异处理记录 - 成功")
    void testGenerateDifferenceRecords_Success() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.generateDifferenceRecords()).thenReturn(5);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/generate"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("差异记录生成成功"))
                .andExpect(jsonPath("$.generatedCount").value(5));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).generateDifferenceRecords();
    }

    @Test
    @DisplayName("处理库存差异 - 成功")
    void testProcessDifference_Success() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.processDifference(any(InventoryDifferenceProcessDTO.class), anyInt()))
            .thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testProcessDTO))
                .header("User-Id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("差异处理成功"));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1))
            .processDifference(any(InventoryDifferenceProcessDTO.class), eq(1));
    }

    @Test
    @DisplayName("批量处理差异 - 成功")
    void testBatchProcessDifferences_Success() throws Exception {
        // 准备批量处理测试数据
        InventoryDifferenceBatchProcessDTO batchProcessDTO = new InventoryDifferenceBatchProcessDTO();
        batchProcessDTO.setDifferenceIds(Arrays.asList(1, 2, 3));
        batchProcessDTO.setStatisticsEndDate(new Date());
        batchProcessDTO.setProcessRemark("批量处理测试");
        
        InventoryDifferenceBatchDetailDTO batchDetail = new InventoryDifferenceBatchDetailDTO();
        batchDetail.setDifferenceId(1);
        batchDetail.setProcessRemark("批量处理明细");
        batchDetail.setDetailList(new ArrayList<>());
        
        batchProcessDTO.setBatchDetails(Arrays.asList(batchDetail));

        // Mock服务方法
        when(inventoryDifferenceService.batchProcessDifferences(anyList(), anyInt()))
            .thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/batch-process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(batchProcessDTO))
                .header("User-Id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("批量处理完成"))
                .andExpect(jsonPath("$.successCount").value(1));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1))
            .batchProcessDifferences(anyList(), eq(1));
    }

    @Test
    @DisplayName("重新计算差异 - 成功")
    void testRecalculateDifference_Success() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.getInventoryDifferenceById(1))
            .thenReturn(testDifferenceDTO);
        when(inventoryDifferenceService.calculateDifference(any(InventoryDifferenceDTO.class)))
            .thenReturn(testDifferenceDTO);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/1/recalculate"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.differenceQuantity").value(50.000));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getInventoryDifferenceById(1);
        verify(inventoryDifferenceService, times(1))
            .calculateDifference(any(InventoryDifferenceDTO.class));
    }

    @Test
    @DisplayName("同步SAP库存 - 成功")
    void testSyncSapStock_Success() throws Exception {
        // 准备测试数据
        List<SapStockSyncDTO> syncDTOList = Arrays.asList(new SapStockSyncDTO());

        // Mock服务方法
        when(inventoryDifferenceService.syncSapStock(anyList())).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/sync-sap-stock")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(syncDTOList)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("SAP库存同步完成"))
                .andExpect(jsonPath("$.successCount").value(1));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).syncSapStock(anyList());
    }

    @Test
    @DisplayName("手动同步单个物料的SAP库存 - 成功")
    void testSyncSingleSapStock_Success() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.syncSingleSapStock("MAT001", "WH001"))
            .thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/sync-single-sap-stock")
                .param("materialCode", "MAT001")
                .param("erpWarehouseCode", "WH001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("SAP库存同步成功"))
                .andExpect(jsonPath("$.materialCode").value("MAT001"))
                .andExpect(jsonPath("$.erpWarehouseCode").value("WH001"));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1))
            .syncSingleSapStock("MAT001", "WH001");
    }

    @Test
    @DisplayName("获取差异处理统计信息 - 成功")
    void testGetDifferenceStatistics_Success() throws Exception {
        // 准备测试数据
        InventoryDifferenceStatisticsDTO statisticsDTO = new InventoryDifferenceStatisticsDTO();
        statisticsDTO.setTotalCount(100);
        statisticsDTO.setPendingCount(20);
        statisticsDTO.setProcessedCount(80);

        // Mock服务方法
        when(inventoryDifferenceService.getDifferenceStatistics()).thenReturn(statisticsDTO);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalCount").value(100))
                .andExpect(jsonPath("$.pendingCount").value(20))
                .andExpect(jsonPath("$.processedCount").value(80));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getDifferenceStatistics();
    }

    @Test
    @DisplayName("获取待处理差异统计信息 - 成功")
    void testGetPendingDifferenceStatistics_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("pendingCount", 20);
        statistics.put("totalDifferenceAmount", new BigDecimal("1000.000"));

        // Mock服务方法
        when(inventoryDifferenceService.getPendingDifferenceStatistics()).thenReturn(statistics);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics/pending"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.pendingCount").value(20))
                .andExpect(jsonPath("$.totalDifferenceAmount").value(1000.000));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getPendingDifferenceStatistics();
    }

    @Test
    @DisplayName("获取已处理差异统计信息 - 成功")
    void testGetProcessedDifferenceStatistics_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("processedCount", 80);
        statistics.put("avgProcessTime", 2.5);

        // Mock服务方法
        when(inventoryDifferenceService.getProcessedDifferenceStatistics(7)).thenReturn(statistics);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/statistics/processed")
                .param("days", "7"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.processedCount").value(80))
                .andExpect(jsonPath("$.avgProcessTime").value(2.5));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getProcessedDifferenceStatistics(7);
    }

    @Test
    @DisplayName("查询历史差异记录 - 成功")
    void testQueryHistoryDifferences_Success() throws Exception {
        // 准备测试数据
        List<InventoryDifferenceDTO> content = Arrays.asList(testDifferenceDTO);
        Page<InventoryDifferenceDTO> page = new PageImpl<>(content, 
            org.springframework.data.domain.PageRequest.of(0, 10), 1);

        // Mock服务方法
        when(inventoryDifferenceService.queryHistoryDifferences(any(InventoryDifferenceQueryDTO.class)))
            .thenReturn(page);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/history")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testQueryDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.totalElements").value(1));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1))
            .queryHistoryDifferences(any(InventoryDifferenceQueryDTO.class));
    }

    @Test
    @DisplayName("导出差异记录 - 成功")
    void testExportDifferenceRecords_Success() throws Exception {
        // 准备测试数据
        List<InventoryDifferenceDTO> exportData = Arrays.asList(testDifferenceDTO);

        // Mock服务方法
        when(inventoryDifferenceService.exportDifferences(any(InventoryDifferenceQueryDTO.class)))
            .thenReturn(exportData);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testQueryDTO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("导出成功"))
                .andExpect(jsonPath("$.exportCount").value(1))
                .andExpect(jsonPath("$.data").isArray());

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1))
            .exportDifferences(any(InventoryDifferenceQueryDTO.class));
    }

    @Test
    @DisplayName("删除差异处理记录 - 成功")
    void testDeleteDifference_Success() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.deleteDifference(1)).thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(delete("/api/rawmaterial/material-difference/1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除成功"));

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).deleteDifference(1);
    }

    @Test
    @DisplayName("删除差异处理记录 - 记录不存在")
    void testDeleteDifference_NotFound() throws Exception {
        // Mock服务方法
        when(inventoryDifferenceService.deleteDifference(999)).thenReturn(false);

        // 执行请求并验证结果
        mockMvc.perform(delete("/api/rawmaterial/material-difference/999"))
                .andDo(print())
                .andExpect(status().isNotFound());

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).deleteDifference(999);
    }

    @Test
    @DisplayName("参数验证 - 无效的ID")
    void testValidation_InvalidId() throws Exception {
        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/0"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("参数验证 - 空的查询条件")
    void testValidation_EmptyQueryDTO() throws Exception {
        // 执行请求并验证结果
        mockMvc.perform(post("/api/rawmaterial/material-difference/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andDo(print())
                .andExpect(status().isOk()); // 空查询条件应该是允许的
    }

    @Test
    @DisplayName("异常处理 - 服务层异常")
    void testExceptionHandling_ServiceException() throws Exception {
        // Mock服务方法抛出异常
        when(inventoryDifferenceService.getInventoryDifferenceById(1))
            .thenThrow(new RuntimeException("服务异常"));

        // 执行请求并验证结果
        mockMvc.perform(get("/api/rawmaterial/material-difference/1"))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        // 验证服务方法调用
        verify(inventoryDifferenceService, times(1)).getInventoryDifferenceById(1);
    }
}