package com.hvisions.rawmaterial.controller.async;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage;
import com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Arrays;
import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 异步控制器集成测试
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
public class AsyncControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    public void testSubmitSapSyncTask() throws Exception {
        // 准备测试数据
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("PARTIAL");
        message.setMaterialCodes(Arrays.asList("TEST_MAT001", "TEST_MAT002"));
        message.setForceSync(false);
        
        // 执行请求
        MvcResult result = mockMvc.perform(post("/api/rawmaterial/material-difference/async/sap-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(message)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.taskId").exists())
                .andExpect(jsonPath("$.syncType").value("PARTIAL"))
                .andReturn();
        
        // 验证响应内容
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("SAP同步任务提交响应: " + responseContent);
    }
    
    @Test
    public void testSubmitBatchGenerateTask() throws Exception {
        // 准备测试数据
        BatchGenerateTaskMessage message = new BatchGenerateTaskMessage();
        message.setStatisticsStartDate(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
        message.setStatisticsEndDate(new Date());
        message.setMaterialCodes(Arrays.asList("TEST_MAT001", "TEST_MAT002"));
        message.setWarehouseCodes(Arrays.asList("TEST_WH001"));
        message.setBatchSize(50);
        message.setEstimatedCount(100);
        message.setOverrideExisting(false);
        
        // 执行请求
        MvcResult result = mockMvc.perform(post("/api/rawmaterial/material-difference/async/batch-generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(message)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.taskId").exists())
                .andExpect(jsonPath("$.estimatedCount").value(100))
                .andReturn();
        
        // 验证响应内容
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("批量生成任务提交响应: " + responseContent);
    }
    
    @Test
    public void testGetTaskStatus() throws Exception {
        // 先提交一个任务
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("FULL");
        
        MvcResult submitResult = mockMvc.perform(post("/api/rawmaterial/material-difference/async/sap-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(message)))
                .andExpect(status().isOk())
                .andReturn();
        
        // 从响应中提取任务ID
        String submitResponse = submitResult.getResponse().getContentAsString();
        String taskId = objectMapper.readTree(submitResponse).get("taskId").asText();
        
        // 查询任务状态
        mockMvc.perform(get("/api/rawmaterial/material-difference/async/task-status/" + taskId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(taskId))
                .andExpect(jsonPath("$.taskType").value("SAP_SYNC"))
                .andExpect(jsonPath("$.status").exists())
                .andExpect(jsonPath("$.updateTime").exists());
    }
    
    @Test
    public void testGetUserTasks() throws Exception {
        // 先提交几个任务
        SapSyncTaskMessage sapMessage = new SapSyncTaskMessage();
        sapMessage.setSyncType("FULL");
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/async/sap-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(sapMessage)))
                .andExpect(status().isOk());
        
        BatchGenerateTaskMessage batchMessage = new BatchGenerateTaskMessage();
        batchMessage.setStatisticsStartDate(new Date());
        batchMessage.setStatisticsEndDate(new Date());
        batchMessage.setEstimatedCount(50);
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/async/batch-generate")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(batchMessage)))
                .andExpect(status().isOk());
        
        // 查询用户任务列表
        mockMvc.perform(get("/api/rawmaterial/material-difference/async/user-tasks")
                .header("User-Id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
        
        // 查询特定类型的任务
        mockMvc.perform(get("/api/rawmaterial/material-difference/async/user-tasks")
                .param("taskType", "SAP_SYNC")
                .header("User-Id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }
    
    @Test
    public void testCancelTask() throws Exception {
        // 先提交一个任务
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("FULL");
        
        MvcResult submitResult = mockMvc.perform(post("/api/rawmaterial/material-difference/async/sap-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(message)))
                .andExpect(status().isOk())
                .andReturn();
        
        // 从响应中提取任务ID
        String submitResponse = submitResult.getResponse().getContentAsString();
        String taskId = objectMapper.readTree(submitResponse).get("taskId").asText();
        
        // 取消任务
        mockMvc.perform(post("/api/rawmaterial/material-difference/async/cancel-task/" + taskId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.taskId").value(taskId));
    }
    
    @Test
    public void testGetNonExistentTaskStatus() throws Exception {
        String nonExistentTaskId = "NON_EXISTENT_TASK_ID";
        
        mockMvc.perform(get("/api/rawmaterial/material-difference/async/task-status/" + nonExistentTaskId))
                .andExpect(status().isNotFound());
    }
    
    @Test
    public void testCancelNonExistentTask() throws Exception {
        String nonExistentTaskId = "NON_EXISTENT_TASK_ID";
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/async/cancel-task/" + nonExistentTaskId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }
    
    @Test
    public void testSubmitTaskWithInvalidData() throws Exception {
        // 测试无效的SAP同步任务
        SapSyncTaskMessage invalidMessage = new SapSyncTaskMessage();
        // 不设置必要的字段
        
        mockMvc.perform(post("/api/rawmaterial/material-difference/async/sap-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Id", "1")
                .content(objectMapper.writeValueAsString(invalidMessage)))
                .andExpect(status().isOk()); // 即使数据无效，提交也应该成功，错误会在处理时体现
    }
    
    @Test
    public void testSubmitTaskWithoutUserId() throws Exception {
        SapSyncTaskMessage message = new SapSyncTaskMessage();
        message.setSyncType("FULL");
        
        // 不设置User-Id头部
        mockMvc.perform(post("/api/rawmaterial/material-difference/async/sap-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(message)))
                .andExpect(status().isOk()); // 应该使用默认用户ID
    }
}