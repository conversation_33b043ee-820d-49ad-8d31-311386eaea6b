package com.hvisions.rawmaterial.performance;

import com.hvisions.rawmaterial.dto.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceQueryDTO;
import com.hvisions.rawmaterial.service.InventoryDifferenceService;
import com.hvisions.rawmaterial.service.cache.CacheService;
import com.hvisions.rawmaterial.service.performance.PerformanceMonitorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存性能测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class CachePerformanceTest {
    
    @Autowired
    private InventoryDifferenceService inventoryDifferenceService;
    
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private PerformanceMonitorService performanceMonitorService;
    
    /**
     * 测试缓存命中率
     */
    @Test
    public void testCacheHitRate() {
        // 清除性能统计
        performanceMonitorService.clearPerformanceStats();
        
        // 创建查询条件
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        // 第一次查询（缓存未命中）
        long startTime = System.currentTimeMillis();
        Page<InventoryDifferenceDTO> result1 = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
        long firstQueryTime = System.currentTimeMillis() - startTime;
        
        // 第二次查询（缓存命中）
        startTime = System.currentTimeMillis();
        Page<InventoryDifferenceDTO> result2 = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
        long secondQueryTime = System.currentTimeMillis() - startTime;
        
        // 验证结果一致性
        assertEquals(result1.getTotalElements(), result2.getTotalElements());
        
        // 验证缓存提升性能（第二次查询应该更快）
        assertTrue(secondQueryTime < firstQueryTime, 
                  String.format("缓存查询时间(%dms)应该小于首次查询时间(%dms)", secondQueryTime, firstQueryTime));
        
        // 获取缓存统计
        Map<String, Object> cacheStats = performanceMonitorService.getCacheHitRateStats();
        assertNotNull(cacheStats);
        
        System.out.println("首次查询时间: " + firstQueryTime + "ms");
        System.out.println("缓存查询时间: " + secondQueryTime + "ms");
        System.out.println("性能提升: " + ((double)(firstQueryTime - secondQueryTime) / firstQueryTime * 100) + "%");
        System.out.println("缓存统计: " + cacheStats);
    }
    
    /**
     * 测试并发缓存性能
     */
    @Test
    public void testConcurrentCachePerformance() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Long>> futures = new ArrayList<>();
        
        // 创建查询条件
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        // 并发执行查询
        for (int i = 0; i < 50; i++) {
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                long startTime = System.currentTimeMillis();
                inventoryDifferenceService.queryInventoryDifferences(queryDTO);
                return System.currentTimeMillis() - startTime;
            }, executor);
            futures.add(future);
        }
        
        // 等待所有查询完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        // 统计查询时间
        List<Long> queryTimes = new ArrayList<>();
        for (CompletableFuture<Long> future : futures) {
            queryTimes.add(future.join());
        }
        
        // 计算平均查询时间
        double avgQueryTime = queryTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long maxQueryTime = queryTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
        long minQueryTime = queryTimes.stream().mapToLong(Long::longValue).min().orElse(0L);
        
        System.out.println("并发查询统计:");
        System.out.println("平均查询时间: " + avgQueryTime + "ms");
        System.out.println("最大查询时间: " + maxQueryTime + "ms");
        System.out.println("最小查询时间: " + minQueryTime + "ms");
        
        // 验证并发性能
        assertTrue(avgQueryTime < 1000, "平均查询时间应该小于1秒");
        assertTrue(maxQueryTime < 5000, "最大查询时间应该小于5秒");
        
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
    }
    
    /**
     * 测试缓存一致性
     */
    @Test
    public void testCacheConsistency() {
        String testKey = "test:cache:consistency";
        String testValue = "test-value-" + System.currentTimeMillis();
        
        // 设置缓存
        cacheService.set(testKey, testValue, 60, TimeUnit.SECONDS);
        
        // 验证缓存存在
        assertTrue(cacheService.exists(testKey));
        
        // 获取缓存值
        String cachedValue = cacheService.get(testKey, String.class);
        assertEquals(testValue, cachedValue);
        
        // 删除缓存
        cacheService.delete(testKey);
        
        // 验证缓存已删除
        assertFalse(cacheService.exists(testKey));
        assertNull(cacheService.get(testKey, String.class));
    }
    
    /**
     * 测试缓存过期机制
     */
    @Test
    public void testCacheExpiration() throws InterruptedException {
        String testKey = "test:cache:expiration";
        String testValue = "test-value";
        
        // 设置短期缓存（2秒过期）
        cacheService.set(testKey, testValue, 2, TimeUnit.SECONDS);
        
        // 验证缓存存在
        assertTrue(cacheService.exists(testKey));
        assertEquals(testValue, cacheService.get(testKey, String.class));
        
        // 等待缓存过期
        Thread.sleep(3000);
        
        // 验证缓存已过期
        assertFalse(cacheService.exists(testKey));
        assertNull(cacheService.get(testKey, String.class));
    }
    
    /**
     * 测试批量缓存操作性能
     */
    @Test
    public void testBatchCacheOperations() {
        List<String> keys = new ArrayList<>();
        
        // 批量设置缓存
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            String key = "test:batch:" + i;
            String value = "value-" + i;
            keys.add(key);
            cacheService.set(key, value, 60, TimeUnit.SECONDS);
        }
        long batchSetTime = System.currentTimeMillis() - startTime;
        
        // 批量获取缓存
        startTime = System.currentTimeMillis();
        for (String key : keys) {
            cacheService.get(key, String.class);
        }
        long batchGetTime = System.currentTimeMillis() - startTime;
        
        // 批量删除缓存
        startTime = System.currentTimeMillis();
        cacheService.deleteAll(keys);
        long batchDeleteTime = System.currentTimeMillis() - startTime;
        
        System.out.println("批量缓存操作性能:");
        System.out.println("批量设置1000个缓存: " + batchSetTime + "ms");
        System.out.println("批量获取1000个缓存: " + batchGetTime + "ms");
        System.out.println("批量删除1000个缓存: " + batchDeleteTime + "ms");
        
        // 验证性能要求
        assertTrue(batchSetTime < 5000, "批量设置缓存时间应该小于5秒");
        assertTrue(batchGetTime < 2000, "批量获取缓存时间应该小于2秒");
        assertTrue(batchDeleteTime < 2000, "批量删除缓存时间应该小于2秒");
    }
    
    /**
     * 测试缓存内存使用情况
     */
    @Test
    public void testCacheMemoryUsage() {
        // 获取初始内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建大量缓存数据
        List<String> keys = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            String key = "test:memory:" + i;
            String value = "large-value-" + i + "-" + "x".repeat(100); // 创建较大的值
            keys.add(key);
            cacheService.set(key, value, 300, TimeUnit.SECONDS);
        }
        
        // 获取缓存后的内存使用情况
        runtime.gc(); // 建议垃圾回收
        long afterCacheMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = afterCacheMemory - initialMemory;
        
        System.out.println("缓存内存使用情况:");
        System.out.println("初始内存: " + (initialMemory / 1024 / 1024) + "MB");
        System.out.println("缓存后内存: " + (afterCacheMemory / 1024 / 1024) + "MB");
        System.out.println("内存增长: " + (memoryIncrease / 1024 / 1024) + "MB");
        
        // 清理缓存
        cacheService.deleteAll(keys);
        
        // 验证内存增长在合理范围内
        assertTrue(memoryIncrease < 100 * 1024 * 1024, "内存增长应该小于100MB");
    }
}