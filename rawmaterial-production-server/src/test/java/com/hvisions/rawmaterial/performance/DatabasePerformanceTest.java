package com.hvisions.rawmaterial.performance;

import com.hvisions.rawmaterial.dto.InventoryDifferenceDTO;
import com.hvisions.rawmaterial.dto.InventoryDifferenceQueryDTO;
import com.hvisions.rawmaterial.service.InventoryDifferenceService;
import com.hvisions.rawmaterial.service.optimization.BatchOperationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库性能测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class DatabasePerformanceTest {
    
    @Autowired
    private InventoryDifferenceService inventoryDifferenceService;
    
    @Autowired
    private BatchOperationService batchOperationService;
    
    /**
     * 测试分页查询性能
     */
    @Test
    public void testPaginationQueryPerformance() {
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageSize(20);
        
        // 测试不同页码的查询性能
        List<Long> queryTimes = new ArrayList<>();
        
        for (int page = 1; page <= 10; page++) {
            queryDTO.setPageNum(page);
            
            long startTime = System.currentTimeMillis();
            Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
            long queryTime = System.currentTimeMillis() - startTime;
            
            queryTimes.add(queryTime);
            
            System.out.println("第" + page + "页查询时间: " + queryTime + "ms, 记录数: " + result.getContent().size());
        }
        
        // 计算平均查询时间
        double avgQueryTime = queryTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long maxQueryTime = queryTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
        
        System.out.println("分页查询性能统计:");
        System.out.println("平均查询时间: " + avgQueryTime + "ms");
        System.out.println("最大查询时间: " + maxQueryTime + "ms");
        
        // 验证查询性能
        assertTrue(avgQueryTime < 2000, "平均查询时间应该小于2秒");
        assertTrue(maxQueryTime < 5000, "最大查询时间应该小于5秒");
    }
    
    /**
     * 测试批量查询性能
     */
    @Test
    public void testBatchQueryPerformance() {
        List<String> materialCodes = Arrays.asList("MAT001", "MAT002", "MAT003", "MAT004", "MAT005");
        List<String> warehouseCodes = Arrays.asList("WH001", "WH002", "WH003");
        
        // 测试批量查询
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = batchOperationService.batchQueryInventoryDifferences(materialCodes, warehouseCodes);
        long batchQueryTime = System.currentTimeMillis() - startTime;
        
        // 测试单个查询的总时间
        startTime = System.currentTimeMillis();
        for (String materialCode : materialCodes) {
            for (String warehouseCode : warehouseCodes) {
                InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
                queryDTO.setMaterialCode(materialCode);
                queryDTO.setErpWarehouseCode(warehouseCode);
                queryDTO.setPageNum(1);
                queryDTO.setPageSize(10);
                inventoryDifferenceService.queryInventoryDifferences(queryDTO);
            }
        }
        long individualQueryTime = System.currentTimeMillis() - startTime;
        
        System.out.println("批量查询性能对比:");
        System.out.println("批量查询时间: " + batchQueryTime + "ms");
        System.out.println("单个查询总时间: " + individualQueryTime + "ms");
        System.out.println("性能提升: " + ((double)(individualQueryTime - batchQueryTime) / individualQueryTime * 100) + "%");
        
        // 验证批量查询性能优势
        assertTrue(batchQueryTime < individualQueryTime, "批量查询应该比单个查询更快");
        assertTrue((Boolean) result.get("success"), "批量查询应该成功");
    }
    
    /**
     * 测试并发查询性能
     */
    @Test
    public void testConcurrentQueryPerformance() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(20);
        List<CompletableFuture<Long>> futures = new ArrayList<>();
        
        // 并发执行查询
        for (int i = 0; i < 100; i++) {
            final int threadId = i;
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
                queryDTO.setPageNum(threadId % 5 + 1);
                queryDTO.setPageSize(10);
                
                long startTime = System.currentTimeMillis();
                inventoryDifferenceService.queryInventoryDifferences(queryDTO);
                return System.currentTimeMillis() - startTime;
            }, executor);
            futures.add(future);
        }
        
        // 等待所有查询完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        // 统计查询时间
        List<Long> queryTimes = new ArrayList<>();
        for (CompletableFuture<Long> future : futures) {
            queryTimes.add(future.join());
        }
        
        // 计算统计数据
        double avgQueryTime = queryTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long maxQueryTime = queryTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
        long minQueryTime = queryTimes.stream().mapToLong(Long::longValue).min().orElse(0L);
        
        System.out.println("并发查询性能统计:");
        System.out.println("并发数: 100");
        System.out.println("平均查询时间: " + avgQueryTime + "ms");
        System.out.println("最大查询时间: " + maxQueryTime + "ms");
        System.out.println("最小查询时间: " + minQueryTime + "ms");
        
        // 验证并发性能
        assertTrue(avgQueryTime < 3000, "并发平均查询时间应该小于3秒");
        assertTrue(maxQueryTime < 10000, "并发最大查询时间应该小于10秒");
        
        executor.shutdown();
        executor.awaitTermination(30, TimeUnit.SECONDS);
    }
    
    /**
     * 测试批量更新性能
     */
    @Test
    public void testBatchUpdatePerformance() {
        // 准备测试数据
        List<Integer> differenceIds = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        Integer targetStatus = 2; // 已处理状态
        
        // 测试批量更新
        long startTime = System.currentTimeMillis();
        int updatedCount = batchOperationService.batchUpdateDifferenceStatus(differenceIds, targetStatus);
        long batchUpdateTime = System.currentTimeMillis() - startTime;
        
        System.out.println("批量更新性能:");
        System.out.println("更新记录数: " + updatedCount);
        System.out.println("批量更新时间: " + batchUpdateTime + "ms");
        System.out.println("平均每条记录更新时间: " + (batchUpdateTime / (double) differenceIds.size()) + "ms");
        
        // 验证批量更新性能
        assertTrue(batchUpdateTime < 5000, "批量更新时间应该小于5秒");
        assertTrue(updatedCount >= 0, "更新记录数应该大于等于0");
    }
    
    /**
     * 测试大数据量查询性能
     */
    @Test
    public void testLargeDataQueryPerformance() {
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(1000); // 大页面大小
        
        // 测试大数据量查询
        long startTime = System.currentTimeMillis();
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
        long largeQueryTime = System.currentTimeMillis() - startTime;
        
        System.out.println("大数据量查询性能:");
        System.out.println("查询记录数: " + result.getContent().size());
        System.out.println("总记录数: " + result.getTotalElements());
        System.out.println("查询时间: " + largeQueryTime + "ms");
        System.out.println("平均每条记录查询时间: " + (largeQueryTime / (double) result.getContent().size()) + "ms");
        
        // 验证大数据量查询性能
        assertTrue(largeQueryTime < 10000, "大数据量查询时间应该小于10秒");
        assertTrue(result.getContent().size() > 0, "应该查询到数据");
    }
    
    /**
     * 测试复杂条件查询性能
     */
    @Test
    public void testComplexQueryPerformance() {
        InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
        queryDTO.setMaterialCode("MAT");
        queryDTO.setErpWarehouseCode("WH");
        queryDTO.setStatus(1);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(50);
        
        // 测试复杂条件查询
        long startTime = System.currentTimeMillis();
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
        long complexQueryTime = System.currentTimeMillis() - startTime;
        
        System.out.println("复杂条件查询性能:");
        System.out.println("查询条件: 物料编码包含'MAT', 仓库编码包含'WH', 状态=1");
        System.out.println("查询记录数: " + result.getContent().size());
        System.out.println("查询时间: " + complexQueryTime + "ms");
        
        // 验证复杂查询性能
        assertTrue(complexQueryTime < 5000, "复杂条件查询时间应该小于5秒");
    }
    
    /**
     * 测试数据库连接池性能
     */
    @Test
    public void testConnectionPoolPerformance() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(50);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        // 大量并发数据库操作
        for (int i = 0; i < 200; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                InventoryDifferenceQueryDTO queryDTO = new InventoryDifferenceQueryDTO();
                queryDTO.setPageNum(1);
                queryDTO.setPageSize(5);
                inventoryDifferenceService.queryInventoryDifferences(queryDTO);
            }, executor);
            futures.add(future);
        }
        
        // 等待所有操作完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        System.out.println("数据库连接池性能测试:");
        System.out.println("并发操作数: 200");
        System.out.println("总执行时间: " + totalTime + "ms");
        System.out.println("平均每个操作时间: " + (totalTime / 200.0) + "ms");
        
        // 验证连接池性能
        assertTrue(totalTime < 30000, "连接池总执行时间应该小于30秒");
        
        executor.shutdown();
        executor.awaitTermination(60, TimeUnit.SECONDS);
    }
}