package com.hvisions.rawmaterial.performance;

import com.hvisions.rawmaterial.service.performance.PerformanceMonitorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 综合性能测试套件
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PerformanceTestSuite {
    
    @Autowired
    private PerformanceMonitorService performanceMonitorService;
    
    @BeforeEach
    public void setUp() {
        // 清除之前的性能统计数据
        performanceMonitorService.clearPerformanceStats();
    }
    
    /**
     * 测试性能监控服务
     */
    @Test
    public void testPerformanceMonitoring() {
        // 模拟缓存命中和未命中
        performanceMonitorService.recordCacheHit("inventory-difference-statistics");
        performanceMonitorService.recordCacheHit("inventory-difference-statistics");
        performanceMonitorService.recordCacheMiss("inventory-difference-statistics");
        
        // 模拟查询执行时间
        performanceMonitorService.recordQueryTime("inventory-difference", 150L);
        performanceMonitorService.recordQueryTime("inventory-difference", 200L);
        performanceMonitorService.recordQueryTime("inventory-difference", 120L);
        
        // 模拟SAP接口调用
        performanceMonitorService.recordSapCall("stock-query", true, 500L);
        performanceMonitorService.recordSapCall("stock-query", false, 2000L);
        performanceMonitorService.recordSapCall("stock-sync", true, 800L);
        
        // 获取性能统计
        Map<String, Object> cacheStats = performanceMonitorService.getCacheHitRateStats();
        Map<String, Object> queryStats = performanceMonitorService.getQueryPerformanceStats();
        Map<String, Object> sapStats = performanceMonitorService.getSapCallStats();
        
        // 验证缓存统计
        assertNotNull(cacheStats);
        assertTrue(cacheStats.containsKey("inventory-difference-statistics"));
        
        Map<String, Object> cacheTypeStats = (Map<String, Object>) cacheStats.get("inventory-difference-statistics");
        assertEquals(2L, cacheTypeStats.get("hits"));
        assertEquals(1L, cacheTypeStats.get("misses"));
        assertEquals(3L, cacheTypeStats.get("total"));
        assertEquals(66.67, (Double) cacheTypeStats.get("hitRate"), 0.01);
        
        // 验证查询统计
        assertNotNull(queryStats);
        assertTrue(queryStats.containsKey("inventory-difference"));
        
        Map<String, Object> queryTypeStats = (Map<String, Object>) queryStats.get("inventory-difference");
        assertEquals(3L, queryTypeStats.get("count"));
        assertEquals(470L, queryTypeStats.get("totalTime"));
        assertEquals(156.67, (Double) queryTypeStats.get("avgTime"), 0.01);
        
        // 验证SAP统计
        assertNotNull(sapStats);
        assertTrue(sapStats.containsKey("stock-query"));
        
        Map<String, Object> sapTypeStats = (Map<String, Object>) sapStats.get("stock-query");
        assertEquals(1L, sapTypeStats.get("success"));
        assertEquals(1L, sapTypeStats.get("failure"));
        assertEquals(2L, sapTypeStats.get("total"));
        assertEquals(50.0, (Double) sapTypeStats.get("successRate"), 0.01);
        
        System.out.println("缓存统计: " + cacheStats);
        System.out.println("查询统计: " + queryStats);
        System.out.println("SAP统计: " + sapStats);
    }
    
    /**
     * 测试综合性能报告
     */
    @Test
    public void testPerformanceReport() {
        // 模拟一些性能数据
        performanceMonitorService.recordCacheHit("sap-stock-data");
        performanceMonitorService.recordCacheMiss("sap-stock-data");
        performanceMonitorService.recordQueryTime("sap-stock", 300L);
        performanceMonitorService.recordSapCall("stock-sync", true, 1000L);
        
        // 获取综合性能报告
        Map<String, Object> report = performanceMonitorService.getPerformanceReport();
        
        // 验证报告结构
        assertNotNull(report);
        assertTrue(report.containsKey("cacheStats"));
        assertTrue(report.containsKey("queryStats"));
        assertTrue(report.containsKey("sapStats"));
        assertTrue(report.containsKey("timestamp"));
        
        // 验证时间戳
        Long timestamp = (Long) report.get("timestamp");
        assertTrue(timestamp > 0);
        assertTrue(System.currentTimeMillis() - timestamp < 1000); // 1秒内生成
        
        System.out.println("综合性能报告: " + report);
    }
    
    /**
     * 测试性能数据清除
     */
    @Test
    public void testPerformanceDataClearing() {
        // 添加一些性能数据
        performanceMonitorService.recordCacheHit("test-cache");
        performanceMonitorService.recordQueryTime("test-query", 100L);
        performanceMonitorService.recordSapCall("test-api", true, 200L);
        
        // 验证数据存在
        Map<String, Object> reportBefore = performanceMonitorService.getPerformanceReport();
        assertNotNull(reportBefore);
        
        // 清除性能数据
        performanceMonitorService.clearPerformanceStats();
        
        // 验证数据已清除
        Map<String, Object> reportAfter = performanceMonitorService.getPerformanceReport();
        assertNotNull(reportAfter);
        
        // 验证缓存统计已清空
        Map<String, Object> cacheStats = (Map<String, Object>) reportAfter.get("cacheStats");
        for (Object value : cacheStats.values()) {
            Map<String, Object> stats = (Map<String, Object>) value;
            assertEquals(0L, stats.get("hits"));
            assertEquals(0L, stats.get("misses"));
            assertEquals(0L, stats.get("total"));
        }
        
        System.out.println("清除前报告: " + reportBefore);
        System.out.println("清除后报告: " + reportAfter);
    }
    
    /**
     * 测试性能阈值告警
     */
    @Test
    public void testPerformanceThresholds() {
        // 模拟慢查询
        performanceMonitorService.recordQueryTime("slow-query", 5000L);
        performanceMonitorService.recordQueryTime("slow-query", 6000L);
        performanceMonitorService.recordQueryTime("slow-query", 4500L);
        
        // 模拟SAP接口超时
        performanceMonitorService.recordSapCall("timeout-api", false, 30000L);
        performanceMonitorService.recordSapCall("timeout-api", false, 25000L);
        
        // 获取性能统计
        Map<String, Object> queryStats = performanceMonitorService.getQueryPerformanceStats();
        Map<String, Object> sapStats = performanceMonitorService.getSapCallStats();
        
        // 验证慢查询统计
        Map<String, Object> slowQueryStats = (Map<String, Object>) queryStats.get("slow-query");
        assertNotNull(slowQueryStats);
        assertEquals(3L, slowQueryStats.get("count"));
        
        Double avgTime = (Double) slowQueryStats.get("avgTime");
        assertTrue(avgTime > 4000, "平均查询时间应该超过4秒阈值");
        
        // 验证SAP接口失败率
        Map<String, Object> timeoutApiStats = (Map<String, Object>) sapStats.get("timeout-api");
        assertNotNull(timeoutApiStats);
        assertEquals(0L, timeoutApiStats.get("success"));
        assertEquals(2L, timeoutApiStats.get("failure"));
        assertEquals(0.0, (Double) timeoutApiStats.get("successRate"), 0.01);
        
        System.out.println("慢查询统计: " + slowQueryStats);
        System.out.println("超时API统计: " + timeoutApiStats);
        
        // 这里可以添加告警逻辑
        if (avgTime > 3000) {
            System.out.println("告警: 查询平均响应时间超过3秒阈值");
        }
        
        Double successRate = (Double) timeoutApiStats.get("successRate");
        if (successRate < 90.0) {
            System.out.println("告警: SAP接口成功率低于90%");
        }
    }
    
    /**
     * 测试性能趋势分析
     */
    @Test
    public void testPerformanceTrends() throws InterruptedException {
        // 模拟性能数据随时间变化
        for (int i = 0; i < 10; i++) {
            // 模拟性能逐渐恶化
            long queryTime = 100L + (i * 50L);
            performanceMonitorService.recordQueryTime("trend-query", queryTime);
            
            // 模拟缓存命中率变化
            if (i < 5) {
                performanceMonitorService.recordCacheHit("trend-cache");
            } else {
                performanceMonitorService.recordCacheMiss("trend-cache");
            }
            
            Thread.sleep(10); // 短暂延迟模拟时间流逝
        }
        
        // 获取性能统计
        Map<String, Object> queryStats = performanceMonitorService.getQueryPerformanceStats();
        Map<String, Object> cacheStats = performanceMonitorService.getCacheHitRateStats();
        
        // 分析查询性能趋势
        Map<String, Object> trendQueryStats = (Map<String, Object>) queryStats.get("trend-query");
        Double avgTime = (Double) trendQueryStats.get("avgTime");
        
        // 分析缓存命中率趋势
        Map<String, Object> trendCacheStats = (Map<String, Object>) cacheStats.get("trend-cache");
        Double hitRate = (Double) trendCacheStats.get("hitRate");
        
        System.out.println("性能趋势分析:");
        System.out.println("查询平均时间: " + avgTime + "ms");
        System.out.println("缓存命中率: " + hitRate + "%");
        
        // 验证趋势
        assertTrue(avgTime > 200, "平均查询时间应该反映性能恶化趋势");
        assertEquals(50.0, hitRate, 0.01, "缓存命中率应该为50%");
    }
}