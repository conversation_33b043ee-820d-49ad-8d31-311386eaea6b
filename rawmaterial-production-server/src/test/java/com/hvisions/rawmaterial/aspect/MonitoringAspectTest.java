package com.hvisions.rawmaterial.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.service.monitoring.BusinessLogService;
import com.hvisions.rawmaterial.service.monitoring.PerformanceMetricsService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 监控切面测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class MonitoringAspectTest {
    
    @Mock
    private PerformanceMetricsService performanceMetricsService;
    
    @Mock
    private BusinessLogService businessLogService;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private ProceedingJoinPoint joinPoint;
    
    @Mock
    private Signature signature;
    
    @InjectMocks
    private MonitoringAspect monitoringAspect;
    
    @BeforeEach
    void setUp() throws Exception {
        when(joinPoint.getSignature()).thenReturn(signature);
        when(signature.getDeclaringTypeName()).thenReturn("com.hvisions.rawmaterial.service.difference.MaterialDifferenceService");
        when(signature.getName()).thenReturn("processDifference");
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"id\":1,\"processQuantity\":100.0}");
    }
    
    @Test
    void testMonitorDifferenceServiceSuccess() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{1, "test"};
        Object expectedResult = "success";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorDifferenceService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), anyString());
        verify(businessLogService, never()).logErrorOperation(
            anyString(), anyString(), any(Exception.class), anyInt(), anyString());
    }
    
    @Test
    void testMonitorDifferenceServiceException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{1, "test"};
        RuntimeException exception = new RuntimeException("Test exception");
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(exception);
        
        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            monitoringAspect.monitorDifferenceService(joinPoint);
        });
        
        // 验证结果
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(false), anyString());
        verify(businessLogService, times(1)).logErrorOperation(
            anyString(), eq("Test exception"), eq(exception), anyInt(), anyString());
    }
    
    @Test
    void testMonitorSapIntegrationServiceSuccess() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"M001", "W001"};
        Object expectedResult = "sap_result";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorSapIntegrationService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), anyString());
    }
    
    @Test
    void testMonitorMesDataServiceSuccess() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"M001", "2024-01-01", "2024-01-31"};
        Object expectedResult = 100.0;
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorMesDataService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), anyString());
    }
    
    @Test
    void testMonitorControllerSuccess() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"queryDTO"};
        Object expectedResult = "controller_result";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorController(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), anyString());
    }
    
    @Test
    void testMonitorRepositorySuccess() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{1};
        Object expectedResult = "repository_result";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorRepository(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), anyString());
    }
    
    @Test
    void testMonitorWithNullArgs() throws Throwable {
        // 准备测试数据
        Object expectedResult = "result";
        
        when(joinPoint.getArgs()).thenReturn(null);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorDifferenceService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), eq(""));
    }
    
    @Test
    void testMonitorWithEmptyArgs() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{};
        Object expectedResult = "result";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        
        // 执行测试
        Object result = monitoringAspect.monitorDifferenceService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), eq(""));
    }
    
    @Test
    void testMonitorWithLongParameters() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"very long parameter"};
        String longJson = "a".repeat(1500); // 超过1000字符的JSON
        Object expectedResult = "result";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        when(objectMapper.writeValueAsString(any())).thenReturn(longJson);
        
        // 执行测试
        Object result = monitoringAspect.monitorDifferenceService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), contains("...(truncated)"));
    }
    
    @Test
    void testMonitorWithJsonProcessingException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"test"};
        Object expectedResult = "result";
        
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(expectedResult);
        when(objectMapper.writeValueAsString(any())).thenThrow(new RuntimeException("JSON processing failed"));
        
        // 执行测试
        Object result = monitoringAspect.monitorDifferenceService(joinPoint);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(joinPoint, times(1)).proceed();
        verify(performanceMetricsService, times(1)).recordMethodPerformance(
            anyString(), anyLong(), eq(true), contains("参数序列化失败"));
    }
}