<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.auth.dao.UserMapper">
    <select id="getAllUser" resultType="com.hvisions.auth.excel.UserImport">
        SELECT t1.user_name, t2.user_account, t1.email, t1.mobile_phone
        FROM sys_user t1
                 JOIN sys_user_login t2 ON t1.id = t2.user_id
    </select>
    <select id="selectUserList" resultType="com.hvisions.auth.entity.SysUser">
        SELECT DISTINCT
            u.*
        FROM
            sys_user u
                JOIN sys_user_login l ON u.id = l.user_id
                LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        where 1=1
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%',#{userName},'%')
        </if>
        <if test="account != null and account != ''">
            AND l.user_account like concat('%',#{account},'%')
        </if>
        <if test="departmentId != null">
            AND u.department_id = #{departmentId}
        </if>
        <if test="roleId != null">
            AND ur.role_id = #{roleId}
        </if>
    </select>
    <select id="getUserName" resultType="java.lang.String">
        SELECT user_name
        FROM sys_user
        where id = #{userId}
    </select>
    <select id="queryUserLogList" resultType="com.hvisions.auth.entity.SysUserLog">
        SELECT * FROM `sys_user_log`
        where 1=1
        <if test="userName != null and userName != ''">
            AND user_name like concat('%',#{userName},'%')
        </if>
        <if test="logState != null and logState != ''">
            AND log_state = #{logState}
        </if>
        <if test="startTime != null and endTime != null">
            and log_time between #{startTime} and #{endTime}
        </if>
        order by log_time desc
        limit #{page},#{pageSize}
    </select>
    <select id="queryUserLogCount" resultType="java.lang.Long">
        SELECT count(*) FROM `sys_user_log`
        where 1=1
        <if test="userName != null and userName != ''">
            AND user_name like concat('%',#{userName},'%')
        </if>
        <if test="logState != null and logState != ''">
            AND log_state = #{logState}
        </if>
        <if test="startTime != null and endTime != null">
            and log_time between #{startTime} and #{endTime}
        </if>
    </select>
    <select id="queryUserLogHeatmap" resultType="com.hvisions.auth.dto.user.UserLogHeatmapDTO">
        SELECT date_str, count(*) numCount FROM `sys_user_log`
        where 1=1
        <if test="userName != null and userName != ''">
            AND user_name like concat('%',#{userName},'%')
        </if>
        <if test="logState != null and logState != ''">
            AND log_state = #{logState}
        </if>
        <if test="startTime != null and endTime != null">
            and log_time between #{startTime} and #{endTime}
        </if>
        group by date_str
        order by date_str
    </select>
    <select id="getUserByAccount" resultType="com.hvisions.auth.entity.SysUser">
        SELECT u.* from sys_user u
        join sys_user_login l on u.id = l.user_id
        where l.user_account = #{userAccout}
    </select>
    <select id="queryOperLogList" resultType="com.hvisions.auth.entity.SysOperLog">
        SELECT * FROM `sys_oper_log`
        where 1=1
        <if test="userName != null and userName != ''">
            AND oper_name like concat('%',#{userName},'%')
        </if>
        <if test="title != null and title != ''">
            AND title like concat('%',#{title},'%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="model != null and model != ''">
            AND model = #{model}
        </if>
        <if test="startTime != null and endTime != null">
            and oper_time between #{startTime} and #{endTime}
        </if>
        order by oper_time desc
        limit #{page},#{pageSize}
    </select>
    <select id="queryOperLogCount" resultType="java.lang.Long">
        SELECT count(*) FROM `sys_oper_log`
        where 1=1
        <if test="userName != null and userName != ''">
            AND oper_name like concat('%',#{userName},'%')
        </if>
        <if test="title != null and title != ''">
            AND title like concat('%',#{title},'%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="model != null and model != ''">
            AND model = #{model}
        </if>
        <if test="startTime != null and endTime != null">
            and oper_time between #{startTime} and #{endTime}
        </if>
    </select>
    <select id="queryOperLogHeatmap" resultType="com.hvisions.auth.dto.user.UserLogHeatmapDTO">
        SELECT date_str, count(*) numCount FROM `sys_oper_log`
        where 1=1
        <if test="userName != null and userName != ''">
            AND oper_name like concat('%',#{userName},'%')
        </if>
        <if test="title != null and title != ''">
            AND title like concat('%',#{title},'%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="model != null and model != ''">
            AND model = #{model}
        </if>
        <if test="startTime != null and endTime != null">
            and oper_time between #{startTime} and #{endTime}
        </if>
        group by date_str
        order by date_str
    </select>
</mapper>