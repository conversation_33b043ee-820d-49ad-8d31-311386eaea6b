spring:
  rabbitmq:
    host: *************
    port: 5672
    username: admin
    password: admin
  redis:
    host: *************
    port: 6378
    password: admin
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: S%7ZaZW5$p
    url: **************************************************************************************************
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
#IAM对接配置
#IAM 接口调用地址
iam.config.url: https://iam.lzlj.com
#IAM 应用标识 - 客户端应用注册ID（统一身份认证平台提供）
iam.config.client_id: MES
#IAM 密钥 - 客户端应用注册密钥（统一身份认证平台提供）
iam.config.client_secret: 3a948a94cce245188e4ae4e91a65f469
#ESB调用地址
esb:
  #IAM获取用户信息接口
  iam-url: https://iam.lzlj.com/bim-server/api/rest/integration/ExtApiIngtTargetAccountService/findBy
