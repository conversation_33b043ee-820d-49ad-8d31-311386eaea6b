package com.hvisions.auth.controller;

import com.hvisions.auth.service.UserClassService;
import com.hvisions.auth.dto.user.UserClassDTO;
import com.hvisions.auth.dto.user.UserClassQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: UserClassController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/8</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/user_class")
@Api(description = "用户属性类型控制器")
public class UserClassController {

    @Autowired
    UserClassService userClassService;


    /**
     * 创建人员属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "/创建人员属性类型")
    public Integer create(@Valid @RequestBody UserClassDTO classDTO) {
        return userClassService.create(classDTO);
    }

    /**
     * 修改人员属性类型
     *
     * @param classDTO 属性类型
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改人员属性类型")
    public void update(@Valid @RequestBody UserClassDTO classDTO) {
        userClassService.update(classDTO);
    }

    /**
     * 获取人员属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    @GetMapping(value = "/findById/{id}")
    @ApiOperation(value = "获取人员属性类型")
    public UserClassDTO findById(@PathVariable Integer id) {
        return userClassService.findById(id);
    }

    /**
     * 获取人员属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    @GetMapping(value = "/findByCode/{code}")
    @ApiOperation(value = "获取人员属性类型")
    public UserClassDTO findByCode(@PathVariable String code) {
        return userClassService.findByCode(code);
    }

    /**
     * 获取人员属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    @PostMapping(value = "/findPage")
    @ApiOperation(value = "")
    public Page<UserClassDTO> findPage(@RequestBody UserClassQueryDTO query) {
        return userClassService.findPage(query);
    }

    /**
     * 删除人员属性类型
     *
     * @param id 属性类型
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除人员属性类型")
    public void deleteById(@PathVariable Integer id) {
        userClassService.deleteById(id);
    }

    /**
     * 根据人员id查询人员属性列表
     *
     * @param id 人员di
     * @return 人员属性类型列表
     */
    @GetMapping(value = "/findByUserId/{id}")
    @ApiOperation(value = "根据人员id查询人员属性列表")
    public List<UserClassDTO> findByUserId(@PathVariable Integer id) {
        return userClassService.findByUserId(id);
    }

    /**
     * 向人员添加人员属性类型
     *
     * @param userId  用户Id
     * @param classId 属性类型id
     */
    @PostMapping(value = "/addClassToUser/{userId}/{classId}")
    @ApiOperation(value = "向人员添加人员属性类型")
    public void addClassToUser(@PathVariable Integer userId, @PathVariable Integer classId) {
        userClassService.addClassToUser(userId, classId);
    }

    /**
     * 删除人员的人员属性类型
     *
     * @param userId  人员di
     * @param classId 属性类型id
     */
    @DeleteMapping(value = "/removeClassToUser/{userId}/{classId}")
    @ApiOperation(value = "删除人员的人员属性类型")
    public void removeClassToUser(@PathVariable Integer userId, @PathVariable Integer classId) {
        userClassService.removeClassToUser(userId, classId);
    }
}