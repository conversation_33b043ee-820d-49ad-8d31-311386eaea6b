package com.hvisions.auth.service.impl;

import com.hvisions.auth.configuration.AuthConfiguration;
import com.hvisions.auth.consts.DbConst;
import com.hvisions.auth.dao.UserMapper;
import com.hvisions.auth.dto.group.GroupDTO;
import com.hvisions.auth.dto.module.ModuleDTO;
import com.hvisions.auth.dto.role.RoleDTO;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.auth.dto.user.UserQueryDTO;
import com.hvisions.auth.entity.*;
import com.hvisions.auth.enums.AuthExceptionEnum;
import com.hvisions.auth.excel.UserImport;
import com.hvisions.auth.repository.*;
import com.hvisions.auth.service.UserExtendService;
import com.hvisions.auth.service.UserLoginService;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.ExtendUtil;
import com.hvisions.common.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.util.Streamable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: SysUserExtendServiceImp</p>
 * <p>Description: 扩展属性服务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class UserExtendServiceImpl implements UserExtendService {
    private final UserRepository userRepository;
    private final UserLoginService userLoginService;
    private final UserLoginRepository userLoginRepository;
    private final ModuleRepository moduleRepository;
    private final DepartmentRepository departmentRepository;
    private final UserMapper userMapper;
    private final RoleRepository roleRepository;
    private final GroupRepository groupRepository;
    private final PasswordEncoder passwordEncoder;

    @Resource(name = "user_extend")
    BaseExtendService userExtendService;
    private final AuthConfiguration authConfiguration;


    @Autowired
    public UserExtendServiceImpl(UserLoginRepository userLoginRepository,
                                 UserLoginService userLoginService,
                                 UserRepository userRepository,
                                 ModuleRepository moduleRepository,
                                 DepartmentRepository departmentRepository, UserMapper userMapper, RoleRepository roleRepository, GroupRepository groupRepository, PasswordEncoder passwordEncoder, AuthConfiguration authConfiguration) {
        this.userLoginService = userLoginService;
        this.userLoginRepository = userLoginRepository;
        this.userRepository = userRepository;
        this.moduleRepository = moduleRepository;
        this.departmentRepository = departmentRepository;
        this.userMapper = userMapper;
        this.roleRepository = roleRepository;
        this.groupRepository = groupRepository;
        this.passwordEncoder = passwordEncoder;
        this.authConfiguration = authConfiguration;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized Integer addUser(UserDTO userDTO) {
        //验证用户名可用性
        if (StringUtils.isEmpty(userDTO.getUserName())) {
            throw new BaseKnownException(AuthExceptionEnum.USER_NAME_NULL);
        }
        //验证账号可用性
        if (StringUtils.isEmpty(userDTO.getUserAccount())) {
            throw new BaseKnownException(AuthExceptionEnum.USER_ACCOUNT_NULL);
        }
        //验证手机号不能重复
        if (Strings.isBlank(userDTO.getMobilePhone())) {
            userDTO.setMobilePhone("");
        } else {
            List<SysUser> entities = userRepository.findByMobilePhone(userDTO.getMobilePhone());
            if (entities.size() > 0) {
                throw new BaseKnownException(10000, "用户手机号已经存在");
            }
        }
        //保存用户信息
        userLoginService.checkAccount(userDTO.getUserAccount());
        SysUser user = DtoMapper.convert(userDTO, SysUser.class);
        GroupDTO groupDTO = userDTO.getGroup();
        if (groupDTO != null) {
            Optional<SysGroup> group = groupRepository.findById(groupDTO.getId());
            group.ifPresent(user::setGroup);
        }
        user.setRoles(roleRepository.findAllById(userDTO.getRoleIdList()));
        userRepository.saveAndFlush(user);
        //保存用户登录信息
        SysUserLogin sysUserLogin = DtoMapper.convert(userDTO, SysUserLogin.class);
        if (StringUtils.isEmpty(userDTO.getUserPassword())) {
//            if ("".equals(userDTO.getMobilePhone())) {
            sysUserLogin.setUserPassword(passwordEncoder.encode(authConfiguration.getPassword()));
            sysUserLogin.setInitialPassword(true);
//            } else {
//                String mobilePhone = userDTO.getMobilePhone();
//                String password = "Lzlj@" + mobilePhone.substring(5);
//                sysUserLogin.setUserPassword(passwordEncoder.encode(password));
//                sysUserLogin.setInitialPassword(true);
//            }
        } else {
            sysUserLogin.setUserPassword(passwordEncoder.encode(userDTO.getUserPassword().trim()));
            sysUserLogin.setInitialPassword(false);
        }
        sysUserLogin.setUserId(user.getId());
        sysUserLogin.setExpireTime(userDTO.getExpireTime());
        sysUserLogin.setIsLock(false);
        userLoginService.save(sysUserLogin);
        //设置扩展属性
        ExtendInfo extendInfo = new ExtendInfo();
        extendInfo.setEntityId(user.getId());
        extendInfo.setValues(userDTO.getExtend());
        userExtendService.addExtendInfo(extendInfo);
        return user.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        SysUserLogin logInfo = userLoginRepository.findByUserId(id);
        if (logInfo == null) {
            throw new BaseKnownException(10000, "用户账户信息不存在，请检查数据");
        }
        if (logInfo.getUserAccount().equals(DbConst.ADMIN_ACCOUNT)) {
            throw new BaseKnownException(AuthExceptionEnum.ADMIN_CAN_NOT_BE_DELETE);
        }
        userLoginRepository.delete(logInfo);
        userExtendService.deleteExtendInfo(id);
        userRepository.deleteById(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserDTO> getUserByRoleId(int roleId) {
        Optional<SysRole> sysRole = roleRepository.findById(roleId);
        List<UserDTO> userDTOS = new ArrayList<>();
        if (sysRole.isPresent()) {
            List<UserDTO> sysUsers =
                    DtoMapper.convertList(sysRole.get().getUsers(), UserDTO.class);
            ExtendUtil.completeExtend(userExtendService, sysUsers);
            userDTOS = sysUsers;
        }
        return userDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserDTO> getUsersByRoleName(String roleName) {
        SysRole sysRole = roleRepository.findByName(roleName);
        List<UserDTO> userDTOS = new ArrayList<>();
        if (sysRole != null) {
            List<UserDTO> sysUsers =
                    DtoMapper.convertList(sysRole.getUsers(), UserDTO.class);
            ExtendUtil.completeExtend(userExtendService, sysUsers);
            userDTOS = sysUsers;
        }
        return userDTOS;
    }

    @Override
    public List<UserDTO> getUsersByRoleCode(String roleCode) {
        SysRole sysRole = roleRepository.findByEncode(roleCode);
        List<UserDTO> userDTOS = new ArrayList<>();
        if (sysRole != null) {
            List<UserDTO> sysUsers =
                    DtoMapper.convertList(sysRole.getUsers(), UserDTO.class);
            ExtendUtil.completeExtend(userExtendService, sysUsers);
            userDTOS = sysUsers;
        }
        return userDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<UserDTO> getUserByDepartmentId(UserQueryDTO queryDTO) {
        Page<SysUser> users;
        if (queryDTO.getDepartmentId() == null) {
            users = userRepository.findAll(queryDTO.getRequest());
        } else {
            List<Integer> departmentIds = new ArrayList<>();
            List<SysDepartment> all = departmentRepository.findAll();
            addDepartmentId(departmentIds, all, queryDTO.getDepartmentId());
            users = userRepository.findSysUserByDepartmentIdIn(departmentIds, queryDTO.getRequest());
        }
        List<UserDTO> sysUserDTOS = getSysUserDTOS(users);
        return new PageImpl<>(sysUserDTOS, queryDTO.getRequest(), users.getTotalElements());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserDTO getUserInfoByAccount(String account) {
        SysUserLogin userAccount = userLoginRepository.findByUserAccount(account);
        if (userAccount == null) {
            return null;
        }
        return getUserDto(userAccount.getUserId());
    }

    /**
     * 递归添加部门id
     *
     * @param departmentIds 部门id
     * @param all           所有的部门
     */
    private void addDepartmentId(List<Integer> departmentIds,
                                 List<SysDepartment> all,
                                 Integer departmentId) {

        departmentIds.add(departmentId);
        List<SysDepartment> childDepartment = all
                .stream()
                .filter(t -> t.getParentId().equals(departmentId))
                .collect(Collectors.toList());
        for (SysDepartment sysDepartment : childDepartment) {
            addDepartmentId(departmentIds, all, sysDepartment.getId());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Page<UserDTO> findUserDtoByKeyword(PageRequest pageRequest, String keyword) {
        //获取用户分页信息
        Page<SysUser> sysUserPage = userRepository.findByKeyword(keyword, pageRequest);
        List<UserDTO> sysUserDTOS = getSysUserDTOS(sysUserPage);
        return new PageImpl<>(sysUserDTOS, pageRequest, sysUserPage.getTotalElements());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserDTO getUserDto(int userId) {
        SysUser sysUser = userRepository.findById(userId).orElse(null);
        if (sysUser == null) {
            return null;
        }
        UserDTO dto = DtoMapper.convert(sysUser, UserDTO.class, "roles", "classes", "properties");
        SysUserLogin login = userLoginRepository.findByUserId(userId);
        if (login != null) {
            dto.setUserAccount(login.getUserAccount());
            dto.setExpireTime(login.getExpireTime());
            dto.setIsLock(login.getIsLock());
        }
        dto.setGroup(DtoMapper.convert(sysUser.getGroup(), GroupDTO.class));
        BeanUtils.copyProperties(sysUser, dto);

        Map<String, Object> extendInfo = userExtendService.getExtend(userId);
        dto.setExtend(extendInfo);
        return dto;
    }

    @Override
    public void initAdmin() {
        //查看是否存在管理员账户
        SysUserLogin byUserAccount = userLoginRepository.findByUserAccount(DbConst.ADMIN_ACCOUNT);
        if (byUserAccount == null) {
            UserDTO userDTO = new UserDTO();
            userDTO.setUserAccount(DbConst.ADMIN_ACCOUNT);
            userDTO.setUserName("管理员");
            userDTO.setUpdateTime(new Date());
            userDTO.setCreateTime(new Date());
            addUser(userDTO);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UserBaseDTO> findUserByDepartmentId(List<Integer> departmentId) {
        List<SysDepartment> allDepartments = departmentRepository.findAll();
        List<SysDepartment> result = new ArrayList<>();
        //最大循环防止数据异常的死循环
        List<SysDepartment> departments = allDepartments.stream()
                .filter(t -> departmentId.contains(t.getId()))
                .collect(Collectors.toList());
        int maxTime = 0;
        while (departments.size() > 0 && maxTime < 10) {
            result.addAll(departments);
            List<SysDepartment> finalDepartments = departments;
            departments = allDepartments.stream()
                    .filter(t -> finalDepartments.stream().anyMatch(d -> d.getId().equals(t.getParentId())))
                    .collect(Collectors.toList());
            maxTime++;
        }
        //根据上面查出来的所有部门id查询部门下面的人员
        List<SysUser> allUsers = userRepository.findAllByDepartmentIdIn(result
                .stream().map(SysBase::getId).collect(Collectors.toList()));
        return DtoMapper.convertList(allUsers, UserBaseDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserDTO getUserByPhoneNumber(String phoneNumber) {
        List<SysUser> user = userRepository.findByMobilePhone(phoneNumber);
        if (user.size() == 0) {
            return null;
        }
        if (user.size() > 1) {
            log.warn("数据出现异常,手机号出现重复,手机号是:{}", phoneNumber);
        }
        return DtoMapper.convert(user.get(0), UserDTO.class);
    }

    /**
     * 导入用户
     *
     * @param file 用户数据
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importUser(MultipartFile file) {
        List<UserImport> data = EasyExcelUtil.getImport(file, UserImport.class);
        for (UserImport userImport : data) {
            if (Strings.isEmpty(userImport.getUserAccount())) {
                continue;
            }
            //判断账户是否存在
            SysUserLogin userLogin = userLoginRepository.findByUserAccount(userImport.getUserAccount());
            //存在用户直接跳过，不存在进行添加操作
            if (userLogin != null) {
                continue;
            }
            UserDTO dto = new UserDTO();
            dto.setUserAccount(userImport.getUserAccount());
            dto.setUserName(userImport.getUserName());
            addUser(dto);
        }
        return null;
    }


    /**
     * 更新用户dto
     *
     * @param userDTO 用户dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserExtendInfo(UserDTO userDTO) {
        SysUser user = userRepository.getOne(userDTO.getId());
        // 更新用户组
        if (userDTO.getGroup() != null && userDTO.getGroup().getId() != null) {
            SysGroup group = groupRepository.findById(userDTO.getGroup().getId()).orElse(null);
            if (group != null) {
                user.setGroup(group);
                userDTO.setGroup(DtoMapper.convert(group, GroupDTO.class));
            }
        }
        //更新用户基础信息
        BeanUtils.copyProperties(userDTO, user);
        //更新用户角色信息
        user.setRoles(roleRepository.findAllById(userDTO.getRoleIdList()));
        //如果用户手机号空。设置为null
        if (Strings.isNotBlank(userDTO.getMobilePhone())) {
            //查询是否更改，如果更改了，根据手机查询是否有重复的数据
            List<SysUser> userEntity = userRepository.findByMobilePhone(userDTO.getMobilePhone());
            if (userEntity.stream().anyMatch(t -> !t.getId().equals(userDTO.getId()))) {
                throw new BaseKnownException(10000, "用户手机号不能重复，请重试");
            }
            user.setMobilePhone(userDTO.getMobilePhone());
        } else {
            userDTO.setMobilePhone("");
        }
        userRepository.save(user);
        //更新用户登录信息
        SysUserLogin sysUserLogin = userLoginRepository.findByUserId(userDTO.getId());
        if (Strings.isNotBlank(sysUserLogin.getUserAccount()) && !sysUserLogin.getUserAccount().equals(DbConst.ADMIN_ACCOUNT)) {
            sysUserLogin.setUserAccount(userDTO.getUserAccount());
            sysUserLogin.setExpireTime(userDTO.getExpireTime());
            if (!StringUtils.isEmpty(userDTO.getUserPassword())) {
                sysUserLogin.setUserPassword(userDTO.getUserPassword());
                sysUserLogin.setInitialPassword(false);
            }
            userLoginRepository.save(sysUserLogin);
        }
        //更新扩展信息
        ExtendInfo info = new ExtendInfo();
        info.setEntityId(userDTO.getId());
        info.setValues(userDTO.getExtend());
        userExtendService.updateExtendInfo(info);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<UserDTO> findUserDtoByUserNameAndAccount(PageRequest pageRequest, String userName, String account, Integer departmentId, Integer roleId) {
        //获取用户分页信息
        //Page<SysUser> sysUserPage = userRepository.findByUserNameOrAccount(userName, account, pageRequest);
        UserQueryDTO queryDTO = new UserQueryDTO();
        queryDTO.setRoleId(roleId);
        queryDTO.setAccount(account);
        queryDTO.setUserName(userName);
        queryDTO.setDepartmentId(departmentId);
        queryDTO.setPage(pageRequest.getPageNumber());
        queryDTO.setPageSize(pageRequest.getPageSize());
        Page<SysUser> sysUserPage = PageHelperUtil.getPage(userMapper::selectUserList, queryDTO, SysUser.class);
        List<UserDTO> sysUserDTOS = getSysUserDTOS(sysUserPage);
        for (UserDTO sysUserDTO : sysUserDTOS) {
            if (sysUserDTO.getModuleId() != null) {
                List<Integer> modules = new ArrayList<>();
                modules.add(sysUserDTO.getModuleId());
                List<SysModule> allById = moduleRepository.findAllById(modules);
                if (allById.size() > 0) {
                    sysUserDTO.setSysModuleDTO(DtoMapper
                            .convert(allById.get(0), ModuleDTO.class));
                }
            }
        }
        return new PageImpl<>(sysUserDTOS, pageRequest, sysUserPage.getTotalElements());
    }

    /**
     * 根据分页数据补全用户数据
     *
     * @param sysUserPage 用户分页数据
     * @return 用户信息列表
     */
    private List<UserDTO> getSysUserDTOS(Streamable<SysUser> sysUserPage) {
        if (sysUserPage != null && sysUserPage.stream().count() == 0) {
            return new ArrayList<>();
        }
        //获取所有用户id。查询对应的扩展信息
        List<Integer> userIds = sysUserPage.stream()
                .map(SysBase::getId).collect(Collectors.toList());
        List<ExtendInfo> extendInfos = userExtendService.getExtend(userIds);
        //转换成dto
        List<UserDTO> sysUserDTOS = sysUserPage.stream()
                .map(t -> {
                    UserDTO dto = new UserDTO();
                    BeanUtils.copyProperties(t, dto);
                    return dto;
                }).collect(Collectors.toList());
        //查询用户对应的登录信息
        List<SysUserLogin> sysUserLogins = userLoginRepository.findAllByUserIdIn(userIds);
        //查询用户角色列表
        List<SysUser> sysUsers = userRepository.findAllById(userIds);
        //查询所有部门信息，并且添加到用户信息表中
        List<SysDepartment> allDepartments = departmentRepository.findAll();

        for (UserDTO dto : sysUserDTOS) {
            //dto添加扩展信息
            for (ExtendInfo info : extendInfos) {
                if (info.getEntityId() == dto.getId()) {
                    dto.setExtend(info.getValues());
                    break;
                }
            }
            //添加账号信息
            for (SysUserLogin sysUserLogin : sysUserLogins) {
                if (sysUserLogin.getUserId().equals(dto.getId())) {
                    dto.setUserAccount(sysUserLogin.getUserAccount());
                    dto.setIsLock(sysUserLogin.getIsLock());
                    dto.setExpireTime(sysUserLogin.getExpireTime());
                    break;
                }
            }
            //添加角色列表信息
            for (SysUser sysUser : sysUsers) {
                if (sysUser.getId().equals(dto.getId())) {
                    dto.setRoleIdList(sysUser.getRoles().stream().map(SysRole::getId).collect(Collectors.toList()));
                    dto.setRoles(DtoMapper.convertList(sysUser.getRoles(), RoleDTO.class));
                    break;
                }
            }

            //添加部门信息
            if (null != dto.getDepartmentId()) {
                List<Integer> departmentIds = new ArrayList<>();
                List<String> departmentNames = new ArrayList<>();
                Optional<SysDepartment> departmentFind = allDepartments.stream()
                        .filter(t -> t.getId().equals(dto.getDepartmentId()))
                        .findFirst();
//                departmentFind.ifPresent(sysDepartment -> dto.setDepartmentName(sysDepartment.getDepartmentName()));
                while (departmentFind.isPresent()) {
                    departmentIds.add(departmentFind.get().getId());
                    departmentNames.add(departmentFind.get().getDepartmentName());
                    Optional<SysDepartment> finalDepartmentFind = departmentFind;
                    departmentFind = allDepartments.stream()
                            .filter(t -> t.getId().equals(finalDepartmentFind.get().getParentId()))
                            .findFirst();
                }
                Collections.reverse(departmentIds);
                Collections.reverse(departmentNames);
                dto.setDepartmentIdList(departmentIds);

                String departName = departmentNames.stream().collect(Collectors.joining("/"));
                dto.setDepartmentName(departName);
            }
        }
        return sysUserDTOS;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void updateUsersByDepartmentId(Integer departmentId, List<Integer> userIds) {
        List<SysUser> users = userRepository.findAllById(userIds);
        users.forEach(user -> {
            user.setDepartmentId(departmentId);
            userRepository.save(user);
        });
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UserDTO> findUserDtosByUserNameAndGroupId(String userName, Integer groupId) {
        List<UserDTO> results = new ArrayList<>();
        List<SysUser> users = userRepository.findByUserNameContainingAndGroupId(userName, groupId);
        List<SysUserLogin> userLogins = userLoginRepository.findAllByUserIdIn(users.stream().map(SysBase::getId).collect(Collectors.toList()));
        users.forEach(u -> {
            UserDTO dto = DtoMapper.convert(u, UserDTO.class);
            userLogins.stream().filter(i -> i.getUserId().equals(u.getId())).findFirst().ifPresent(hit -> {
                dto.setUserAccount(hit.getUserAccount());
            });
            results.add(dto);
        });
        return results;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<UserDTO> getAllUsersByDepartmentIdAndGroupId(Integer departmentId, Integer groupId) {
        List<SysUser> users = userRepository.findAllByDepartmentIdAndGroupId(departmentId, groupId);
        return DtoMapper.convertList(users, UserDTO.class);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     */
    @Override
    public ExcelExportDto getImportTemplate() {
        List<UserImport> data = getDemoUser();
        return EasyExcelUtil.getExcel(data, UserImport.class, "用户模板.xlsx");
    }

    /**
     * 下载模板(可以通过url直接下载)
     *
     * @return 模板
     */
    @Override
    public ResponseEntity<byte[]> getImportTemplateUrl() {
        List<UserImport> data = getDemoUser();
        //这里可以传入数据列表就是导出功能。
        return EasyExcelUtil.getExcelResponse(data, UserImport.class, "users.xlsx");
    }


    /**
     * 导出用户
     *
     * @return 导入模板
     */
    @Override
    public ExcelExportDto exportUserInfo() {
        List<UserImport> allUser = getAllUser();
        return EasyExcelUtil.getExcel(allUser, UserImport.class, "用户信息.xlsx");
    }


    /**
     * 导出用户(Url下载）
     *
     * @return 导入模板
     */
    @Override
    public ResponseEntity<byte[]> exportUserInfoUrl() {
        List<UserImport> allUser = getAllUser();
        return EasyExcelUtil.getExcelResponse(allUser, UserImport.class, "users.xlsx");
    }

    /**
     * 获取所有用户
     *
     * @return 用户数据
     */
    private List<UserImport> getAllUser() {
        return userMapper.getAllUser();
    }

    /**
     * 创建一个模拟用户数据
     *
     * @return 用户数据
     */
    private List<UserImport> getDemoUser() {
        List<UserImport> data = new ArrayList<>();
        UserImport entity = new UserImport();
        entity.setEmail("xxx#163.com");
        entity.setUserAccount("user1");
        entity.setUserPassword("111111");
        entity.setUserName("用户1");
        entity.setMobilePhone("***********");
        data.add(entity);
        return data;
    }
}
