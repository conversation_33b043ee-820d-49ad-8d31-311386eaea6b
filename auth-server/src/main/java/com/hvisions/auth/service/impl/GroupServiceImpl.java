package com.hvisions.auth.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hvisions.auth.dto.group.GroupDTO;
import com.hvisions.auth.dto.role.RoleDTO;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.auth.entity.SysGroup;
import com.hvisions.auth.entity.SysRole;
import com.hvisions.auth.entity.SysUser;
import com.hvisions.auth.entity.SysUserLogin;
import com.hvisions.auth.repository.GroupRepository;
import com.hvisions.auth.repository.UserLoginRepository;
import com.hvisions.auth.repository.UserRepository;
import com.hvisions.auth.service.DepartmentService;
import com.hvisions.auth.service.GroupService;
import com.hvisions.auth.service.RoleService;
import com.hvisions.auth.service.UserExtendService;
import com.hvisions.common.utils.DtoMapper;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class GroupServiceImpl implements GroupService {
	
	@Autowired
	GroupRepository groupRepository;
	
	@Autowired
	UserRepository userRepository;
	
	@Autowired
	UserLoginRepository userLoginRepository;
	
	@Autowired
	RoleService roleService;
	
	@Autowired
	UserExtendService userService;
	
	@Autowired
	DepartmentService departmentService;
	
	
	
	@Override
	public Integer create(GroupDTO groupDTO) {
		SysGroup group = DtoMapper.convert(groupDTO, SysGroup.class, "id");
		if (groupDTO.getRoleIds() == null || groupDTO.getRoleIds().isEmpty()) {
			log.error("Empty roleIds");
		} else {
			List<SysRole> roles = roleService.getByRoleIds(groupDTO.getRoleIds());
			group.setRoles(roles);
		}
		SysGroup newGroup = groupRepository.save(group);
		return newGroup.getId();
	}

	@Override
	public void update(GroupDTO groupDTO) {
		Assert.notNull(groupDTO.getId(), "更新时主键不能为空");
		groupRepository.findById(groupDTO.getId()).ifPresent(group -> {
			// update group info
			group.setGroupName(groupDTO.getGroupName());
			group.setDescription(groupDTO.getDescription());
			group.setAppModuleId(groupDTO.getAppModuleId());
			// update group role relationship
			group.setRoles(roleService.getByRoleIds(groupDTO.getRoleIds()));
			List<SysUser> users = group.getUsers();
			users.forEach(user -> {
				for (int i = 0; i < user.getRoles().size(); i ++) {
					// 若user的roleid不在group的roleIds里，则移除这些roleIds
					if (groupDTO.getRoleIds().indexOf(user.getRoles().get(i).getId()) == -1) {
						user.getRoles().remove(i);
					}
				}
			});
			group.setUsers(users);
			groupRepository.save(group);
		});
	}

	@Override
	public void delete(Integer id) {
		groupRepository.findById(id).ifPresent(g -> {
			g.setRoles(new ArrayList<>());
			g.getUsers().forEach(u -> {
				userService.delete(u.getId());
			});
		});
		groupRepository.deleteById(id);
	}

	@Override
	public List<GroupDTO> getByName(String name) {
		List<GroupDTO> groups = DtoMapper.convertList(groupRepository.findByNameOrder(name), GroupDTO.class);
		List<Integer> groupIds = groups.stream().map(g -> g.getId()).collect(Collectors.toList());
		List<SysUserLogin> userLogins;
		if (!groupIds.isEmpty()) {
			userLogins = userLoginRepository.findAllByGroupIds(groupIds);
		} else {
			userLogins = Collections.emptyList();
		}
		groups.forEach(g -> {
			List<UserDTO> users = DtoMapper.convertList(g.getUsers(), UserDTO.class);
			users.forEach(u -> {
				userLogins.stream().filter(i -> i.getUserId().equals(u.getId())).findFirst().ifPresent(hit -> {
					u.setUserAccount(hit.getUserAccount());
					u.setExpireTime(hit.getExpireTime());
					u.setIsLock(hit.getIsLock());
				});
			});
			g.setUsers(users);
		});
		return groups;
	}

	@Override
	public GroupDTO getById(Integer id) {
		SysGroup g = groupRepository.findById(id).get();
		if (g != null) {
			List<Integer> uids = g.getUsers().stream().map(u -> u.getId()).collect(Collectors.toList());
			List<SysUserLogin> userLogins = userLoginRepository.findAllByUserIdIn(uids);
			GroupDTO dto = new GroupDTO();
			dto.setId(g.getId());
			dto.setGroupName(g.getGroupName());
			dto.setDescription(g.getDescription());
			dto.setAppModuleId(g.getAppModuleId());
			dto.setCreateTime(g.getCreateTime());
			dto.setRoles(DtoMapper.convertList(g.getRoles(), RoleDTO.class));
			List<UserDTO> users = DtoMapper.convertList(g.getUsers(), UserDTO.class);
			users.forEach(u -> {
				userLogins.stream().filter(i -> i.getUserId() == u.getId()).findFirst().ifPresent(hit -> {
					u.setUserAccount(hit.getUserAccount());
					u.setExpireTime(hit.getExpireTime());
		            u.setIsLock(hit.getIsLock());
				});
			});
			dto.setUsers(users);
			return dto;
		} else {
			return null;
		}
	}

}
