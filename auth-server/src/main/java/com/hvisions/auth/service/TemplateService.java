package com.hvisions.auth.service;

import com.hvisions.auth.dto.board.TemplateDTO;
import com.hvisions.auth.dto.board.TemplateQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <p>Title: TemplateService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface TemplateService {


    /**
     * 创建报表模版
     *
     * @param templateDTO 模版信息
     */
    void createTemplate(TemplateDTO templateDTO);


    /**
     * 删除报表模版
     *
     * @param id 模版id
     */
    void deleteById(int id);

    /**
     * 分页查询报表模版
     *
     * @param templateQueryDTO 查询条件
     * @return 模版信息
     */
    Page<TemplateDTO> getTemplateByQuery(TemplateQueryDTO templateQueryDTO);
}