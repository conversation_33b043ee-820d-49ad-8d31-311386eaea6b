package com.hvisions.auth.repository;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.hvisions.auth.entity.SysGroup;

/**
 * 
 * <AUTHOR>
 *
 */
@Repository
public interface GroupRepository extends JpaRepository<SysGroup, Integer> {
	/**
	 * 根据用户组名称查询用户组
	 * @param name
	 * @return
	 */
	@Query(value = "SELECT g from SysGroup g WHERE groupName like %?1% order by createTime desc")
	List<SysGroup> findByNameOrder(String name);
	
	/**
	 * 根据groupIds，查询用户组name和用户数
	 * 
	 * @param groupIds
	 * @return
	 */
	@Query(value = "SELECT sg.id, sg.group_name AS groupName, count(su.id) AS totalUserCount "
			+ "FROM sys_group sg LEFT JOIN sys_user su ON sg.id = su.group_id "
			+ "WHERE sg.id in (:groupIds) GROUP BY sg.id", nativeQuery = true)
	List<Map<String, Object>> findGroupUsersTotalCountByGroupIds(@Param("groupIds") List<Integer> groupIds);
	
}
