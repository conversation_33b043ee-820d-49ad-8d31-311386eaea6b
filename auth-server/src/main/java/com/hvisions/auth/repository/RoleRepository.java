package com.hvisions.auth.repository;

import com.hvisions.auth.entity.SysRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <p>Title: RoleRepository</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/9/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface RoleRepository extends JpaRepository<SysRole, Integer> {

    /**
     * 根据角色名获取角色列表
     *
     * @param name 角色
     * @return 用户角色列表
     */
    List<SysRole> findByNameContains(String name);

    /**
     * 根据角色名称查询
     *
     * @param roleName 角色名称
     * @return 角色名称
     */
    SysRole findByName(String roleName);
    /**
     * 根据角色编码查询
     *
     * @param enCode 角色编码
     * @return 角色名称
     */
    SysRole findByEncode(String enCode);

    /**
     * 根据名称或者编码匹配
     *
     * @param name     名称
     * @param code     编码
     * @param pageable 分页信息
     * @return 角色分页信息
     */
    Page<SysRole> findAllByNameContainsOrEncodeContains(String name, String code, Pageable pageable);

}
