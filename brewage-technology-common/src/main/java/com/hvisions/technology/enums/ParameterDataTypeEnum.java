package com.hvisions.technology.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ParameterTypeEnum</p>
 * <p>Description: 参数类型枚举
 * 数据类型，1.整数，2.浮点数，3.字符串，4.布尔值，5.日期，6.时间，7.精确小数，</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/27</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum ParameterDataTypeEnum implements IKeyValueObject {
    //参数类型
    INT(1, "整数"),
    FLOAT(2, "浮点数"),
    STRING(3, "字符串"),
    BOOLEAN(4, "布尔值"),
    DATE(5, "日期"),
    DATETIME(6, "时间"),
    DECIMAL(7, "精确小数"),
    ;

    ParameterDataTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    private int code;
    private String name;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public static ParameterDataTypeEnum valueOf(int index) {
        switch (index) {
            case 1:
                return INT;
            case 2:
                return FLOAT;
            case 3:
                return STRING;
            case 4:
                return BOOLEAN;
            case 5:
                return DATE;
            case 6:
                return DATETIME;
            case 7:
                return DECIMAL;
            default:
                return INT;
        }
    }

    public static ParameterDataTypeEnum valueOfString(String typename) {
        if (INT.getName().equals(typename)) {
            return INT;
        } else if (FLOAT.getName().equals(typename)) {
            return FLOAT;
        } else if (STRING.getName().equals(typename)) {
            return STRING;
        } else if (BOOLEAN.getName().equals(typename)) {
            return BOOLEAN;
        } else if (DATETIME.getName().equals(typename)) {
            return DATETIME;
        } else if (DATE.getName().equals(typename)) {
            return DATE;
        } else if (DECIMAL.getName().equals(typename)) {
            return DECIMAL;
        }
        return INT;
    }
}

    
    
    
    
    
    
    
    
