package com.hvisions.technology.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: GatewayTypeEnum</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/12/11</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */

public enum GatewayTypeEnum implements IKeyValueObject {
        //工艺步骤关系类型
        EXCLUSIVE(0, "排他"),
        PARALLEL(1, "平行"),
    ;

    GatewayTypeEnum(int code, String name) {
            this.code = code;
            this.name = name;
        }

        private int code;
        private String name;

        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getName() {
            return name;
        }
    }