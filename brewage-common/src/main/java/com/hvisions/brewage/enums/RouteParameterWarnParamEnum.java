package com.hvisions.brewage.enums;

/**
 * 业务参数
 */
public enum RouteParameterWarnParamEnum {
    // 枚举常量定义
    QUANTITY_INVESTED("窖池养护任务", "大曲使用量(kg)","大曲使用量(kg)"),
    TL_TEMPERATURE("摊晾任务", "摊晾斗内糟醅温度","摊晾斗内糟醅温度"),
    TL_DZJQL("摊晾任务", "单甑加曲量","单甑加曲量"),

    RJ_LOADING_TEMPERATURE("入窖任务", "入窖温度","入窖温度"),
    RJ_EMPTY_TIME("入窖任务", "空窖时长","空窖时长"),

    DZ_FermentedGrainsWeight("堆糟任务", "单甑糟醅重量","单甑糟醅重量"),
    DZ_SingleCellarIncreaseRate("堆糟任务", "单窖涨幅","单窖涨幅"),

    BZ_SingleContainerRiceHuskSettingValue("拌糟任务", "单甑稻壳设定值","单甑稻壳设定值"),

    SZ_DurationOfSteaming("上甑任务", "上甑时长","上甑时长"),
    SZ_SteamPressureDepth("上甑任务", "压汽深度","压汽深度"),

    ZL_YellowWaterUsage("蒸馏任务", "黄水单甑使用量","黄水单甑使用量"),
    ZL_TailWineUsage("蒸馏任务", "尾酒单甑使用量","尾酒单甑使用量"),
    ZL_Hj1SingleContainerUsage("蒸馏任务", "HJ1单甑使用量","HJ1单甑使用量"),

    ZJ_WeightFlowingWineOne("摘酒任务", "重量","一段流酒重量"),
    ZJ_WeightFlowingWineTwo("摘酒任务", "重量","二段流酒重量"),
    ZJ_WeightFlowingWineThree("摘酒任务", "重量","三段流酒重量"),
    ZJ_WeightFlowingWineFour("摘酒任务", "重量","四段流酒重量"),
    ZJ_AlcoholContentOne("摘酒任务", "酒精度","一段酒精度"),
    ZJ_AlcoholContentTwo("摘酒任务", "酒精度","二段酒精度"),
    ZJ_AlcoholContentThree("摘酒任务", "酒精度","三段酒精度"),
    ZJ_AlcoholContentFour("摘酒任务", "酒精度","四段酒精度"),
    ZJ_FlowingWineTemperature("摘酒任务", "流酒温度","流酒温度"),
    ZJ_FlowingWineSpeed("摘酒任务", "流酒速度","流酒速度"),
    ZJ_CoverPlateFlowingWineDuration("摘酒任务", "盖盘到流酒时长","盖盘到流酒时长"),

    CZ_SteamingTime("出甑任务", "蒸粮时长","蒸粮时长"),
    CZ_SteamUsageDuration("出甑任务", "用汽时长","用汽时长"),
    CZ_FlowMeasurementUsage("出甑任务", "量水单甑使用量","量水单甑使用量"),
    CZ_OneMeasureWaterWeight("出甑任务", "第一次量水重量","第一次量水重量"),
    CZ_OneMeasureWaterTemperature("出甑任务", "第一次量水温度","第一次量水温度"),
    CZ_TwoMeasureWaterTemperature("出甑任务", "第二次量水温度","第二次量水温度"),
    CZ_TemperatureMeasuringBucket("出甑任务", "量水桶量水温度","量水桶量水温度"),
    CZ_BottomPotWaterEmissionNumber("出甑任务", "底锅水排放次数","底锅水排放次数"),
    ;

    // 枚举字段
    private final String businessModule;
    private final String paramCode;
    private final String paramName;

    // 私有构造函数
    RouteParameterWarnParamEnum(String businessModule, String paramCode,String paramName) {
        this.businessModule = businessModule;
        this.paramCode = paramCode;
        this.paramName = paramName;
    }

    // Getter方法
    public String getBusinessModule() {
        return businessModule;
    }

    public String getParamCode() {
        return paramCode;
    }

    public String getParamName() {
        return paramName;
    }
}
