package com.hvisions.brewage.dto.plan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/4/12 14:51
 */
@Data
public class PurchasePlanMaterialDTO {
    @ApiModelProperty("物料id")
    private Integer materialId;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("单位;kg、g、t")
    private String unit;

    @ApiModelProperty("数量")
    private BigDecimal totalQuality;
}
