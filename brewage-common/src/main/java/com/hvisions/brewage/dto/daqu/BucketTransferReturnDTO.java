package com.hvisions.brewage.dto.daqu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 空斗转运返回dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "空斗转运返回dto")
public class BucketTransferReturnDTO {

    @ApiModelProperty(value = "曲斗编码")
    private String bucketNo;

    @ApiModelProperty(value = "车牌号")
    private String licenseNo;


}
