/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.vo;

import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname RetortContainerVO
 * @description 甑口配置视图实体类
 * @date 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("RetortContainerVO对象")
public class RetortContainerVO extends SysBase {

    @ApiModelProperty("糟源代码")
    private String sourceCode;

    @ApiModelProperty("每天生产甑口数")
    private Integer retortCount;

    @ApiModelProperty("涨幅系数")
    private BigDecimal gainsCoefficient;

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

}
