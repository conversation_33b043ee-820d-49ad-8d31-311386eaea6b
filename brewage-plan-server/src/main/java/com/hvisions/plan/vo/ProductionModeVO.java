/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.vo;

import com.hvisions.brewage.mkwine.req.SysBase;
import com.hvisions.plan.dto.PmDetailDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @classname ProductionModeVO
 * @description 生产模式视图实体类
 * @date 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("ProductionModeVO对象")
public class ProductionModeVO extends SysBase {

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("生产模式编号")
    private String code;

    @ApiModelProperty("生产模式名称")
    private String name;

    @ApiModelProperty("生效开始日期")
    private String beginTime;

    @ApiModelProperty("生效结束日期")
    private String endTime;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

    @ApiModelProperty("生产甑口详细(甑口编码+甑口数量)")
    private String retortContainerDetail;

    @ApiModelProperty("涨幅系数详细(甑口编码+涨幅系数)")
    private String gainsCoefficientDetail;

    @ApiModelProperty("生效时间")
    private LocalDateTime effectTime;

    @ApiModelProperty("生产模式详情")
    private List<PmDetailDto> pmDetailDtoList;
}
