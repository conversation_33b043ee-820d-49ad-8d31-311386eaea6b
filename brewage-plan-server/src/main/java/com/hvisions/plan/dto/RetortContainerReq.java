package com.hvisions.plan.dto;

import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname RetortContainerReq
 * @description 甑口配置数据传输对象实体类
 * @date 2022-03-23
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("RetortContainer数据传输对象实体类")
public class RetortContainerReq extends SysBase {

    @ApiModelProperty("糟源代码")
    private String sourceCode;

    @ApiModelProperty("每天生产甑口数")
    private Integer retortCount;

    @ApiModelProperty("涨幅系数")
    private BigDecimal gainsCoefficient;

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;
}