/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.controller;

import com.hvisions.plan.service.ISchedulingStatsDetailService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 排程统计详情数据管理相关接口
 *
 * <AUTHOR>
 * 2022-03-23
 */
@RestController
@RequestMapping("/system/SchedulingStatsDetail")
@Api(value = "排程统计详情数据管理相关接口", tags = "排程统计详情数据管理相关接口")
public class SchedulingStatsDetailController {

    @Autowired
    ISchedulingStatsDetailService baseService;
}