package com.hvisions.plan.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.dao.ProductionModeMapper;
import com.hvisions.plan.dto.PmDetailReq;
import com.hvisions.plan.dto.ProductionModeReq;
import com.hvisions.plan.entity.PmDetail;
import com.hvisions.plan.entity.ProductionMode;
import com.hvisions.plan.service.IPmDetailService;
import com.hvisions.plan.service.IProductionModeService;
import com.hvisions.plan.utils.CopyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 生产模式 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
public class ProductionModeServiceImpl extends ServiceImpl<ProductionModeMapper, ProductionMode> implements IProductionModeService {
    @Autowired
    IPmDetailService pmDetailService;

    /**
     * 新增生产模式
     *
     * @param req
     * @return
     */
    @Override
    public void addInfo(ProductionModeReq req) {
        ProductionMode entity = CopyUtil.simpleCopy(req, ProductionMode.class);
        List<String> sourceCodeList = req.getPmDetailReqList().stream().map(PmDetailReq::getSourceCode).collect(Collectors.toList());
        // 槽源编码排序
        Collections.sort(sourceCodeList);
        entity.setCode(StringUtils.join(sourceCodeList.toArray(), ""));
        entity.setVersion(CommonConsts.INTEGER_NULL);
        entity.setState(StateEnum.NEW.getCode());
        save(entity);

        req.getPmDetailReqList().forEach(it -> {
            PmDetail pmDetail = CopyUtil.simpleCopy(it, PmDetail.class);
            pmDetail.setProductionModeId(entity.getId());
            pmDetailService.save(pmDetail);
        });
    }

    /**
     * 修改生产模式
     *
     * @param req
     * @return
     */
    @Override
    public void updateInfo(ProductionModeReq req) {
        ProductionMode productionMode = getById(req.getId());
        Assert.isTrue(Objects.equals(productionMode.getState(), StateEnum.NEW.getCode()), "只有【新建】状态下的数据才可以修改");

        ProductionMode entity = CopyUtil.simpleCopy(req, ProductionMode.class);
        List<String> sourceCodeList = req.getPmDetailReqList().stream().map(PmDetailReq::getSourceCode).collect(Collectors.toList());
        // 槽源编码排序
        Collections.sort(sourceCodeList);
        entity.setCode(StringUtils.join(sourceCodeList.toArray(), ""));
        updateById(entity);

        // 明细 先删后插
        pmDetailService.remove(new LambdaQueryWrapper<PmDetail>()
                .eq(PmDetail::getProductionModeId, entity.getId()));

        req.getPmDetailReqList().forEach(it -> {
            PmDetail pmDetail = CopyUtil.simpleCopy(it, PmDetail.class);
            pmDetail.setProductionModeId(entity.getId());

            pmDetailService.save(pmDetail);
        });
    }

    /**
     * 删除生产模式
     *
     * @param id 主键
     * @return
     */
    @Override
    public void deleteInfo(Integer id) {
        removeById(id);

        // 删除原先明细
        pmDetailService.remove(new LambdaQueryWrapper<PmDetail>()
                .eq(PmDetail::getProductionModeId, id));
    }

    /**
     * 状态切换（生效/归档）
     *
     * @param id     主键
     * @param status 状态;1:生效,2:归档
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/16 11:04
     */
    @Override
    public void switchStatus(Integer id, String status) {
        ProductionMode entity = getById(id);

        if (Objects.equals(StateEnum.TAKE_EFFECT.getCode(), status)) {
            Assert.isTrue(Objects.equals(entity.getState(), StateEnum.NEW.getCode()), "当前记录非新建状态，不允许生效操作");
            // 查询生产基地+糟源代码 记录
            LambdaQueryWrapper<ProductionMode> wrapper = new LambdaQueryWrapper<ProductionMode>()
                    .eq(ProductionMode::getProductionBaseId, entity.getProductionBaseId())
                    .eq(ProductionMode::getCode, entity.getCode()).orderByDesc(ProductionMode::getVersion);
            List<ProductionMode> productionModeList = list(wrapper);
            entity.setVersion(productionModeList.get(0).getVersion() + 1);
            entity.setEffectTime(LocalDateTime.now());
            // 将其他的条目置为归档
            List<ProductionMode> collect = productionModeList.stream()
                    .filter(it -> it.getState().equals(StateEnum.TAKE_EFFECT.getCode())).collect(Collectors.toList());
            for (ProductionMode productionMode : collect) {
                productionMode.setState(StateEnum.FILE.getCode());
                updateById(productionMode);
            }
        } else {
            Assert.isTrue(Objects.equals(entity.getState(), StateEnum.TAKE_EFFECT.getCode()), "当前记录非生效状态，不允许存档操作");
        }
        entity.setState(status);
        updateById(entity);
    }
}
