package com.hvisions.plan.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.plan.dao.SchedulingLocationMapper;
import com.hvisions.plan.entity.SchedulingLocation;
import com.hvisions.plan.service.ISchedulingLocationService;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 排程生效位置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
public class SchedulingLocationServiceImpl extends ServiceImpl<SchedulingLocationMapper, SchedulingLocation> implements ISchedulingLocationService {
    @Autowired
    LocationExtendClient locationExtendClient;

    @Autowired
    SchedulingLocationMapper schedulingLocationMapper;

    @Override
    public void handleNewLocation(Integer scheduleId, String productionBaseIds, String centerIds, String locationIds) {
        String[] productionBaseIdArray = productionBaseIds.split(",");
        Assert.isTrue(0 < productionBaseIdArray.length, "请传入正确的生产基地信息");
        String[] centerArray = centerIds.split(",");
        Assert.isTrue(0 < centerArray.length, "请传入正确的酿酒中心信息");
        for (String productionBaseId : productionBaseIdArray) {
            //查询基地
            int baseId = Integer.parseInt(productionBaseId);
            LocationDTO locationDTOL1 = Optional.ofNullable(locationExtendClient.getLocationById(baseId))
                    .filter(ResultVO::isSuccess)
                    .map(ResultVO::getData)
                    .orElseThrow(() -> new BaseKnownException(325001, productionBaseId + "获取生产基地信息失败"));
            //查询中心
            List<LocationDTO> locationDTOL2 = Optional.ofNullable(locationExtendClient.getLocationListByParentId(baseId))
                    .filter(ResultVO::isSuccess)
                    .map(ResultVO::getData)
                    .orElseThrow(() -> new BaseKnownException(325002, productionBaseId + "获取生产基地下属酿酒中心信息异常"));
            locationDTOL2.stream()
                    .filter(center -> Arrays.stream(centerArray).anyMatch(compare -> center.getId() == Integer.parseInt(compare)))
                    .forEach(center -> {
                        //查询车间
                        Integer centerId = center.getId();
                        List<LocationDTO> locationDTOL3 = Optional.ofNullable(locationExtendClient.getLocationListByParentId(centerId))
                                .filter(ResultVO::isSuccess)
                                .map(ResultVO::getData)
                                .orElseThrow(() -> new BaseKnownException(325003, MessageFormat.format("【{0}】获取酿酒中心下属车间信息异常", center.getCode())));
                        if (CollectionUtils.isEmpty(locationDTOL3)) {
                            throw new BaseKnownException(419011, MessageFormat.format("【{0}】没有配置车间", center.getCode()));
                        }
                        List<SchedulingLocation> schedulingLocations = locationDTOL3.stream()
                                .filter(location -> "0".equals(locationIds.trim())
                                        || Arrays.stream(locationIds.split(",")).anyMatch(compare -> location.getId() == Integer.parseInt(compare)))
                                .map(location -> new SchedulingLocation()
                                        .setSchedulingId(scheduleId)
                                        .setPlantId(locationDTOL1.getId())
                                        .setPlantCode(locationDTOL1.getCode())
                                        .setPlantName(locationDTOL1.getName())
                                        .setCenterId(centerId)
                                        .setCenterCode(center.getCode())
                                        .setCenterName(center.getName())
                                        .setLocationId(location.getId())
                                        .setLocationCode(location.getCode())
                                        .setLocationName(location.getName()))
                                .collect(Collectors.toList());
                        this.saveBatch(schedulingLocations);
                    });
        }
    }

    @Override
    public void deleteOldLocation(Integer scheduleId) {
        schedulingLocationMapper.physicsDeleteBySchedulingId(scheduleId);
    }
}
