package com.hvisions.auth.client;

import com.hvisions.auth.client.dto.SysOperLogSaveDto;
import com.hvisions.auth.dto.role.RoleDTO;
import com.hvisions.auth.dto.user.*;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class UserFallBackClient extends BaseFallbackFactory<UserClient> {
    @Override
    public UserClient getFallBack(ResultVO vo) {
        return new UserClient() {
            @Override
            public ResultVO<Map<String, Object>> getUserExtend(int userId) {
                return vo;
            }

            @Override
            public ResultVO createUserExtend(ExtendColumnInfo extendColumnInfo) {
                return vo;
            }

            @Override
            public ResultVO deleteUserExtend(String name) {
                return vo;
            }

            @Override
            public ResultVO<UserDTO> getUser(int userId) {
                return vo;
            }

            @Override
            public ResultVO<UserDTO> getUserByPhoneNumber(String phoneNumber) {
                return null;
            }

            @Override
            public ResultVO<UserDTO> getUserWithModule(Integer userId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UserDTO>> getUserPageByName(PageInfo pageInfo, String userName) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UserDTO>> getUserPageByNameOrAccount(PageInfo pageInfo, String userName, String account) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UserDTO>> getUserByDepartmentId(UserQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ExtendColumnInfo>> getExtendColumnInfo() {
                return vo;
            }

            @Override
            public ResultVO<Integer> createUser(UserDTO userDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteUserExtendInfo(int userId) {
                return vo;
            }

            @Override
            public ResultVO updateUser(UserDTO sysUserDTO) {
                return vo;
            }

            @Override
            public ResultVO updateUserPrivilege(User_RoleDTO user_roleDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<UserDTO>> getUsersByRoleName(String roleName) {
                return vo;
            }

            @Override
            public ResultVO<List<UserDTO>> getUsersByRole(int roleId) {
                return vo;
            }

            @Override
            public ResultVO<List<RoleDTO>> getUserRole(int userId) {
                return vo;
            }

            @Override
            public ResultVO resetPassword(int userId) {
                return vo;
            }

            @Override
            public ResultVO setNewPassword(Integer userId, String password) {
                return null;
            }

            @Override
            public ResultVO<UserDTO> getUserInfoByAccount(String account) {
                return vo;
            }

            @Override
            public ResultVO setPassword(PasswordDTO password) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UserLoginDTO>> findAll(PageInfo pageRequest) {
                return vo;
            }

            @Override
            public ResultVO<List<UserDTO>> getUserListByDepartmentId(int departmentId) {
                return vo;
            }

            @Override
            public void addOperLog(SysOperLogSaveDto sysOperLogSaveDto) {

            }
        };
    }
}
