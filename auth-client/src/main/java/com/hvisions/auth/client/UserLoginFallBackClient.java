package com.hvisions.auth.client;


import com.hvisions.auth.dto.user.UserLoginDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class UserLoginFallBackClient extends BaseFallbackFactory<UserLoginClient> {
    @Override
    public UserLoginClient getFallBack(ResultVO vo) {
        return new UserLoginClient() {
            @Override
            public ResultVO<HvPage<UserLoginDTO>> findAll(PageInfo pageRequest) {
                return vo;
            }

            @Override
            public ResultVO<UserLoginDTO> validAccount(String account, String password) {
                return vo;
            }

            /**
             * 锁定用户
             *
             * @param userId 用户id
             */
            @Override
            public ResultVO lockAccount(Integer userId) {
                return vo;
            }

            /**
             * 解除用户账户锁定
             *
             * @param userId 用户id
             */
            @Override
            public ResultVO unLockAccount(Integer userId) {
                return vo;
            }

            @Override
            public void addLogInLog(Integer userId, String fromPort) {

            }
        };
    }
}
