package com.hvisions.powder.sack.mapper;

import com.hvisions.powder.sack.dto.SackSpecificationPageDTO;
import com.hvisions.powder.sack.dto.SackSpecificationQueryDTO;
import com.hvisions.powder.sack.entity.SackSpecification;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_wp_sack_specification(袋装规格（新增）)】的数据库操作Mapper
* @createDate 2023-12-26 09:39:19
* @Entity com,hvisions.powder.sack.entity.SackSpecification
*/
@Mapper
public interface SackSpecificationMapper extends BaseMapper<SackSpecification> {

    /**
     * @描述: 分页查询袋装配置集合
     * @作者: 刘文勇
     * @日期: 2023/12/26 17:42
     * @参数: sackSpecificationQueryDTO
     * @返回值: org.springframework.data.domain.Page<com.hvisions.powder.sack.dto.SackSpecificationPageDTO>
     */
    List<SackSpecificationPageDTO> getSackSpecificationPageList(SackSpecificationQueryDTO sackSpecificationQueryDTO);

    /**
      * @描述: 根据曲粉获取当前生效大曲配送规格
      * @作者: 刘文勇
      * @日期: 2024/6/14 11:56
      * @参数: materialId
      * @返回值: java.math.BigDecimal
    */
    BigDecimal getDistribution(Integer materialId);
}




