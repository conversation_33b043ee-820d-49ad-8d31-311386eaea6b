package com.hvisions.powder.utils;

import cn.hutool.core.util.ObjectUtil;
import io.vavr.Tuple2;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-16 11:06
 */
public class LiquidToWeight {
	public static final Map<String, Tuple2<Double, Double>> CONVERT = new HashMap<>();
	static {
		CONVERT.put("1.8", new Tuple2<Double, Double>(62D, 62D));
		CONVERT.put("1.7", new Tuple2<Double, Double>(60D, 62D));
		CONVERT.put("1.6", new Tuple2<Double, Double>(56.5D, 60D));
		CONVERT.put("1.5", new Tuple2<Double, Double>(53D, 56.5D));
		CONVERT.put("1.4", new Tuple2<Double, Double>(49D, 53D));
		CONVERT.put("1.3", new Tuple2<Double, Double>(45D, 49D));
		CONVERT.put("1.2", new Tuple2<Double, Double>(41D, 45D));
		CONVERT.put("1.1", new Tuple2<Double, Double>(36.5D, 41D));
		CONVERT.put("1.0", new Tuple2<Double, Double>(31.5D, 36.5D));
		CONVERT.put("0.9", new Tuple2<Double, Double>(27D, 31.5D));
		CONVERT.put("0.8", new Tuple2<Double, Double>(22.5D, 27D));
		CONVERT.put("0.7", new Tuple2<Double, Double>(18.5D, 22.5D));
		CONVERT.put("0.6", new Tuple2<Double, Double>(14.5D, 18.5D));
		CONVERT.put("0.5", new Tuple2<Double, Double>(10.5D, 14.5D));
		CONVERT.put("0.4", new Tuple2<Double, Double>(7D, 10.5D));
		CONVERT.put("0.3", new Tuple2<Double, Double>(3.5D, 7D));
		CONVERT.put("0.2", new Tuple2<Double, Double>(1.5D, 3.5D));
		CONVERT.put("0.1", new Tuple2<Double, Double>(0.5D, 1.5D));
		CONVERT.put("0.0", new Tuple2<Double, Double>(0D, 0.5D));
	}

	public static BigDecimal convertToWeight(BigDecimal liquid) {
		final BigDecimal base = liquid.setScale(1, BigDecimal.ROUND_DOWN);
		final BigDecimal subtract = liquid.subtract(base);
		final Tuple2<Double, Double> convertMap = ObjectUtil.defaultIfNull(
				CONVERT.get(base.toString()), CONVERT.get("1.8"));
		return (BigDecimal.valueOf((convertMap._2() - convertMap._1())/0.1D)
						.multiply(subtract)
						.add(BigDecimal.valueOf(convertMap._1())))
						.divide(BigDecimal.valueOf(5), 6, BigDecimal.ROUND_UP)
						.multiply(BigDecimal.valueOf(1000)).setScale(0, BigDecimal.ROUND_DOWN);
	}
}
