package com.hvisions.powder.qudou.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.powder.qudou.dao.SceneDataMapper;
import com.hvisions.powder.qudou.entity.SceneData;
import com.hvisions.powder.qudou.service.ISceneDataService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 * 曲粉现场数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Service
public class SceneDataServiceImpl extends ServiceImpl<SceneDataMapper, SceneData> implements ISceneDataService {

    @Override
    public void handleSceneData(SceneData sceneData) {
        //如果当前车间已经存在接收记录   则进行数量累加计算
        LambdaQueryWrapper<SceneData> queryWrapper = new LambdaQueryWrapper<SceneData>()
                .eq(SceneData::getLocationId, sceneData.getLocationId())
                .eq(SceneData::getMaterialId, sceneData.getMaterialId());
        SceneData dbData = this.getOne(queryWrapper);
        if (Objects.isNull(dbData)) {
            baseMapper.insert(sceneData);
            return;
        }
        BigDecimal weight = dbData.getCurrentWeight().add(sceneData.getCurrentWeight());
        dbData.setCurrentWeight(weight);
        baseMapper.updateById(dbData);
    }
}
