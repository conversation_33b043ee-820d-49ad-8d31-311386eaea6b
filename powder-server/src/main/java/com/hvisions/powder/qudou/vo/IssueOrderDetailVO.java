/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.powder.qudou.vo;

import com.hvisions.powder.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @classname IssueOrderDetailVO
 * @description 日发放单详情视图实体类
 * <AUTHOR>
 * @date 2022-04-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("IssueOrderDetailVO对象")
public class IssueOrderDetailVO extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("日发放单子项id")
    private Integer itemId;

    @ApiModelProperty("曲斗id")
    private Integer parameterId;

    @ApiModelProperty("曲斗型号")
    private String parameterType;

    @ApiModelProperty("曲斗条码")
    private String barcode;

    @ApiModelProperty("曲粉批次")
    private String batch;

    @ApiModelProperty("每斗重量KG")
    private BigDecimal bucketWeight;

    @ApiModelProperty("发放人id")
    private Integer issuerId;

    @ApiModelProperty("发放人")
    private String issuer;

    @ApiModelProperty("发放时间")
    private LocalDateTime issueTime;

    @ApiModelProperty("接收中心")
    private String acceptCenter;

    @ApiModelProperty("接收车间")
    private String acceptLocation;

    @ApiModelProperty("接收人id")
    private Integer acceptorId;

    @ApiModelProperty("接收人")
    private String acceptor;

    @ApiModelProperty("接收时间")
    private LocalDateTime acceptTime;

    @ApiModelProperty("接收状态;0-新建、1-已接收、2-退换")
    private String acceptState;

    @ApiModelProperty("曲粉ID")
    private Integer materialId;

    @ApiModelProperty("中心ID")
    private Integer centerId;

    @ApiModelProperty("车辆状态 1-正常、2-检修")
    private Integer vehicleTransportState;

    @ApiModelProperty("车辆配置信息id")
    private Integer vehicleTransportId;

    @ApiModelProperty("车牌")
    private String licensePlateNumber;

    @ApiModelProperty("司机")
    private String driverName;

    @ApiModelProperty("包装方式 1--斗装，2--袋装")
    private String type;

    @ApiModelProperty("是否为散装数 0--否  1--是")
    private String isSurplus;

    @ApiModelProperty("是否为实验曲 0--否  1--是")
    private String experimentPower;

    @ApiModelProperty(value = "是否为科研曲 0-否、1-是")
    private String sciencePower;

    @ApiModelProperty(value = "袋装规格id")
    private Integer specificationId;

    @ApiModelProperty("散装数")
    private BigDecimal issueSurplusNumber;

    @ApiModelProperty("发放袋数或者斗数")
    private Integer issueNumber;

}
