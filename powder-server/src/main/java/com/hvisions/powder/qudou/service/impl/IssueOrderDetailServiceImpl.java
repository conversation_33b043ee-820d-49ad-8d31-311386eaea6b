package com.hvisions.powder.qudou.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hvisions.auth.client.BaseUserClient;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.brewage.client.DqDistributionClient;
import com.hvisions.brewage.dto.plan.dto.DqDistributionDTO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialQueryDTO;
import com.hvisions.powder.advice.UserAuditorAware;
import com.hvisions.powder.dto.kanBan.issue.WeightContrastExportDTO;
import com.hvisions.powder.dto.qudou.SapPostDTO;
import com.hvisions.powder.dto.qudou.demand.issue.order.detail.*;
import com.hvisions.powder.enums.IssueStateEnum;
import com.hvisions.powder.enums.QFAcceptStateEnum;
import com.hvisions.powder.enums.SapPostStateEnum;
import com.hvisions.powder.excel.AllocationExcel;
import com.hvisions.powder.qudou.dao.*;
import com.hvisions.powder.qudou.dto.*;
import com.hvisions.powder.qudou.entity.*;
import com.hvisions.powder.qudou.service.*;
import com.hvisions.powder.qudou.vo.DouIssueVO;
import com.hvisions.powder.qudou.vo.IssueOrderDetailVO;
import com.hvisions.powder.qudou.vo.IssueOrderItemVO;
import com.hvisions.powder.sack.entity.SackSpecification;
import com.hvisions.powder.sack.service.SackSpecificationService;
import com.hvisions.powder.salesOrder.dto.WeighDataDTO;
import com.hvisions.powder.sap.constant.SapConst;
import com.hvisions.powder.sap.dto.SapBaseOutputDto;
import com.hvisions.powder.sap.dto.SapBaseResponseDto;
import com.hvisions.powder.sap.dto.art.VehicleInfoSendArtDTO;
import com.hvisions.powder.sap.dto.inventory.InventoryAllocationDto;
import com.hvisions.powder.sap.dto.purchase.OrderWriteOffHeaderDto;
import com.hvisions.powder.sap.service.ArtService;
import com.hvisions.powder.sap.service.SapService;
import com.hvisions.powder.utils.CopyUtil;
import com.hvisions.powder.utils.DateUtil;
import com.hvisions.powder.utils.GenerateCodeUtil;
import com.hvisions.powder.utils.StringUtil;
import com.hvisions.powder.vehicleTransport.mapper.VehicleTransportMapper;
import com.hvisions.purchase.client.InventoryLocationClient;
import com.hvisions.purchase.client.SorghumLossClient;
import com.hvisions.purchase.dto.production.loss.sorghum.SorghumLossDTO;
import com.hvisions.purchase.dto.purchase.inventory.location.InventoryLocationPageDTO;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.hvisions.powder.consts.CommonConsts.LOGIN_HINT;


/**
 * <p>
 * 日发放单详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
@Slf4j
public class IssueOrderDetailServiceImpl extends ServiceImpl<IssueOrderDetailMapper, IssueOrderDetail> implements IIssueOrderDetailService {

    @Autowired
    IssueOrderDetailMapper issueOrderDetailMapper;
    @Autowired
    IssueOrderItemMapper issueOrderItemMapper;
    @Autowired
    IssueOrderMapper issueOrderMapper;
    @Autowired
    UserAuditorAware userAuditorAware;
    @Autowired
    IExceptionService exceptionService;
    @Autowired
    ParameterMapper parameterMapper;
    @Resource
    IIssueOrderDetailService issueOrderDetailService;

    @Autowired
    IParameterService parameterService;
    @Autowired
    BaseUserClient baseUserClient;
    @Autowired
    LocationExtendClient locationExtendClient;
    @Autowired
    MaterialClient materialClient;
    @Autowired
    @Lazy
    IIssueOrderService iIssueOrderService;
    @Autowired
    TransactionWrapperService transactionWrapperService;
    @Autowired
    ISceneDataService sceneDataService;
    @Autowired
    ISapPostService sapPostService;
    @Resource
    SapService sapService;

    @Resource
    InventoryLocationClient inventoryLocationClient;

    @Autowired
    SapPostMapper sapPostMapper;

    @Autowired
    GenerateCodeUtil generateCodeUtil;
    @Resource
    SingleDemandDetailMapper singleDemandDetailMapper;

    @Resource
    private SingleDemandMapper singleDemandMapper;

    @Resource
    private DqDistributionClient dqDistributionClient;

    @Resource
    VehicleTransportMapper vehicleTransportMapper;

    @Resource
    private SackSpecificationService sackSpecificationService;

    @Resource
    private ArtService artService;

    @Resource
    private SorghumLossClient sorghumLossClient;

    @Autowired
    private RedisTemplate redisTemplate;


    @Override
    public void finishIssueOrder(Integer issueId) {
        IssueOrder issueOrder = Optional.ofNullable(issueOrderMapper.selectById(issueId))
                .orElseThrow(() -> new BaseKnownException(415003, "未匹配到相关发放单"));
        //判断是否存在发放记录
        Optional.ofNullable(issueOrderDetailMapper.checkExistDetailInfo(issueId))
                .orElseThrow(() -> new BaseKnownException(415001, MessageFormat.format("【{0}】当前发放单还未进行发放操作", issueOrder.getDemandOrder())));
        //判断是否存在未接收或者退还的发放记录
//        Boolean orderFinishedFalse = issueOrderDetailMapper.checkIssueOrderFinished(issueId);
//        if (Objects.nonNull(orderFinishedFalse) && orderFinishedFalse) {
//            throw new BaseKnownException(415002, MessageFormat.format("【{0}】当前发放单存在未完成的发放数据", issueOrder.getDemandOrder()));
//        }
        issueOrderMapper.finishIssueOrder(issueId);
    }

    @Override
    public List<IssueOrderDetailVO> listDetailByItemId(Integer itemId) {
        return issueOrderDetailMapper.listDetailByItemId(itemId);
    }

    @Override
    public IssueOrderItemVO getDouInfoByCode(String douCode) {
        LambdaQueryWrapper<IssueOrderDetail> queryWrapper = new LambdaQueryWrapper<IssueOrderDetail>()
                .like(StringUtil.isNotEmpty(douCode), IssueOrderDetail::getBarcode, douCode)
                .eq(IssueOrderDetail::getAcceptState, QFAcceptStateEnum.NEW.getCode());
        List<IssueOrderDetail> issueOrderDetails = this.list(queryWrapper);
        Assert.notEmpty(issueOrderDetails, "未发现相关曲斗存在待接收的发货记录,请确认曲斗条码是否正确");
        Assert.isTrue(1 == issueOrderDetails.size(), "根据曲斗编码查到多条待接收的记录");
        IssueOrderDetail issueOrderDetail = issueOrderDetails.get(0);
        return Optional.ofNullable(issueOrderDetail.getItemId())
                .map(it -> issueOrderItemMapper.selectById(it))
                .map(it -> {
                    IssueOrderItemVO issueOrderItemVO = CopyUtil.simpleCopy(it, IssueOrderItemVO.class);
                    IssueOrderDetailVO issueOrderDetailVO = CopyUtil.simpleCopy(issueOrderDetail, IssueOrderDetailVO.class);
                    //查询曲斗型号
                    Optional.ofNullable(parameterMapper.selectById(issueOrderDetail.getParameterId()))
                            .ifPresent(p -> issueOrderDetailVO.setParameterType(p.getType()));
                    if (issueOrderDetail.getVehicleTransportId() != null && issueOrderDetail.getVehicleTransportId() > 0) {
                        //查询车辆信息
                        Optional.ofNullable(vehicleTransportMapper.getVehicleTransportsById(issueOrderDetail.getVehicleTransportId()))
                                .ifPresent(p -> {
                                    issueOrderDetailVO.setVehicleTransportId(p.getId());
                                    issueOrderDetailVO.setVehicleTransportState(p.getState());
                                    issueOrderDetailVO.setDriverName(p.getDriverName());
                                    issueOrderDetailVO.setLicensePlateNumber(p.getLicensePlateNumber());
                                });
                    }
                    issueOrderItemVO.setIssueOrderDetailVOList(new ArrayList<IssueOrderDetailVO>() {{
                        add(issueOrderDetailVO);
                    }});
                    return issueOrderItemVO;
                }).orElseGet(() -> {
                    //未匹配到发货单子项则把曲斗细心返回
                    IssueOrderDetailVO issueOrderDetailVO = CopyUtil.simpleCopy(issueOrderDetail, IssueOrderDetailVO.class);
                    //查询曲斗型号
                    Optional.ofNullable(parameterMapper.selectById(issueOrderDetail.getParameterId()))
                            .ifPresent(p -> issueOrderDetailVO.setParameterType(p.getType()));
                    if (issueOrderDetail.getVehicleTransportId() != null && issueOrderDetail.getVehicleTransportId() > 0) {
                        //查询车辆信息
                        Optional.ofNullable(vehicleTransportMapper.getVehicleTransportsById(issueOrderDetail.getVehicleTransportId()))
                                .ifPresent(p -> {
                                    issueOrderDetailVO.setVehicleTransportId(p.getId());
                                    issueOrderDetailVO.setVehicleTransportState(p.getState());
                                    issueOrderDetailVO.setDriverName(p.getDriverName());
                                    issueOrderDetailVO.setLicensePlateNumber(p.getLicensePlateNumber());
                                });
                    }
                    return new IssueOrderItemVO()
                            .setIssueOrderDetailVOList(new ArrayList<IssueOrderDetailVO>() {{
                                add(issueOrderDetailVO);
                            }});
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void acceptBatch(List<IssueOrderDetailAccept> detailIds, LocalDate acceptDate) {
        UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));
        //处理发货单相关
        List<IssueOrderDetail> issueOrderDetails = issueOrderDetailMapper.selectBatchIds(detailIds.stream().map(IssueOrderDetailAccept::getId).collect(Collectors.toList()));
        List<Integer> itemIds = issueOrderDetails.stream()
                .map(IssueOrderDetail::getItemId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemIds)) {
            throw new BaseKnownException(621008, "未匹配到相关发放单子项信息");
        }
        List<IssueOrderItem> issueOrderItems = issueOrderItemMapper.selectBatchIds(itemIds);
        List<Integer> orderIds = issueOrderItems.stream()
                .map(IssueOrderItem::getOrderId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new BaseKnownException(621007, "未匹配到相关发放单信息");
        }
        List<IssueOrder> issueOrders = issueOrderMapper.selectBatchIds(orderIds);
        Map<Integer, IssueOrderItem> itemMap = issueOrderItems.stream()
                .collect(Collectors.toMap(IssueOrderItem::getId, v -> v, (a, b) -> b));
        Map<Integer, IssueOrder> orderMap = issueOrders.stream()
                .collect(Collectors.toMap(IssueOrder::getId, v -> v));
        List<IssueOrderDetail> detailContainer = null;
        for (IssueOrderDetail issueOrderDetail : issueOrderDetails) {
            detailContainer = new ArrayList<IssueOrderDetail>();
            IssueOrderDetailAccept issueOrderDetailAccept1 = detailIds.stream().filter(issueOrderDetailAccept -> issueOrderDetailAccept.getId().equals(issueOrderDetail.getId())).findAny().orElse(new IssueOrderDetailAccept());
            Integer issueNumber = 0;
            //如果填写的袋数，则接受填写的袋数，如果没有袋数，接受剩余袋数
            if (StringUtil.isNotEmpty(issueOrderDetailAccept1.getActualNumber())) {
                issueNumber = issueOrderDetailAccept1.getActualNumber();
            } else{
                IssueOrderItem issueOrderItem = issueOrderItemMapper.selectById(issueOrderDetail.getItemId());
                if (issueOrderItem.getIssueNumber() == null || issueOrderItem.getActualNumber() == null) {
                    //异常数据保持原来的接受逻辑
                    issueNumber = issueOrderDetail.getIssueNumber();
                    log.info("接收发放的袋数，发放Id:" + issueOrderDetail.getId());
                } else {
                    //接受剩下的袋数
                    issueNumber = issueOrderItem.getIssueNumber().intValue() - issueOrderItem.getActualNumber().intValue();
                    log.info("未获取到袋数接收剩下袋数，发放Id:" + issueOrderDetail.getId());
                }
            }
            issueOrderDetail.setIssueNumber(issueNumber);
            detailContainer.add(issueOrderDetail);
            // 根据过账日期(曲粉接收时间)判定是否过账给sap
            ZoneId zoneId = ZoneId.systemDefault();
            Date postDate = Date.from(acceptDate.atStartOfDay().atZone(zoneId).toInstant());
            postDate = getPostDate(postDate);
            boolean isPost = sapService.judgeSapPostTime(postDate);
            String acceptCenter = detailContainer.get(0).getAcceptCenter();
            if (isPost) {
                if (!"706".equals(acceptCenter) && !"707".equals(acceptCenter)) {
                    handleReportSAP(userBaseDTO, detailContainer, itemMap, orderMap, postDate);
                }
            }
            //处理发放单相关记录
            transactionWrapperService.batchAcceptDetail(issueOrders, issueOrderItems, detailContainer);
            //处理库存
            handleLocationMaterialStorage(detailContainer, itemMap, orderMap);
        }
    }

    /**
     * 获取过账日期
     * @param from
     * @return
     */
    private Date getPostDate(Date from) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(from);
        int postDay = calendar.get(Calendar.DAY_OF_MONTH); // 当前过账时间日期
        // 如果过账日期 >= 24, 则将过账日期改成次月1号
        if (postDay >= 24){
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date postTime = calendar.getTime();
            log.info("过账日期 >= 24,更改过账日期为次月1号:" + DateUtil.dateFormat(postTime));
            return postTime;
        }
        return from;
    }

    /***
     * @Description 每月1号12点调用，将上月未过账曲粉发放的数据的发放数据同步给sap
     *
     * <AUTHOR>
     * @Date 2022-12-15 14:32
     * @param
     * @return void
     **/
    @Scheduled(cron = "0 0 12 1 * ?")
    @SchedulerLock(name = "scheduledQfSyncSapLock")
    public void scheduledQfSyncSap() {

        log.info("曲粉1号定时同步sap数据开始==");

        Date yearTime = sapService.isYearPost();

        List<IssueOrderDetailVO> notSyncSapIssueOrderDetailList;
        if (yearTime != null) {
            // 获取年度扎帐日期到本月1号未过账的曲粉发放记录
            notSyncSapIssueOrderDetailList = issueOrderDetailMapper.getNotSyncSapIssueOrderDetailList(yearTime, DateUtil.getAppointDay(new Date(), 1));
            log.info("曲粉年度未过账数量==>" + notSyncSapIssueOrderDetailList.size());

        } else {
            // 获取上个月24到本月1号
            notSyncSapIssueOrderDetailList = issueOrderDetailMapper.getNotSyncSapIssueOrderDetailList(DateUtil.getUpMonthAppointDay(24), DateUtil.getAppointDay(new Date(), 1));
            log.info("曲粉月度未过账数量==>" + notSyncSapIssueOrderDetailList.size());
        }

        notSyncSapIssueOrderDetailList.forEach(it -> {

            IssueOrderItem item = issueOrderItemMapper.selectById(it.getItemId());
            IssueOrder order = issueOrderMapper.selectById(item.getOrderId());

            // 获取sap库存列表
            ResultVO<List<InventoryLocationPageDTO>> resultVO = inventoryLocationClient.getInventoryLocationListByCenterId(it.getCenterId());
            if (resultVO.getCode() != 200 || resultVO.getData().size() == 0) {
                throw new BaseKnownException(10000, "当前中心未维护sap库存地址");
            }
            InventoryLocationPageDTO locationPageDTO = resultVO.getData().get(0);

            SapPostDTO sapPostDTO = new SapPostDTO()
                    .setMaterialId(item.getMaterialId())
                    .setMaterialName(item.getMaterialName())
                    .setMaterialCode(item.getMaterialCode())
                    .setUnit("kg")
                    .setWeight(it.getBucketWeight())
                    .setInitialWarehouseCode("1397")
                    .setType(0)
                    .setTargetWarehouseCode(locationPageDTO.getCode())
                    .setMovementTypeId(311)
                    .setState(SapPostStateEnum.SAP_PROCESSING.getCode())
                    .setOrderDetailId(it.getId())
                    .setSapCode("MESNJ016")
                    .setOperatorType("曲粉接收")
                    .setCertificateDate(LocalDate.now())
                    .setOperatingTime(LocalDateTime.now());
            if ("1".equals(it.getType())) {
                sapPostDTO.setWeight(it.getBucketWeight());
            } else {
                if ("1".equals(it.getIsSurplus())) {
                    sapPostDTO.setWeight(it.getIssueSurplusNumber());
                } else {
                    SackSpecification byId = sackSpecificationService.getById(it.getSpecificationId());
                    sapPostDTO.setWeight(BigDecimal.valueOf((long) byId.getWeight() * it.getIssueNumber()));
                }
            }
            if (Objects.nonNull(order)) {
                sapPostDTO.setCenterId(order.getCenterId())
                        .setLocationId(order.getLocationId())
                        .setTargetWarehouseCode(locationPageDTO.getCode())
                ;
            }
            SapBaseResponseDto sapBaseResponseDto = sapPostService.acceptAndSend(sapPostDTO);
            //生成高粱损耗记录
            log.info("sapBaseResponseDto:{}",sapBaseResponseDto);
            if(!"E".equals(sapBaseResponseDto.getEsMessage().getMsgty())){
                try {
                    SorghumLossDTO sorghumLossDTO = new SorghumLossDTO();
                    Calendar calendar = Calendar.getInstance();
                    int postDay = calendar.get(Calendar.DAY_OF_MONTH);
                    if(postDay >= 24){
                        calendar.set(Calendar.DAY_OF_MONTH,24);
                        sorghumLossDTO.setActualBeginTime(calendar.getTime());
                        calendar.set(Calendar.DAY_OF_MONTH,23);
                        int nowMonth = calendar.get(Calendar.MONTH) +1;
                        calendar.set(Calendar.MONTH,nowMonth+1);
                        sorghumLossDTO.setActualEndTime(calendar.getTime());
                    }else {
                        calendar.set(Calendar.DAY_OF_MONTH,23);
                        sorghumLossDTO.setActualEndTime(calendar.getTime());
                        calendar.set(Calendar.DAY_OF_MONTH,24);
                        int nowMonth = calendar.get(Calendar.MONTH) +1;
                        calendar.set(Calendar.MONTH,nowMonth-1);
                        sorghumLossDTO.setActualBeginTime(calendar.getTime());
                    }
                    sorghumLossDTO.setCenterId(order.getCenterId());
                    sorghumLossDTO.setCenter(order.getCenterName());
                    sorghumLossDTO.setMaterialId(item.getMaterialId());
                    sorghumLossDTO.setMaterialCode(item.getMaterialCode());
                    sorghumLossDTO.setMaterialName(item.getMaterialName());
                    sorghumLossDTO.setUnit("kg");
                    sorghumLossDTO.setIssueQuantity(sapPostDTO.getWeight());
                    sorghumLossDTO.setSendSiloId(13);
                    sorghumLossDTO.setSendSilo("1397");
                    sorghumLossDTO.setAcceptSiloId(locationPageDTO.getId());
                    sorghumLossDTO.setAcceptSilo(locationPageDTO.getName());
                    log.info("生成高粱损耗记录");
                    sorghumLossClient.insertOrUpdateSorghumLoss(sorghumLossDTO);
                }catch (Exception e){
                    log.error("生成高粱损耗记录失败");
                    e.printStackTrace();
                }
            }

        });
        log.info("曲粉1号sap同步结束");
    }

    /**
     * @param userBaseDTO
     * @param issueOrderDetails
     * @param itemMap
     * @param orderMap
     * @param postDate
     * @return void
     * @Description 曲粉接收同步sap
     * <AUTHOR>
     * @Date 2024-6-5 16:47
     **/
    private void handleReportSAP(UserBaseDTO userBaseDTO, List<IssueOrderDetail> issueOrderDetails, Map<Integer, IssueOrderItem> itemMap, Map<Integer, IssueOrder> orderMap, Date postDate) {

        //获取目标中心对应的仓库
        Integer acceptCenter = issueOrderDetails.get(0).getCenterId();
        // 获取sap库存列表
        List<InventoryLocationPageDTO> inventoryLocationListByCenterId = sapPostMapper.getInventoryLocationListByCenterId(acceptCenter);
//        ResultVO<List<InventoryLocationPageDTO>> resultVO = inventoryLocationClient.getInventoryLocationListByCenterId(acceptCenter);
        if (inventoryLocationListByCenterId.isEmpty()) {
            throw new BaseKnownException(10000, "当前中心未维护sap库存地址");
        }
        InventoryLocationPageDTO locationPageDTO = inventoryLocationListByCenterId.get(0);
        for (IssueOrderDetail it : issueOrderDetails) {
            Integer itemId = it.getItemId();
            IssueOrderItem item = itemMap.get(itemId);
            if (Objects.isNull(item)) {
                log.info(MessageFormat.format("【{0}】未匹配到对应的子项数据", itemId));
                return;
            }
            LocalDateTime postDateTime = LocalDateTime.ofInstant(postDate.toInstant(), ZoneId.systemDefault());
            IssueOrder order = orderMap.get(item.getOrderId());
            SapPostDTO sapPostDTO = new SapPostDTO()
                    .setMaterialId(item.getMaterialId())
                    .setMaterialName(item.getMaterialName())
                    .setMaterialCode(item.getMaterialCode())
                    .setUnit("kg")
                    .setWeight(it.getBucketWeight())
                    .setInitialWarehouseCode("1397")
                    .setType(0)
                    .setTargetWarehouseCode(locationPageDTO.getCode())
                    .setMovementTypeId(311)
                    .setState(SapPostStateEnum.SAP_PROCESSING.getCode())
                    .setOrderDetailId(it.getId())
                    .setOperator(userBaseDTO.getUserName())
                    .setOperatorId(userBaseDTO.getId())
                    .setSapCode("MESNJ016")
                    .setOperatorType("曲粉接收")
                    .setOperatingTime(postDateTime);
            if ("1".equals(it.getType())) {
                sapPostDTO.setWeight(it.getBucketWeight());
            } else {
                if ("1".equals(it.getIsSurplus())) {
                    sapPostDTO.setWeight(it.getIssueSurplusNumber());
                } else {
                    SackSpecification byId = sackSpecificationService.getById(it.getSpecificationId());
                    sapPostDTO.setWeight(BigDecimal.valueOf((long) byId.getWeight() * it.getIssueNumber()));
                }
            }
            if (Objects.nonNull(order)) {

                sapPostDTO.setCenterId(order.getCenterId())
                        .setLocationId(order.getLocationId())
                        .setTargetWarehouseCode(locationPageDTO.getCode());
            }
            SapBaseResponseDto sapBaseResponseDto = null;
            try {
                sapBaseResponseDto = sapPostService.acceptAndSend(sapPostDTO);
            } catch (Exception e) {
                log.error("曲粉过账调拨失败:" + e.getMessage());
            }
            //过账成功生成高粱损耗记录
            //先校验24号获取开始时间和结束时间 >=24号  --> 当月24日-下月23日    <24号  -->上月24日-当月23日
            log.info("sapBaseResponseDto:{}",sapBaseResponseDto);
            if(sapBaseResponseDto != null && !"E".equals(sapBaseResponseDto.getEsMessage().getMsgty())){
                try {
                    SorghumLossDTO sorghumLossDTO = new SorghumLossDTO();
                    Calendar calendar = Calendar.getInstance();
                    int postDay = calendar.get(Calendar.DAY_OF_MONTH);
                    if(postDay >= 24){
                        calendar.set(Calendar.DAY_OF_MONTH,24);
                        sorghumLossDTO.setActualBeginTime(calendar.getTime());
                        calendar.set(Calendar.DAY_OF_MONTH,23);
                        int nowMonth = calendar.get(Calendar.MONTH);
                        calendar.set(Calendar.MONTH,nowMonth+1);
                        sorghumLossDTO.setActualEndTime(calendar.getTime());
                    }else {
                        calendar.set(Calendar.DAY_OF_MONTH,23);
                        sorghumLossDTO.setActualEndTime(calendar.getTime());
                        calendar.set(Calendar.DAY_OF_MONTH,24);
                        int nowMonth = calendar.get(Calendar.MONTH);
                        calendar.set(Calendar.MONTH,nowMonth-1);
                        sorghumLossDTO.setActualBeginTime(calendar.getTime());
                    }
                    sorghumLossDTO.setCenterId(acceptCenter);
                    sorghumLossDTO.setCenter(issueOrderDetails.get(0).getAcceptCenter());
                    sorghumLossDTO.setMaterialId(item.getMaterialId());
                    sorghumLossDTO.setMaterialCode(item.getMaterialCode());
                    sorghumLossDTO.setMaterialName(item.getMaterialName());
                    sorghumLossDTO.setUnit("kg");
                    sorghumLossDTO.setIssueQuantity(sapPostDTO.getWeight());
                    sorghumLossDTO.setSendSiloId(13);
                    sorghumLossDTO.setSendSilo("1397");
                    sorghumLossDTO.setAcceptSiloId(locationPageDTO.getId());
                    sorghumLossDTO.setAcceptSilo(locationPageDTO.getName());
                    log.info("生成高粱损耗记录");
                    sorghumLossClient.insertOrUpdateSorghumLoss(sorghumLossDTO);
                }catch (Exception e){
                    log.error("生成高粱损耗记录失败：" + e.getMessage());
                }
            }
        }
    }

    private void handleLocationMaterialStorage(List<IssueOrderDetail> issueOrderDetails, Map<Integer, IssueOrderItem> itemMap, Map<Integer, IssueOrder> orderMap) {
        issueOrderDetails.stream()
                .collect(Collectors.groupingBy(IssueOrderDetail::getMaterialId))
                .values()
                .forEach(it -> {
                    Integer itemId = it.get(0).getItemId();
                    IssueOrderItem item = itemMap.get(itemId);
                    if (Objects.isNull(item)) {
                        log.warn(MessageFormat.format("【{0}】未匹配到对应的子项数据", itemId));
                        return;
                    }
                    IssueOrder order = orderMap.get(item.getOrderId());
                    if (Objects.isNull(order)) {
                        log.warn(MessageFormat.format("【{0}】未匹配到对应的发货单数据", item.getOrderId()));
                        return;
                    }
                    BigDecimal weight = new BigDecimal("0");
                    if ("1".equals(order.getType())) {
                        //累加斗装发放量
                        weight = it.stream()
                                .map(IssueOrderDetail::getBucketWeight)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO);
                    } else if ("2".equals(order.getType())) {
                        BigDecimal bigDecimal = new BigDecimal("0");
                        SackSpecification specification = sackSpecificationService.getById(order.getSpecificationId());
                        if (StringUtil.isNotEmpty(specification) && StringUtil.isNotEmpty(specification.getWeight())) {
                            //累加袋装发放数量  袋数 * 规格 + 散装数
                            for (IssueOrderDetail issueOrderDetail : it) {
                                if (StringUtil.isNotEmpty(issueOrderDetail.getIssueNumber())) {
                                    bigDecimal = bigDecimal.add(BigDecimal.valueOf((long) issueOrderDetail.getIssueNumber() * specification.getWeight()));
                                }
                                if (StringUtil.isNotEmpty(issueOrderDetail.getIssueSurplusNumber())) {
                                    bigDecimal = bigDecimal.add(issueOrderDetail.getIssueSurplusNumber());
                                }
                            }
                        }
                        weight = bigDecimal;
                    }
                    SceneData sceneData = new SceneData()
                            .setMaterialId(item.getMaterialId())
                            .setMaterialName(item.getMaterialName())
                            .setMaterialCode(item.getMaterialCode())
                            .setCurrentWeight(weight)
                            .setCenterId(order.getCenterId())
                            .setCenterName(order.getCenterName())
                            .setLocationId(order.getLocationId())
                            .setLocationName(order.getLocationName());
                    sceneDataService.handleSceneData(sceneData);
                });
    }

    @Override
    public void acceptPoolBatch(List<IssueOrderDetailAccept> detailIds, String centerCode, String lineName, String location, String acceptDate) {
        //增加超收判断
        List<String> collect = detailIds.stream().filter(d -> d.getId() != null).map(IssueOrderDetailAccept::getId).map(Object::toString).collect(Collectors.toList());
        collect.add(centerCode);
        collect.add(lineName);
        collect.add(location);
        collect.add(acceptDate);
        String key = "powderAccept:" + String.join(",",collect);
        //数据校验
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(key, 1))) {
            throw new BaseKnownException(10000, "曲粉接收的数据项已提交，请不要重复提交");
        }
        redisTemplate.expire(key, 2, TimeUnit.MINUTES);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(acceptDate, fmt);
        //获取发货单
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        boolean checkLine = pattern.matcher(centerCode).matches() && Integer.parseInt(centerCode) >= 709 && Integer.parseInt(centerCode) <= 719;
        List<IssueOrderDTO> issueOrders = iIssueOrderService.matchSuitOrder(centerCode, lineName, location, date, checkLine);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(issueOrders)) {
            throw new BaseKnownException(419010, "未匹配到符合条件的发放单");
        }
        //处理无发货单子项的
        List<IssueOrderDetail> issueOrderDetails = issueOrderDetailMapper.selectBatchIds(detailIds.stream().map(IssueOrderDetailAccept::getId).collect(Collectors.toList()));

        //存在任意发放详情已经完成接收则失败
        boolean existAccepted = issueOrderDetails.stream()
                .anyMatch(it -> QFAcceptStateEnum.ACCEPT.getCode().equals(it.getAcceptState()));
        Assert.isTrue(!existAccepted, "存在已接收的曲斗，请勿重复接收");
        issueOrderDetails.stream().forEach(issueOrderDetail -> {
            IssueOrderDTO issueOrderDTO;
            //匹配发货单
            if ("2".equals(issueOrderDetail.getType())) {
                //袋装匹配
                if (StringUtil.isNotEmpty(issueOrderDetail.getIssueSurplusNumber()) && issueOrderDetail.getIssueSurplusNumber().compareTo(new BigDecimal("0")) > 0) {
                    //散装匹配
                    issueOrderDTO = issueOrders.stream().filter(item -> StringUtil.isNotEmpty(item.getMaterialId()) && item.getMaterialId().equals(issueOrderDetail.getMaterialId()) && "2".equals(item.getType()) && StringUtil.isEmpty(item.getBucketWeight()) && StringUtil.isEmpty(item.getSpecificationId()) && item.getExperimentPower().equals(issueOrderDetail.getExperimentPower()) && item.getSciencePower().equals(issueOrderDetail.getSciencePower()) && StringUtil.isNotEmpty(item.getSurplusNumber()) && (item.getSurplusNumber().compareTo(issueOrderDetail.getIssueSurplusNumber()) == 0)).findFirst().orElse(null);
                } else {
                    //整袋匹配
                    issueOrderDTO = issueOrders.stream().filter(item -> "2".equals(item.getType()) && item.getMaterialId().equals(issueOrderDetail.getMaterialId()) && StringUtil.isNotEmpty(item.getSpecificationId()) && item.getSpecificationId().equals(issueOrderDetail.getSpecificationId()) && item.getExperimentPower().equals(issueOrderDetail.getExperimentPower()) && item.getSciencePower().equals(issueOrderDetail.getSciencePower())).findFirst().orElse(null);
                }
            } else {
                //斗装匹配
                issueOrderDTO = issueOrders.stream().filter(item -> "1".equals(item.getType()) && item.getMaterialId().equals(issueOrderDetail.getMaterialId()) && StringUtil.isNotEmpty(item.getBucketWeight()) && item.getBucketWeight().equals(issueOrderDetail.getBucketWeight()) && item.getExperimentPower().equals(issueOrderDetail.getExperimentPower()) && item.getSciencePower().equals(issueOrderDetail.getSciencePower())).findFirst().orElse(null);
            }
            if (StringUtil.isNotEmpty(issueOrderDTO)) {
                LambdaQueryWrapper<IssueOrderItem> queryWrapper = new LambdaQueryWrapper<IssueOrderItem>()
                        .eq(IssueOrderItem::getOrderId, issueOrderDTO.getId());
                Map<Integer, Integer> groupByMaterial = issueOrderItemMapper.selectList(queryWrapper).stream()
                        .collect(Collectors.toMap(IssueOrderItem::getMaterialId, IssueOrderItem::getId, (a, b) -> b));

                //处理接收中心信息
                issueOrderDetail.setItemId(groupByMaterial.get(issueOrderDetail.getMaterialId()));
                issueOrderDetail.setAcceptCenter(issueOrderDTO.getCenterName());
                issueOrderDetail.setAcceptLocation(issueOrderDTO.getLocationName());
                issueOrderDetail.setCenterId(issueOrderDTO.getCenterId());
                issueOrderDetail.setLocationId(issueOrderDTO.getLocationId());
                issueOrderDetail.setAcceptLine(issueOrderDTO.getLineName());
                issueOrderDetailMapper.updateById(issueOrderDetail);
                //处理发放单状态 更新状态为进行中
                IssueOrder issueOrder = CopyUtil.simpleCopy(issueOrderDTO, IssueOrder.class);
                issueOrder.setState(IssueStateEnum.PROCESSING.getCode());
                iIssueOrderService.updateById(issueOrder);
            }
        });
        //统一处理收货人以及收货状态
        this.acceptBatch(detailIds, date);
    }

    @Override
    public void raiseException(ReceiptException receiptException) {
        IssueOrderDetail issueOrderDetail = Optional.ofNullable(receiptException.getDetailId())
                .map(it -> issueOrderDetailMapper.selectById(it))
                .orElseThrow(() -> new BaseKnownException(423020, "未查询到相关信息"));
        Assert.isTrue(!QFAcceptStateEnum.RAISE_EXCEPTION.getCode().equals(issueOrderDetail.getAcceptState()), "请勿重复发起异常申请");

        if (QFAcceptStateEnum.ACCEPT.getCode().equals(issueOrderDetail.getAcceptState())) {
            //已接收发起异常  处理发货单关联表数据
            this.handleRaiseException(receiptException.getDetailId());
        } else {
            //未接收发起异常
            issueOrderDetail.setStateBeforeException(issueOrderDetail.getAcceptState());
            issueOrderDetail.setAcceptState(QFAcceptStateEnum.RAISE_EXCEPTION.getCode());
            issueOrderDetailMapper.updateById(issueOrderDetail);
        }
        //发起异常
        exceptionService.handleDetailException(receiptException);
    }

    @Override
    public void batchRaiseException(List<ReceiptException> receiptExceptions) {
        receiptExceptions.forEach(this::raiseException);
    }

    @Override
    public Page<DouIssueVO> queryPagedIssueOrderDetail(IssueOrderDetailQueryReq req) {
        if (req.getSubmitStart() != null && req.getSubmitEnd() != null) {
            req.setStart(DateUtil.getBeginLocalDate(req.getSubmitStart()));
            req.setEnd(DateUtil.getEndLocalDate(req.getSubmitEnd()));
        }
        Page<DouIssueVO> douIssueVOPage = new Page<>();
        //有的页面page传0
        if (req.getPage() == 0) {
            req.setPage(1);
        }
        req.setPage((req.getPage() - 1) * req.getPageSize());
        List<DouIssueVO> douIssueVOS = issueOrderDetailMapper.queryPagedIssueOrderDetail(req);
        douIssueVOPage.setTotal(issueOrderDetailMapper.queryPagedIssueOrderDetailCount(req));
        douIssueVOPage.setSize(req.getPageSize());
        douIssueVOPage.setPages((douIssueVOPage.getTotal() + douIssueVOPage.getSize() - 1) / douIssueVOPage.getSize());
        if (CollectionUtils.isEmpty(douIssueVOS)) {
            return douIssueVOPage;
        }
        //查询曲斗状态
        List<String> barcodes = douIssueVOS.stream().map(DouIssueVO::getBarcode).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<Parameter> parameterQuery = new LambdaQueryWrapper<Parameter>()
                .in(Parameter::getBarcode, barcodes);
        Map<String, Boolean> emptyStateMap = parameterMapper.selectList(parameterQuery).stream()
                .collect(Collectors.toMap(Parameter::getBarcode, Parameter::getEmptyState, (a, b) -> b));
        douIssueVOS.forEach(it -> it.setEmptyState(emptyStateMap.get(it.getBarcode())));
        douIssueVOPage.setRecords(douIssueVOS);
        //处理曲粉编码和曲粉名称
        List<Integer> materialIds = douIssueVOS.stream()
                .map(DouIssueVO::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        Optional.ofNullable(materialClient.getMaterialsByIdList(materialIds))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .map(it -> it.stream().collect(Collectors.toMap(MaterialDTO::getId, v -> v, (a, b) -> b)))
                .ifPresent(it -> {
                    douIssueVOS.forEach(vo -> {
                        MaterialDTO materialDTO = it.get(vo.getMaterialId());
                        if (Objects.isNull(materialDTO)) {
                            vo.setMaterialName(Strings.EMPTY);
                            vo.setMaterialCode(Strings.EMPTY);
                            return;
                        }
                        vo.setMaterialCode(materialDTO.getMaterialCode());
                        vo.setMaterialName(materialDTO.getMaterialName());
                    });
                });
        return douIssueVOPage;
    }

    @Override
    public void handleRaiseException(Integer detailId) {
        this.handleExceptionCommon(detailId, QFAcceptStateEnum.RAISE_EXCEPTION.getCode(), new BigDecimal("-1"));
    }

    @Override
    public void handleSolveException(Integer detailId, String acceptState) {
        this.handleExceptionCommon(detailId, acceptState, BigDecimal.ONE);
    }

    @Override
    public void recoverAcceptState(Integer detailId) {
        IssueOrderDetail issueOrderDetail = issueOrderDetailMapper.selectById(detailId);
        issueOrderDetail.setAcceptState(issueOrderDetail.getStateBeforeException());
        issueOrderDetailMapper.updateById(issueOrderDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIssueRecord(Integer detailId) {
        IssueOrderDetail issueOrderDetail = issueOrderDetailMapper.selectById(detailId);
        Assert.isTrue(QFAcceptStateEnum.NEW.getCode().equals(issueOrderDetail.getAcceptState()), "删除失败，当前发放记录非新建状态");
        //如果是绑定item记录  扣减发放斗数
        if (Objects.nonNull(issueOrderDetail.getItemId())) {
            IssueOrderItem issueOrderItem = issueOrderItemMapper.selectById(issueOrderDetail.getItemId());
            issueOrderItem.setIssueNumber(issueOrderItem.getIssueNumber().subtract(BigDecimal.ONE));
            issueOrderItemMapper.updateById(issueOrderItem);
        }
        issueOrderDetailMapper.deleteById(detailId);
    }

    @Override
    public IssueOrderDetailEdit getEditDetail(Integer detailId) {
        return Optional.ofNullable(issueOrderDetailMapper.selectById(detailId))
                .map(detail -> {
                    Integer itemId = detail.getItemId();
                    IssueOrderDetailEdit issueOrderDetailEdit = new IssueOrderDetailEdit();
                    issueOrderDetailEdit.setMaterialId(detail.getMaterialId());
                    issueOrderDetailEdit.setDetailId(detail.getId());
                    issueOrderDetailEdit.setBarcode(detail.getBarcode());
                    issueOrderDetailEdit.setBatch(detail.getBatch());
                    issueOrderDetailEdit.setBucketWeight(detail.getBucketWeight());
                    issueOrderDetailEdit.setParameterId(detail.getParameterId());
                    issueOrderDetailEdit.setVehicleTransportId(detail.getVehicleTransportId());
                    issueOrderDetailEdit.setIssueNumber(detail.getIssueNumber());
                    issueOrderDetailEdit.setIssueSurplusNumber(detail.getIssueSurplusNumber());
                    issueOrderDetailEdit.setType(detail.getType());
                    issueOrderDetailEdit.setIsSurplus(detail.getIsSurplus());
                    Optional.ofNullable(itemId)
                            .map(item -> issueOrderItemMapper.selectById(itemId))
                            .ifPresent(it -> {
                                issueOrderDetailEdit.setItemId(itemId);
                                issueOrderDetailEdit.setMaterialCode(it.getMaterialCode());
                                issueOrderDetailEdit.setMaterialName(it.getMaterialName());
                            });
                    return issueOrderDetailEdit;
                }).orElseThrow(() -> new BaseKnownException(614001, "未查到相关详情信息，请确认入参是否正确"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editIssueRecord(IssueOrderDetailEdit issueOrderDetailEdit) {
        //处理detail
        IssueOrderDetail issueOrderDetail = issueOrderDetailMapper.selectById(issueOrderDetailEdit.getDetailId());
        issueOrderDetail.setMaterialId(issueOrderDetailEdit.getMaterialId());
        issueOrderDetail.setBarcode(issueOrderDetailEdit.getBarcode());
        issueOrderDetail.setBatch(issueOrderDetailEdit.getBatch());
        issueOrderDetail.setBucketWeight(issueOrderDetailEdit.getBucketWeight());
        issueOrderDetail.setParameterId(issueOrderDetailEdit.getParameterId());
        issueOrderDetail.setVehicleTransportId(issueOrderDetailEdit.getVehicleTransportId());
        issueOrderDetail.setIssueNumber(issueOrderDetailEdit.getIssueNumber());
        //处理item
        Integer itemId = issueOrderDetailEdit.getItemId();
        Integer materialId = issueOrderDetailEdit.getMaterialId();
        if (Objects.nonNull(itemId)) {
            IssueOrderItem issueOrderItem = issueOrderItemMapper.selectById(itemId);
            //如果物料变了   则需要修改该发放单下对应的item
            if (!issueOrderItem.getMaterialId().equals(materialId)) {
                LambdaQueryWrapper<IssueOrderItem> itemQueryByOrder = new LambdaQueryWrapper<IssueOrderItem>()
                        .eq(IssueOrderItem::getOrderId, issueOrderItem.getOrderId());
                Optional<IssueOrderItem> suitItem = issueOrderItemMapper.selectList(itemQueryByOrder).stream()
                        .filter(it -> it.getMaterialId().equals(materialId))
                        .findFirst();
                //存在该种曲粉的item
                if (suitItem.isPresent()) {
                    //根据物料绑定item
                    IssueOrderItem suitItemGet = suitItem.get();
                    issueOrderDetail.setItemId(suitItemGet.getId());
                    suitItemGet.setIssueNumber(suitItemGet.getIssueNumber().add(BigDecimal.ONE));
                    issueOrderItemMapper.updateById(suitItemGet);
                    //处理旧的item
                    issueOrderItem.setIssueNumber(suitItemGet.getIssueNumber().subtract(BigDecimal.ONE));
                    issueOrderItemMapper.updateById(issueOrderItem);
                } else {
                    //不存在则需要新建一条
                    IssueOrderItem newItem = new IssueOrderItem();
                    newItem.setOrderId(issueOrderItem.getOrderId());
                    newItem.setMaterialId(materialId);
                    newItem.setMaterialCode(issueOrderDetailEdit.getMaterialCode());
                    newItem.setMaterialName(issueOrderDetailEdit.getMaterialName());
                    newItem.setDemandNumber(issueOrderItem.getDemandNumber());
                    newItem.setIssueNumber(BigDecimal.ONE);
                    newItem.setActualNumber(BigDecimal.ZERO);
                    issueOrderItemMapper.insert(newItem);
                    issueOrderDetail.setItemId(newItem.getId());
                }
            }
        }
        //处理detail
        issueOrderDetailMapper.updateById(issueOrderDetail);
    }

    @Override
    public TransferReportDTO getTransferReport(ReportReq req) {
        LocalDateTime start = Optional.ofNullable(req.getBeginDate())
                .map(it -> LocalDateTime.of(DateUtil.parse2LocalDate(it, DateUtil.LD_PATTERN), LocalTime.MIN))
                .orElse(null);

        LocalDateTime end = Optional.ofNullable(req.getEndDate())
                .map(it -> LocalDateTime.of(DateUtil.parse2LocalDate(it, DateUtil.LD_PATTERN), LocalTime.of(23, 59, 59)))
                .orElse(null);

        LambdaQueryWrapper<SapPost> wrapper = new LambdaQueryWrapper<SapPost>()
                .eq(SapPost::getType, 0)
                .eq(SapPost::getState, 1)
                .eq(SapPost::getInitialWarehouseCode, 1397)
                .between(SapPost::getCertificateDate, start, end);
        List<SapPost> list = sapPostMapper.selectList(wrapper);
        List<TransferRecord> transferRecords = issueOrderDetailMapper.listTransferRecord(start, end);
        //查询物料信息
        List<Integer> materialIds = list.stream()
                .map(SapPost::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, MaterialDTO> materialMap = Optional.ofNullable(materialClient.getMaterialsByIdList(materialIds))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .map(it -> it.stream().collect(Collectors.toMap(MaterialDTO::getId, v -> v, (a, b) -> b)))
                .orElse(new HashMap<>());
        //按照中心分组
        List<TransferReportDTO.TransferReportItem> transferReportItems =
                list.stream()
                        .collect(Collectors.groupingBy(SapPost::getTargetWarehouseCode))
                        .entrySet()
                        .stream()
                        .map(byCenter -> {
                            //按照物料分组聚合
                            Map<Integer, List<SapPost>> byMaterial = byCenter.getValue()
                                    .stream()
                                    .collect(Collectors.groupingBy(SapPost::getMaterialId));
                            HashMap<String, String> materialItemMap = new HashMap<>(byMaterial.size());
                            for (List<SapPost> value : byMaterial.values()) {
                                BigDecimal totalByMaterial = value
                                        .stream()
                                        .map(SapPost::getWeight)
                                        .reduce(BigDecimal::add)
                                        .orElse(BigDecimal.ZERO);

                                Optional.ofNullable(materialMap.get(value.get(0).getMaterialId()))
                                        .ifPresent(it -> materialItemMap.put(it.getMaterialName(), totalByMaterial.intValue() + "/" + value.stream().filter(f -> f.getCenterId() != null).collect(Collectors.toList()).size()));
                            }
                            TransferReportDTO.TransferReportItem transferReportItem = new TransferReportDTO.TransferReportItem();
                            Integer centerId = byCenter.getValue().get(0).getCenterId();
                            transferReportItem.setCenterId(centerId);
                            ResultVO<LocationDTO> resultVO = locationExtendClient.getLocationById(centerId);
                            if (resultVO.isSuccess()) {
                                transferReportItem.setCenterName(resultVO.getData().getCode());
                            }
                            transferReportItem.setMaterialReport(materialItemMap);
                            return transferReportItem;
                        }).collect(Collectors.toList());
        TransferReportDTO transferReportDTO = new TransferReportDTO();
        transferReportDTO.setItems(transferReportItems);
        return transferReportDTO;
    }

    @Override
    public TransferReportDTO getApplyReport(ReportReq req) {
        LocalDateTime start = Optional.ofNullable(req.getBeginDate())
                .map(it -> LocalDateTime.of(DateUtil.parse2LocalDate(it, DateUtil.LD_PATTERN), LocalTime.MIN))
                .orElseThrow(() -> new BaseKnownException(10000, "请选择开始日期"));

        LocalDateTime end = Optional.ofNullable(req.getEndDate())
                .map(it -> LocalDateTime.of(DateUtil.parse2LocalDate(it, DateUtil.LD_PATTERN), LocalTime.of(23, 59, 59)))
                .orElseThrow(() -> new BaseKnownException(10000, "请选择结束日期"));
        List<TransferRecord> transferRecords = singleDemandDetailMapper.listTransferRecord(start, end, req.getCenterId());
        //查询物料信息
        List<Integer> materialIds = transferRecords.stream()
                .map(TransferRecord::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, MaterialDTO> materialMap = Optional.ofNullable(materialClient.getMaterialsByIdList(materialIds))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .map(it -> it.stream().collect(Collectors.toMap(MaterialDTO::getId, v -> v, (a, b) -> b)))
                .orElse(new HashMap<>());
        //按照中心分组
        List<TransferReportDTO.TransferReportItem> transferReportItems = transferRecords.stream()
                .collect(Collectors.groupingBy(TransferRecord::getCenterId))
                .entrySet()
                .stream()
                .map(byCenter -> {
                    //按照物料分组聚合
                    Map<Integer, List<TransferRecord>> byMaterial = byCenter.getValue()
                            .stream()
                            .collect(Collectors.groupingBy(TransferRecord::getMaterialId));
                    HashMap<String, String> materialItemMap = new HashMap<>(byMaterial.size());
                    for (List<TransferRecord> value : byMaterial.values()) {
                        BigDecimal totalByMaterial = value
                                .stream()
                                .map(TransferRecord::getDemandNumber)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO);
                        // 获取大曲配送详情
                        ResultVO<DqDistributionDTO> resultVO = dqDistributionClient.getDqDistributionDetailByMaterial(value.get(0).getMaterialId());
                        if (resultVO.getCode() != 200 || StringUtil.isEmpty(resultVO.getData())) {
                            throw new BaseKnownException(10000, "获取大曲配送详情失败");
                        }

                        DqDistributionDTO dqDistribution = resultVO.getData();
                        Optional.ofNullable(materialMap.get(value.get(0).getMaterialId()))
                                .ifPresent(it -> materialItemMap.put(it.getMaterialName(), totalByMaterial.multiply(dqDistribution.getShippingNumber()).setScale(0) + "/" + totalByMaterial.setScale(0).toString()));
                    }
                    TransferReportDTO.TransferReportItem transferReportItem = new TransferReportDTO.TransferReportItem();
                    TransferRecord transferRecord = byCenter.getValue().get(0);
                    transferReportItem.setCenterId(transferRecord.getCenterId());
                    transferReportItem.setCenterName(transferRecord.getCenterName());
                    transferReportItem.setMaterialReport(materialItemMap);
                    return transferReportItem;
                }).sorted(Comparator.comparing(TransferReportDTO.TransferReportItem::getSequence)).collect(Collectors.toList());
        TransferReportDTO transferReportDTO = new TransferReportDTO();
        transferReportDTO.setItems(transferReportItems);
        return transferReportDTO;
    }

    @Override
    public List<MaterialOption> listMaterialsIssued() {
        List<Integer> materialIds = issueOrderDetailMapper.listMaterialsIssued();
        if (CollectionUtils.isEmpty(materialIds)) {
            return Lists.newArrayList();
        }
        return Optional.ofNullable(materialClient.getMaterialsByIdList(materialIds))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .map(it -> it.stream()
                        .map(m -> {
                            MaterialOption materialOption = new MaterialOption();
                            materialOption.setMaterialId(m.getId());
                            materialOption.setMaterialName(m.getMaterialName());
                            materialOption.setMaterialCode(m.getMaterialCode());
                            return materialOption;
                        })
                        .collect(Collectors.toList()))
                .orElseThrow(() -> new BaseKnownException(630001, "查询物料信息失败"));
    }

    @Override
    public void revoke(Integer id) {
        SapPost sapPost = Optional.ofNullable(sapPostMapper.selectOne(new LambdaQueryWrapper<SapPost>().eq(SapPost::getOrderDetailId, id).eq(SapPost::getState, "1").orderByDesc(SapPost::getCreateTime).last("limit 1").select(SapPost::getId)))
                .orElseThrow(() -> new BaseKnownException(927002, "未匹配到相关过账记录"));
        sapPostService.revoke(sapPost.getId());
    }

    private void handleExceptionCommon(Integer detailId, String detailAcceptState, BigDecimal exceptionDirection) {
        IssueOrderDetail detail = Optional.ofNullable(issueOrderDetailMapper.selectById(detailId))
                .orElseThrow(() -> new BaseKnownException(415007, "未查到相关发货信息"));
        IssueOrderItem item = Optional.ofNullable(issueOrderItemMapper.selectById(detail.getItemId()))
                .orElseThrow(() -> new BaseKnownException(423005, "未匹配到对应的子项信息"));
        IssueOrder order = Optional.ofNullable(issueOrderMapper.selectById(item.getOrderId()))
                .orElseThrow(() -> new BaseKnownException(423006, "未匹配到对应的发货单信息"));
        if (IssueStateEnum.FINISH.getCode().equals(order.getState())) {
            throw new BaseKnownException(423010, "当前发货单状态为已完成，不允许再进行异常操作");
        }
        //detail发起异常状态
        if (QFAcceptStateEnum.RAISE_EXCEPTION.getCode().equals(detailAcceptState)) {
            detail.setStateBeforeException(detail.getAcceptState());
        }
        detail.setAcceptState(detailAcceptState);
        issueOrderDetailMapper.updateById(detail);
        //处理子项实收数量
        item.setActualNumber(item.getActualNumber().add(exceptionDirection));
        issueOrderItemMapper.updateById(item);
        //处理车间库存数据
        SceneData sceneData = new SceneData()
                .setMaterialId(item.getMaterialId())
                .setMaterialName(item.getMaterialName())
                .setMaterialCode(item.getMaterialCode())
                .setCurrentWeight(detail.getBucketWeight().multiply(exceptionDirection))
                .setCenterId(order.getCenterId())
                .setCenterName(order.getCenterName())
                .setLocationId(order.getLocationId())
                .setLocationName(order.getLocationName());
        sceneDataService.handleSceneData(sceneData);
    }

    @Override
    public IssueOrderDetailDTO getByCode(String barcode) {
        return issueOrderDetailMapper.getByCode(barcode);
    }

    @Override
    public IssueOrderDetailDTO getLastIssueOrderDetailByParameterId(Integer parameterId) {
        IssueOrderDetail issueOrderDetail = issueOrderDetailMapper.selectOne(new LambdaQueryWrapper<IssueOrderDetail>()
                .eq(IssueOrderDetail::getDeleted, false)
                .eq(IssueOrderDetail::getParameterId, parameterId)
                .orderByDesc(IssueOrderDetail::getIssueTime)
                .last("limit 1")
        );
        return DtoMapper.convert(issueOrderDetail, IssueOrderDetailDTO.class);
    }

    @Override
    public List<VehicleTransportDetailDTO> getVehicleTransportDetail(Integer vehicleId, String type) {
        List<VehicleTransportDetailDTO> vehicleTransportDetail = baseMapper.getVehicleTransportDetail(vehicleId, type);
        List<Integer> materialIds = vehicleTransportDetail.stream()
                .map(VehicleTransportDetailDTO::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        Optional.ofNullable(materialClient.getMaterialsByIdList(materialIds))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .map(it -> it.stream().collect(Collectors.toMap(MaterialDTO::getId, v -> v, (a, b) -> b)))
                .ifPresent(it -> {
                    vehicleTransportDetail.forEach(vo -> {
                        MaterialDTO materialDTO = it.get(vo.getMaterialId());
                        if (Objects.isNull(materialDTO)) {
                            vo.setMaterialName(Strings.EMPTY);
                            vo.setMaterialCode(Strings.EMPTY);
                            return;
                        }
                        vo.setMaterialCode(materialDTO.getMaterialCode());
                        vo.setMaterialName(materialDTO.getMaterialName());
                    });
                });
        return vehicleTransportDetail;
    }

    @Override
    public org.springframework.data.domain.Page getVehicleIssueList(IssueOrderDetailQueryDTO issueOrderDetailQueryDTO) {
        return PageHelperUtil.getPage(baseMapper::getVehicleIssueList, issueOrderDetailQueryDTO, IssueOrderDetailPageDTO.class);
    }

    @Override
    public IssueDetailSumPageDTO getIssueDetailSum(IssueDetailSumQueryDTO issueDetailSumQueryDTO) {
        return issueOrderDetailMapper.getIssueDetailSum(issueDetailSumQueryDTO);
    }

    @Override
    public IssueSchedulePageDTO getBaseIssueSchedule(BaseIssueScheduleQueryDTO baseIssueScheduleQueryDTO) {
        IssueSchedulePageDTO issueSchedulePageDTO = new IssueSchedulePageDTO();
        List<BaseIssueSchedulePageDTO> baseIssueSchedule = issueOrderDetailMapper.getBaseIssueSchedule(baseIssueScheduleQueryDTO);
        List<PowderIssueSchedulePageDTO> list = new ArrayList<>();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(baseIssueSchedule)) {
            baseIssueSchedule.forEach(baseIssueSchedulePageDTO -> {
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(baseIssueSchedulePageDTO.getCenterIssueSchedulePageDTOS())) {
                    baseIssueSchedulePageDTO.getCenterIssueSchedulePageDTOS().forEach(centerIssueSchedulePageDTO -> {
                        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(centerIssueSchedulePageDTO.getPowderIssueSchedulePageDTOS())) {
                            list.addAll(centerIssueSchedulePageDTO.getPowderIssueSchedulePageDTOS());
                            BigDecimal issueWeight = centerIssueSchedulePageDTO.getPowderIssueSchedulePageDTOS().stream().map(PowderIssueSchedulePageDTO::getIssueWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal demandWeight = centerIssueSchedulePageDTO.getPowderIssueSchedulePageDTOS().stream().map(PowderIssueSchedulePageDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                            baseIssueSchedulePageDTO.setDemandWeight(StringUtil.isNotEmpty(baseIssueSchedulePageDTO.getDemandWeight()) ? baseIssueSchedulePageDTO.getDemandWeight().add(demandWeight) : demandWeight);
                            baseIssueSchedulePageDTO.setIssueWeight(StringUtil.isNotEmpty(baseIssueSchedulePageDTO.getIssueWeight()) ? baseIssueSchedulePageDTO.getIssueWeight().add(issueWeight) : issueWeight);
                            if (demandWeight.compareTo(BigDecimal.ZERO) != 0) {
                                centerIssueSchedulePageDTO.setSchedule(new DecimalFormat("0.00%").format(issueWeight.divide(demandWeight, 2)));
                            } else {
                                centerIssueSchedulePageDTO.setSchedule("0.00%");
                            }
                        }
                    });
                    if (baseIssueSchedulePageDTO.getDemandWeight().compareTo(BigDecimal.ZERO) != 0) {
                        baseIssueSchedulePageDTO.setSchedule(new DecimalFormat("0.00%").format(baseIssueSchedulePageDTO.getIssueWeight().divide(baseIssueSchedulePageDTO.getDemandWeight(), 0)));
                    } else {
                        baseIssueSchedulePageDTO.setSchedule("0.00%");
                    }
                } else {
                    baseIssueSchedulePageDTO.setSchedule("0.00%");
                }
            });
        }
        //斗装 type = 1
        List<IssueSchedulePageDTO.ParameterSchedule> douSchedule = new ArrayList<>();
        List<PowderIssueSchedulePageDTO> dou = list.stream().filter(powderIssueSchedulePageDTO -> "1".equals(powderIssueSchedulePageDTO.getType())).collect(Collectors.toList());
        List<PowderIssueSchedulePageDTO> douQF1 = dou.stream().filter(powderIssueSchedulePageDTO -> "QF1".equals(powderIssueSchedulePageDTO.getMaterialName())).collect(Collectors.toList());
        BigDecimal douQF1DemandNumber = douQF1.stream().map(PowderIssueSchedulePageDTO::getDemandNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        IssueSchedulePageDTO.ParameterSchedule douQF1parameterSchedule = new IssueSchedulePageDTO.ParameterSchedule();
        douQF1parameterSchedule.setMaterialName("QF1");
        douQF1parameterSchedule.setMaterialCode("34000074");
        douQF1parameterSchedule.setDemandNumber(douQF1DemandNumber);
        List<IssueOrderDetailStatistics> douQF1Issue = issueOrderDetailMapper.getIssueOrderDetailStatistics("QF1", "1", null,baseIssueScheduleQueryDTO);
        BigDecimal douQF1IssueNumber = douQF1Issue.stream().map(IssueOrderDetailStatistics::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal douQF1ActualNumber = douQF1Issue.stream().filter(issueOrderDetailStatistics -> "1".equals(issueOrderDetailStatistics.getAcceptState())).map(IssueOrderDetailStatistics::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        douQF1parameterSchedule.setIssueNumber(douQF1IssueNumber);
        douQF1parameterSchedule.setActualNumber(douQF1ActualNumber);
        douSchedule.add(douQF1parameterSchedule);
        List<PowderIssueSchedulePageDTO> douQF2 = dou.stream().filter(powderIssueSchedulePageDTO -> "QF2".equals(powderIssueSchedulePageDTO.getMaterialName())).collect(Collectors.toList());
        BigDecimal douQF2DemandNumber = douQF2.stream().map(PowderIssueSchedulePageDTO::getDemandNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<IssueOrderDetailStatistics> douQF2Issue = issueOrderDetailMapper.getIssueOrderDetailStatistics("QF2", "1", null,baseIssueScheduleQueryDTO);
        BigDecimal douQF2IssueNumber = douQF2Issue.stream().map(IssueOrderDetailStatistics::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal douQF2ActualNumber = douQF2Issue.stream().filter(issueOrderDetailStatistics -> "1".equals(issueOrderDetailStatistics.getAcceptState())).map(IssueOrderDetailStatistics::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        IssueSchedulePageDTO.ParameterSchedule douQF2parameterSchedule = new IssueSchedulePageDTO.ParameterSchedule();
        douQF2parameterSchedule.setMaterialName("QF2");
        douQF2parameterSchedule.setMaterialCode("34000073");
        douQF2parameterSchedule.setDemandNumber(douQF2DemandNumber);
        douQF2parameterSchedule.setIssueNumber(douQF2IssueNumber);
        douQF2parameterSchedule.setActualNumber(douQF2ActualNumber);
        douSchedule.add(douQF2parameterSchedule);
        issueSchedulePageDTO.setDouSchedule(douSchedule);
        //袋装 type = 2
        List<IssueSchedulePageDTO.ParameterSchedule> daiSchedule = new ArrayList<>();
        List<PowderIssueSchedulePageDTO> dai = list.stream().filter(powderIssueSchedulePageDTO -> "2".equals(powderIssueSchedulePageDTO.getType())).collect(Collectors.toList());
        List<PowderIssueSchedulePageDTO> daiQF1 = dai.stream().filter(powderIssueSchedulePageDTO -> "QF1".equals(powderIssueSchedulePageDTO.getMaterialName())).collect(Collectors.toList());
        BigDecimal daiQF1DemandNumber = daiQF1.stream().map(PowderIssueSchedulePageDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        IssueSchedulePageDTO.ParameterSchedule daiQF1parameterSchedule = new IssueSchedulePageDTO.ParameterSchedule();
        List<IssueOrderDetailStatistics> daiQF1Issue = issueOrderDetailMapper.getIssueOrderDetailStatistics("QF1", "2", null,baseIssueScheduleQueryDTO);
        BigDecimal daiQF1IssueNumber = daiQF1Issue.stream().map(issueOrderDetailStatistics -> issueOrderDetailStatistics.getIssueNumber().multiply(issueOrderDetailStatistics.getSpecification())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal daiQF1ActualNumber = daiQF1Issue.stream().filter(issueOrderDetailStatistics -> "1".equals(issueOrderDetailStatistics.getAcceptState())).map(issueOrderDetailStatistics -> issueOrderDetailStatistics.getIssueNumber().multiply(issueOrderDetailStatistics.getSpecification())).reduce(BigDecimal.ZERO, BigDecimal::add);
        daiQF1parameterSchedule.setMaterialName("QF1");
        daiQF1parameterSchedule.setMaterialCode("34000074");
        daiQF1parameterSchedule.setDemandNumber(daiQF1DemandNumber);
        daiQF1parameterSchedule.setIssueNumber(daiQF1IssueNumber);
        daiQF1parameterSchedule.setActualNumber(daiQF1ActualNumber);
        daiSchedule.add(daiQF1parameterSchedule);
        List<PowderIssueSchedulePageDTO> daiQF2 = dai.stream().filter(powderIssueSchedulePageDTO -> "QF2".equals(powderIssueSchedulePageDTO.getMaterialName())).collect(Collectors.toList());
        BigDecimal daiQF2DemandNumber = daiQF2.stream().map(PowderIssueSchedulePageDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<IssueOrderDetailStatistics> daiQF2Issue = issueOrderDetailMapper.getIssueOrderDetailStatistics("QF2", "2", null,baseIssueScheduleQueryDTO);
        BigDecimal daiQF2IssueNumber = daiQF2Issue.stream().map(issueOrderDetailStatistics -> issueOrderDetailStatistics.getIssueNumber().multiply(issueOrderDetailStatistics.getSpecification())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal daiQF2ActualNumber = daiQF2Issue.stream().filter(issueOrderDetailStatistics -> "1".equals(issueOrderDetailStatistics.getAcceptState())).map(issueOrderDetailStatistics -> issueOrderDetailStatistics.getIssueNumber().multiply(issueOrderDetailStatistics.getSpecification())).reduce(BigDecimal.ZERO, BigDecimal::add);
        IssueSchedulePageDTO.ParameterSchedule QF2parameterSchedule = new IssueSchedulePageDTO.ParameterSchedule();
        QF2parameterSchedule.setMaterialName("QF2");
        QF2parameterSchedule.setMaterialCode("34000073");
        QF2parameterSchedule.setDemandNumber(daiQF2DemandNumber);
        QF2parameterSchedule.setIssueNumber(daiQF2IssueNumber);
        QF2parameterSchedule.setActualNumber(daiQF2ActualNumber);
        daiSchedule.add(QF2parameterSchedule);
        issueSchedulePageDTO.setDaiSchedule(daiSchedule);
        issueSchedulePageDTO.setBaseIssueSchedulePageDTOList(baseIssueSchedule);
        return issueSchedulePageDTO;
    }

    @Override
    public void vehicleRelease(String licensePlate) {
        VehicleInfoSendArtDTO vehicleInfoSendArtDTO = new VehicleInfoSendArtDTO();
        vehicleInfoSendArtDTO.setPlateNo(licensePlate);
        vehicleInfoSendArtDTO.setPurpose("车辆出门");
        vehicleInfoSendArtDTO.setCargoName("曲粉");
        vehicleInfoSendArtDTO.setPlanOutTime(new Date());
        vehicleInfoSendArtDTO.setStayNightFlag("2");
        vehicleInfoSendArtDTO.setParkCode("2");
//        artService.sendVehicleInfo(vehicleInfoSendArtDTO);
    }

    @Override
    public Boolean receivePowderVehicle(WeighDataDTO weighDataDTO) {
        IssueOrderDetail issueOrderDetail = new IssueOrderDetail();
        issueOrderDetail.setDemandNumber(weighDataDTO.getDeliveryNumber()); //送货单号
        issueOrderDetail.setGrossWeight(weighDataDTO.getGrossWeight()); //毛重
        issueOrderDetail.setAppearanceWeight(weighDataDTO.getAppearanceWeight()); //皮重
        issueOrderDetail.setLeaveNetWeight(weighDataDTO.getNetWeight()); //离场净重
        issueOrderDetail.setNetWeight(weighDataDTO.getNetWeight()); //最终净重
        issueOrderDetail.setDemandNumber(generateCodeUtil.generateInspectionCode("QF", 3));
        //处理过磅时间
        if (StringUtil.isNotEmpty(weighDataDTO.getType())) {
            if (weighDataDTO.getType().equals("1")) {
                issueOrderDetail.setVehicleState("1");
                issueOrderDetail.setAdmissionTime(new Date());
                return false;
            } else if (weighDataDTO.getType().equals("2")) {
                issueOrderDetail.setVehicleState("2");
                issueOrderDetail.setAppearanceTime(new Date());
            }
        }
        //没有设置上入场时间,有毛重就是入场
        if (issueOrderDetail.getGrossWeight() != null && issueOrderDetail.getGrossWeight().compareTo(BigDecimal.ZERO) > 0) {
            issueOrderDetail.setAdmissionTime(new Date());
        }
        String demandNuber = issueOrderDetailMapper.getDemandByCP(weighDataDTO.getLicensePlateNumber());
        return issueOrderDetailMapper.receivePowderVehicle(issueOrderDetail, demandNuber) > 0;
    }

    /**
     * @描述: 曲粉发放重量对比
     * @作者: 刘文勇
     * @日期: 2024/6/3 10:38
     * @参数: issueWeightQuery
     * @返回值: java.util.List<com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueWeight>
     */
    @Override
    public org.springframework.data.domain.Page<IssueWeight> weightContrast(IssueWeightQuery issueWeightQuery) {
        //查询出车辆过磅信息根据每次过磅分组
        org.springframework.data.domain.Page<IssueWeight> page = PageHelperUtil.getPage(issueOrderDetailMapper::getWeightContrast, issueWeightQuery, IssueWeight.class);
        //根据车辆过磅信息查询曲粉发放记录
        if (StringUtil.isNotEmpty(page.getContent())) {
            page.getContent().forEach(issueWeight -> {
                List<IssueWeightDetail> issueWeightDetailList = issueOrderDetailMapper.getWeighingInformationDetailList(issueWeight, issueWeightQuery.getStartTime(), issueWeightQuery.getEndTime());
                if (StringUtil.isNotEmpty(issueWeightDetailList)) {
                    issueWeight.setIssueWeightDetailList(issueWeightDetailList);
                    BigDecimal reduce = issueWeightDetailList.stream().map(IssueWeightDetail::getIssueWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    issueWeight.setIssueWeight(reduce);
                    if (StringUtil.isNotEmpty(issueWeight.getIssueWeight()) && StringUtil.isNotEmpty(issueWeight.getNetWeight())) {
                        issueWeight.setDifferenceWeight(issueWeight.getNetWeight().subtract(issueWeight.getIssueWeight()));
                    }
                }
            });
        }
        return page;
    }

    /**
     * @描述: 曲粉接收调拨
     * @作者: 刘文勇
     * @日期: 2024/6/11 11:31
     * @参数: reportReq
     * @返回值: java.util.List<com.hvisions.powder.dto.qudou.demand.issue.order.detail.AllocationPageDTO>
     */
    @Override
    public List<AllocationPageDTO> getAllocation(ReportReq reportReq) {
        List<AllocationPageDTO> allocationPageDTOList = new ArrayList<>();
        List<LocationDTO> data = new ArrayList<>();
        List<LocationDTO> baseList = new ArrayList<>();
        try {
            ResultVO<List<LocationDTO>> locationListByType = locationExtendClient.getLocationListByType(20);
            ResultVO<List<LocationDTO>> base = locationExtendClient.getLocationListByType(10);
            baseList.addAll(base.getData());
            data.addAll(locationListByType.getData());
        }catch (Exception e){
            e.printStackTrace();
        }
        //查询中心接收数量
        List<AllocationDetailPageDTO> allocationDetailPageDTOList = issueOrderDetailMapper.getAllocationDetail(reportReq);
        //查询中心需求数量
        List<DemandForCenterDTO> demandForCenterDTOList = singleDemandDetailMapper.getDemandForCenter(reportReq);
        data.stream()
                .filter(item ->!item.getName().equals("原辅料管理部") && !item.getName().equals("制曲中心") && !item.getName().equals("酒管中心"))
                .filter(item -> StringUtil.isEmpty(reportReq.getCenterId()) || item.getId().equals(reportReq.getCenterId()))
                .filter(item ->StringUtil.isEmpty(reportReq.getBaseId()) || item.getParentId().equals(reportReq.getBaseId()))
                .forEach(location ->{
                    AllocationPageDTO allocationPageDTO = new AllocationPageDTO();
                    allocationPageDTO.setCenterId(location.getId());
                    allocationPageDTO.setCenterCode(location.getCode());
                    allocationPageDTO.setCenterName(location.getName());
                    LocationDTO locationDTO = baseList.stream().filter(item -> item.getId().equals(location.getParentId())).findFirst().orElse(new LocationDTO());
                    allocationPageDTO.setBaseId(locationDTO.getId());
                    allocationPageDTO.setBaseName(locationDTO.getName());
                    allocationPageDTO.setQF1PostState("0");
                    allocationPageDTO.setQF2PostState("0");
                    allocationPageDTO.setQF3PostState("0");
                    allocationPageDTO.setQF4PostState("0");
                    List<AllocationDetailPageDTO> collect = allocationDetailPageDTOList.stream().filter(item -> location.getId().equals(item.getCenterId())).collect(Collectors.toList());
                    List<DemandForCenterDTO> collect1 = demandForCenterDTOList.stream().filter(item -> location.getId().equals(item.getCenterId())).collect(Collectors.toList());
                //统计QF1
                    BigDecimal douQF1 = collect1.stream().filter(item -> "QF1".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF1DemandQuantity(douQF1);
                    BigDecimal douQF1w = collect1.stream().filter(item -> "QF1".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF1DemandWeight(douQF1w);
                    //接收重量
                    BigDecimal QF1Weight = collect.stream().filter(allocationDetailPageDTO -> "QF1".equals(allocationDetailPageDTO.getMaterialName())).map(item ->item.getIssueNumber().multiply(item.getSpecification())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF1Weight(QF1Weight);
                    //过账数量
                    List<AllocationDetailPageDTO> issue = collect.stream().filter(allocationDetailPageDTO -> "1".equals(allocationDetailPageDTO.getPostState()) && "QF1".equals(allocationDetailPageDTO.getMaterialName())).collect(Collectors.toList());
                    BigDecimal QF1PostQuantity = issue.stream().map(AllocationDetailPageDTO::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF1PostQuantity(QF1PostQuantity);
                    if (QF1PostQuantity.compareTo(new BigDecimal(0)) > 0) {
                        allocationPageDTO.setQF1PostState("1");
                        allocationPageDTO.setQF1PostNumber(Optional.ofNullable(issue.get(0)).map(AllocationDetailPageDTO::getPostNumber).orElse(""));
                        allocationPageDTO.setQF1PostTime(Optional.ofNullable(issue.get(0)).map(AllocationDetailPageDTO::getPostTime).orElse(null));
                    }
                //统计QF2
                    //斗
                    BigDecimal douQF2 = collect1.stream().filter(item -> "QF2".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF2DemandQuantity(douQF2);
                    BigDecimal douQF2w = collect1.stream().filter(item -> "QF2".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF2DemandWeight(douQF2w);
                    //接收重量
                    BigDecimal QF2Weight = collect.stream().filter(allocationDetailPageDTO -> "QF2".equals(allocationDetailPageDTO.getMaterialName())).map(item ->item.getIssueNumber().multiply(item.getSpecification())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF2Weight(QF2Weight);
                    //过账数量
                    List<AllocationDetailPageDTO> collect2 = collect.stream().filter(allocationDetailPageDTO -> "1".equals(allocationDetailPageDTO.getPostState()) && "QF2".equals(allocationDetailPageDTO.getMaterialName())).collect(Collectors.toList());
                    BigDecimal QF2PostQuantity = collect2.stream().map(AllocationDetailPageDTO::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF2PostQuantity(QF2PostQuantity);
                    if (QF2PostQuantity.compareTo(new BigDecimal(0)) > 0) {
                        allocationPageDTO.setQF2PostState("1");
                        allocationPageDTO.setQF2PostNumber(Optional.ofNullable(collect2.get(0)).map(AllocationDetailPageDTO::getPostNumber).orElse(""));
                        allocationPageDTO.setQF2PostTime(Optional.ofNullable(collect2.get(0)).map(AllocationDetailPageDTO::getPostTime).orElse(null));
                    }
                //统计QF3
                    BigDecimal douQF3 = collect1.stream().filter(item -> "QF3".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF3DemandQuantity(douQF3);
                    BigDecimal douQF3w = collect1.stream().filter(item -> "QF3".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF3DemandWeight(douQF3w);
                    //接收重量
                    BigDecimal QF3Weight = collect.stream().filter(allocationDetailPageDTO -> "QF3".equals(allocationDetailPageDTO.getMaterialName())).map(allocationDetailPageDTO -> allocationDetailPageDTO.getSpecification().multiply(allocationDetailPageDTO.getIssueNumber())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF3Weight(QF3Weight);
                    //过账数量
                    List<AllocationDetailPageDTO> collect3 = collect.stream().filter(allocationDetailPageDTO -> "1".equals(allocationDetailPageDTO.getPostState()) && "QF3".equals(allocationDetailPageDTO.getMaterialName())).collect(Collectors.toList());
                    BigDecimal QF3PostQuantity = collect3.stream().map(AllocationDetailPageDTO::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF3PostQuantity(QF3PostQuantity);
                    if (QF3PostQuantity.compareTo(new BigDecimal(0)) > 0) {
                        allocationPageDTO.setQF3PostState("1");
                        allocationPageDTO.setQF3PostNumber(Optional.ofNullable(collect3.get(0)).map(AllocationDetailPageDTO::getPostNumber).orElse(""));
                        allocationPageDTO.setQF3PostTime(Optional.ofNullable(collect3.get(0)).map(AllocationDetailPageDTO::getPostTime).orElse(null));
                    }
                //统计QF4
                    BigDecimal douQF4 = collect1.stream().filter(item -> "QF4".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF4DemandQuantity(douQF4);
                    BigDecimal douQF4w = collect1.stream().filter(item -> "QF4".equals(item.getMaterialName())).map(DemandForCenterDTO::getDemandWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF4DemandWeight(douQF4w);
                    //接收重量
                    BigDecimal QF4Weight = collect.stream().filter(allocationDetailPageDTO -> "QF4".equals(allocationDetailPageDTO.getMaterialName())).map(item -> item.getSpecification().multiply(item.getIssueNumber())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF4Weight(QF4Weight);
                    //过账数量
                    List<AllocationDetailPageDTO> collect4 = collect.stream().filter(allocationDetailPageDTO -> "1".equals(allocationDetailPageDTO.getPostState()) && "QF4".equals(allocationDetailPageDTO.getMaterialName())).collect(Collectors.toList());
                    BigDecimal QF4PostQuantity = collect4.stream().map(AllocationDetailPageDTO::getIssueNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    allocationPageDTO.setQF4PostQuantity(QF4PostQuantity);
                    if (QF4PostQuantity.compareTo(new BigDecimal(0)) > 0) {
                        allocationPageDTO.setQF4PostState("1");
                        allocationPageDTO.setQF4PostNumber(Optional.ofNullable(collect4.get(0)).map(AllocationDetailPageDTO::getPostNumber).orElse(""));
                        allocationPageDTO.setQF4PostTime(Optional.ofNullable(collect4.get(0)).map(AllocationDetailPageDTO::getPostTime).orElse(null));
                    }
                    allocationPageDTOList.add(allocationPageDTO);
                });
        return allocationPageDTOList;
    }

    /**
     * @描述: 706中心曲粉过账
     * @作者: 刘文勇
     * @日期: 2024/6/16 15:25
     * @参数: postForLuoHanDTO
     * @返回值: java.lang.Boolean
     */
    @Override
    public Boolean postForLuoHan(PostForLuoHanDTO postForLuoHanDTO) {
        UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));
        //过账sap
        List<InventoryLocationPageDTO> inventoryLocationListByCenterId = sapPostMapper.getInventoryLocationListByCenterId(postForLuoHanDTO.getCenterId());
        if (inventoryLocationListByCenterId.isEmpty()) {
            throw new BaseKnownException(10000, "当前中心未维护sap库存地址");
        }
        MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setMaterialName(postForLuoHanDTO.getMaterialName());
        SapPost sapPost = new SapPost();
        Optional.ofNullable(materialClient.getMaterialByNameOrCode(queryDTO))
                .filter(ResultVO::isSuccess)
                .map(ResultVO::getData)
                .map(HvPage::getContent)
                .map(item -> {
                    sapPost.setMaterialCode(item.get(0).getMaterialCode());
                    sapPost.setMaterialId(item.get(0).getId());
                    return null;
                });
        LocalDate now = postForLuoHanDTO.getPostDate() == null?LocalDate.now():postForLuoHanDTO.getPostDate().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        InventoryLocationPageDTO locationPageDTO = inventoryLocationListByCenterId.get(0);
        sapPost.setMaterialName(postForLuoHanDTO.getMaterialName())
                .setUnit("kg")
                .setWeight(postForLuoHanDTO.getPostQuantity())
                .setInitialWarehouseCode("1397")
                .setType(0)
                .setTargetWarehouseCode(locationPageDTO.getCode())
                .setState(SapPostStateEnum.SAP_PROCESSING.getCode())
                .setOrderDetailId(null)
                .setOperator(userBaseDTO.getUserName())
                .setOperatorId(userBaseDTO.getId())
                .setSapCode("MESNJ016")
                .setOperatorType("曲粉接收")
                .setOperatingTime(LocalDateTime.now())
                .setCertificateDate(now)
                .setState("0");
        sapPostMapper.insert(sapPost);
        InventoryAllocationDto req = new InventoryAllocationDto();
        String orderNo = "";
        // 冲销后重新调拨
        if (sapPost.getState().equals(SapPostStateEnum.REVOKE.getCode())) {
            orderNo = this.generalOrderNo();
        } else {
            //如果之前提交过  则默认取表里的数据用于重发
            orderNo = Optional.ofNullable(sapPost.getOrderNo())
                    .orElseGet(this::generalOrderNo);
        }
        req.setOrderNo(orderNo);
        req.setMaterial(sapPost.getMaterialCode());
        req.setSlogrt(sapPost.getInitialWarehouseCode());
        req.setDlogrt(sapPost.getTargetWarehouseCode());
        req.setEntryQnt(sapPost.getWeight());
        req.setCertificateDate(sapPost.getCertificateDate());
        SapBaseResponseDto sapBaseResponseDto = sapService.inventoryAllocation(req);
        sapPost.setValueJson(JSONObject.toJSONString(req));

        if ("E".equals(sapBaseResponseDto.getEsMessage().getMsgty())
                && !sapBaseResponseDto.getEsMessage().getMsgtx().contains("已处理，请勿重复传输！")) {
            // 记录失败原因
            sapPost.setState(SapPostStateEnum.FAILED.getCode());
            sapPost.setFailReason(sapBaseResponseDto.getEsMessage().getMsgtx());
            sapPost.setOrderNo(orderNo);
            sapPostMapper.updateById(sapPost);
            log.info("706中心过账失败:{}", sapBaseResponseDto.getEsMessage().getMsgtx());
            throw new BaseKnownException(10000, sapBaseResponseDto.getEsMessage().getMsgtx());
        } else {
            // 保存SAP返回的过账凭证
            SapBaseOutputDto sapBaseOutputDto = sapBaseResponseDto.getEvOutput().getOutput().get(0);
            sapPost.setState(SapPostStateEnum.FINISH.getCode());
            sapPost.setCertificateNumber(sapBaseOutputDto.getMblnr());
            sapPost.setCertificateYear(sapBaseOutputDto.getMjahr());
            sapPost.setOrderNo(orderNo);
            //更新发放记录过账状态
            issueOrderDetailMapper.postForLuoHan(DateUtil.formatLocaldate(now, "yyyy-MM-dd"), sapBaseOutputDto.getMjahr(), sapBaseOutputDto.getMblnr(), postForLuoHanDTO.getBeginDate(), postForLuoHanDTO.getEndDate(), sapPost.getMaterialId());
            //如果成功  清空异常信息
            sapPost.setFailReason("");
            sapPostMapper.updateById(sapPost);
            log.info("706中心过账成功");
        }
        return null;
    }

    /**
     * @描述: 706中心曲粉冲销
     * @作者: 刘文勇
     * @日期: 2024/6/17 9:07
     * @参数: writeForLuoHanDTO
     * @返回值: java.lang.Boolean
     */
    @Override
    public Boolean writeForLuoHan(WriteForLuoHanDTO writeForLuoHanDTO) {
        List<Map<String, Object>> writeList = issueOrderDetailMapper.getWriteList(writeForLuoHanDTO.getBeginDate(), writeForLuoHanDTO.getEndDate(), writeForLuoHanDTO.getMaterialName(),writeForLuoHanDTO.getCenterId());
        Optional.ofNullable(writeList)
                .orElse(new ArrayList<>())
                .forEach(item -> {
                    OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
                    headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO));
                    headerDto.setPstingDate(DateUtil.dateFormat((Date) item.get("postTime"), "yyyy-MM-dd"));
                    headerDto.setMjahr((String) item.get("postYear"));
                    headerDto.setMblnr((String) item.get("postNumber"));
                    SapBaseResponseDto responseDto = sapService.writeOff(headerDto);
                    if ("S".equals(responseDto.getEsMessage().getMsgty())) {
                        SapPost sapPost = sapPostMapper.selectOne(new LambdaUpdateWrapper<SapPost>()
                                .eq(SapPost::getCertificateNumber, (String) item.get("postNumber"))
                                .eq(SapPost::getCertificateYear, (String) item.get("postYear"))
                                .last("LIMIT 1"));
                        if(StringUtil.isNotEmpty(sapPost)){
                            sapPost.setState("3");
                            sapPostMapper.updateById(sapPost);
                        }
//                        issueOrderDetailMapper.writeForLuoHan((String) item.get("postYear"), (String) item.get("postNumber"));
                    } else {
                        log.error("sap冲销失败，原因：" + responseDto.getEsMessage().getMsgtx());
                        throw new BaseKnownException(10000, "sap冲销失败，原因：" + responseDto.getEsMessage().getMsgtx());
                    }
                });
        return null;
    }

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private String generalOrderNo() {
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateString = LocalDate.now().format(formatters);

        String cacheKey = "SapPost:" + dateString;
        Boolean absent = stringRedisTemplate.opsForValue().setIfAbsent(cacheKey, "1");
        if (Objects.nonNull(absent) && absent) {
            return cacheKey + "001";
        }
        //创建失败  自增
        Long afterIncrease = stringRedisTemplate.opsForValue().increment(cacheKey, 1);
//        if (Objects.nonNull(afterIncrease) && CommonConsts.TENANT_ID_999L < afterIncrease) {
//            throw new BaseKnownException(325005, "当前数值已超出99限制");
//        }
        return "16" + dateString + afterIncrease.toString();
    }


    @Override
    public List<PowderDistributionDetailDTO> getPowderDistributionProductQuantityByTime(Date startTime, Date endTime) {
        return issueOrderDetailMapper.getPowderDistributionProductQuantityByTime(startTime, endTime);
    }

    @Override
    public List<PowderDistributionDetailDTO> getPowderDistributionMonthProductQuantity(int year, int month) {
        return issueOrderDetailMapper.getPowderDistributionMonthProductQuantity(year, month);
    }

    @Override
    public BigDecimal getAcceptTotal(QuFenAcceptTotalQueryDTO queryDTO) {
        return issueOrderDetailMapper.getAcceptTotal(queryDTO);
    }

    /**
      * @描述: 根据粉碎批次获取发放信息
      * @作者: 刘文勇
      * @日期: 2024/7/6 16:02
      * @参数: issueDetailSmashQueryDTO
      * @返回值: java.util.List<com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueDetailSmashDTO>
    */
    @Override
    public List<IssueDetailSmashDTO> getIssueDetailSmash(IssueDetailSmashQueryDTO issueDetailSmashQueryDTO) {
        return issueOrderDetailMapper.getIssueDetailSmash(issueDetailSmashQueryDTO);
    }

    @Override
    public IssueDetailSmashDTO getIssueOrderDetailById(Integer id) {
        return issueOrderDetailMapper.getIssueOrderDetailById(id);
    }

    @Override
    public IssueWeightSummary getIssueWeightSummary(IssueWeightQuerySummary issueWeightQuerySummary) {
        IssueWeightSummary issueWeightSummary = Optional.ofNullable(issueOrderDetailMapper.getIssueWeightSummary(issueWeightQuerySummary)).orElse(new IssueWeightSummary());
        Map<String,BigDecimal> map = Optional.ofNullable(issueOrderDetailMapper.getSalesWeightSummary(issueWeightQuerySummary)).orElse(new HashMap<>());
        issueWeightSummary.setSalesQf1Weight(Optional.ofNullable(map.get("salesQf1Weight")).orElse(new BigDecimal("0")));
        issueWeightSummary.setSalesQf2Weight(Optional.ofNullable(map.get("salesQf2Weight")).orElse(new BigDecimal("0")));
        return issueWeightSummary;
    }

    /**
      * @描述: 曲粉接收调拨导出
      * @作者: 刘文勇
      * @日期: 2024/7/16 12:07
      * @参数: reportReq
      * @返回值: com.hvisions.common.vo.ResultVO<com.hvisions.common.dto.ExcelExportDto>
    */
    @Override
    public ResultVO<ExcelExportDto> getAllocationExcel(ReportReq reportReq) throws UnsupportedEncodingException {
        List<AllocationPageDTO> allocation = this.getAllocation(reportReq);
        List<AllocationExcel> collect = allocation.stream().map(item -> {
            AllocationExcel allocationExcel = new AllocationExcel();
            allocationExcel.setCenterName(item.getCenterName());
            allocationExcel.setQF1DemandWeight(item.getQF1DemandWeight());
            allocationExcel.setQF1IssueWeight(item.getQF1Weight());
            allocationExcel.setQF2DemandWeight(item.getQF2DemandWeight());
            allocationExcel.setQF2IssueWeight(item.getQF2Weight());
            allocationExcel.setQF3DemandWeight(item.getQF3DemandWeight());
            allocationExcel.setQF3IssueWeight(item.getQF3Weight());
            allocationExcel.setQF4DemandWeight(item.getQF4DemandWeight());
            allocationExcel.setQF4IssueWeight(item.getQF4Weight());
            allocationExcel.setDemandTotal(item.getQF1DemandWeight().add(item.getQF2DemandWeight()).add(item.getQF3DemandWeight()).add(item.getQF4DemandWeight()));
            allocationExcel.setIssueTotal(item.getQF1Weight().add(item.getQF2Weight()).add(item.getQF3Weight()).add(item.getQF4Weight()));
            return allocationExcel;
        }).collect(Collectors.toList());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, AllocationExcel.class)
                .sheet("曲粉日需求导出")
                .doWrite(collect);
        byte[] bytes = outputStream.toByteArray();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachement", URLEncoder.encode("曲粉接收调拨导出.xlsx", "utf-8"));
        ResponseEntity<byte[]> responseEntity = new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName("曲粉接收调拨导出.xlsx");
        return ResultVO.success(excelExportDto);
    }

    /**
     * 发放记录批量过账
     * @param detailIds
     */
    @Override
    public void recordToSapPost(List<IssueOrderDetailAccept> detailIds, LocalDate acceptDate) {
        UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));
        List<Integer> detailIdList = detailIds.stream().map(IssueOrderDetailAccept::getId).collect(Collectors.toList());
        //判断是否有已经过账的数据
        List<SapPost> sapPosts = sapPostMapper.selectList(new LambdaUpdateWrapper<SapPost>().eq(SapPost::getDeleted, false).eq(SapPost::getState, "1").in(SapPost::getOrderDetailId, detailIdList));
        if (!CollectionUtils.isEmpty(sapPosts)) {
            throw new BaseKnownException(621009, "提交有已过账的数据，请检查");
        }
        //处理发货单相关
        List<IssueOrderDetail> issueOrderDetails = issueOrderDetailMapper.selectBatchIds(detailIdList);
        List<Integer> itemIds = issueOrderDetails.stream()
                .map(IssueOrderDetail::getItemId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemIds)) {
            throw new BaseKnownException(621008, "未匹配到相关发放单子项信息");
        }
        List<IssueOrderItem> issueOrderItems = issueOrderItemMapper.selectBatchIds(itemIds);
        List<Integer> orderIds = issueOrderItems.stream()
                .map(IssueOrderItem::getOrderId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new BaseKnownException(621007, "未匹配到相关发放单信息");
        }
        List<IssueOrder> issueOrders = issueOrderMapper.selectBatchIds(orderIds);
        Map<Integer, IssueOrderItem> itemMap = issueOrderItems.stream()
                .collect(Collectors.toMap(IssueOrderItem::getId, v -> v, (a, b) -> b));
        Map<Integer, IssueOrder> orderMap = issueOrders.stream()
                .collect(Collectors.toMap(IssueOrder::getId, v -> v));
        List<IssueOrderDetail> detailContainer = null;
        for (IssueOrderDetail issueOrderDetail : issueOrderDetails) {
            detailContainer = new ArrayList<IssueOrderDetail>();
            IssueOrderDetailAccept issueOrderDetailAccept1 = detailIds.stream().filter(issueOrderDetailAccept -> issueOrderDetailAccept.getId().equals(issueOrderDetail.getId())).findAny().orElse(new IssueOrderDetailAccept());
            issueOrderDetail.setIssueNumber(StringUtil.isNotEmpty(issueOrderDetailAccept1.getActualNumber()) ? issueOrderDetailAccept1.getActualNumber() : issueOrderDetail.getIssueNumber());
            detailContainer.add(issueOrderDetail);
            // 根据过账日期(曲粉接收时间)判定是否过账给sap
            ZoneId zoneId = ZoneId.systemDefault();
            Date postDate = Date.from(acceptDate.atStartOfDay().atZone(zoneId).toInstant());
            postDate = getPostDate(postDate);
            boolean isPost = sapService.judgeSapPostTime(postDate);
            String acceptCenter = detailContainer.get(0).getAcceptCenter();
            if (isPost) {
                if (!"706".equals(acceptCenter) && !"707".equals(acceptCenter)) {
                    handleReportSAP(userBaseDTO, detailContainer, itemMap, orderMap, postDate);
                }
            }
        }
    }

    /**
     * 发放记录批量冲销
     * @param reversalList
     */
    @Override
    public void recordToSapReversal(List<RecordToSapReversal> reversalList) {
        //对冲销的数据做验证
        List<String> numbers = reversalList.stream().map(RecordToSapReversal::getPostNumber).collect(Collectors.toList());
        List<SapPost> sapPosts = sapPostMapper.selectList(new LambdaUpdateWrapper<SapPost>().in(SapPost::getCertificateNumber, numbers).eq(SapPost::getState, "1"));
        if (CollectionUtils.isEmpty(sapPosts) || sapPosts.size() != numbers.size()) {
            throw new BaseKnownException(10000, "sap冲销验证失败，凭证号选择数据有误，请选择未冲销的数据进行冲销");
        }
        for (RecordToSapReversal recordToSapReversal : reversalList) {
            OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
            headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO));
            headerDto.setPstingDate(DateUtil.dateFormat(new Date(), "yyyy-MM-dd"));
            headerDto.setMjahr(recordToSapReversal.getPostYear());
            headerDto.setMblnr(recordToSapReversal.getPostNumber());
            SapBaseResponseDto responseDto = sapService.writeOff(headerDto);
            if ("S".equals(responseDto.getEsMessage().getMsgty())) {
                SapPost sapPost = sapPostMapper.selectOne(new LambdaUpdateWrapper<SapPost>()
                        .eq(SapPost::getCertificateNumber, recordToSapReversal.getPostNumber())
                        .eq(SapPost::getCertificateYear, recordToSapReversal.getPostYear())
                        .last("LIMIT 1"));
                if(StringUtil.isNotEmpty(sapPost)){
                    sapPost.setState("3");
                    sapPostMapper.updateById(sapPost);
                }
            } else {
                log.error("sap冲销失败，原因：" + responseDto.getEsMessage().getMsgtx());
                throw new BaseKnownException(10000, "sap冲销失败，原因：" + responseDto.getEsMessage().getMsgtx());
            }
        }
    }

    @Override
    public List<AcceptDetailDTO> getAcceptDetail(Integer id) {
        IssueOrderDetail issueOrderDetail = issueOrderDetailMapper.selectById(id);
        if("1".equals(issueOrderDetail.getType()) || "1".equals(issueOrderDetail.getIsSurplus())){
            return issueOrderDetailMapper.getAcceptDetailDou(id);
        }else {
            return issueOrderDetailMapper.getAcceptDetail(id);
        }
    }

    @Override
    public List<IssueDetailByItem> getDetailByItems(List<Integer> itemIds) {
        List<IssueDetailByItem> list = new ArrayList<>();
        itemIds.forEach(item ->{
            IssueOrderItem issueOrderItem = Optional.ofNullable(issueOrderItemMapper.selectById(item)).orElse(new IssueOrderItem());
            IssueOrder issueOrder = Optional.ofNullable(issueOrderMapper.selectById(issueOrderItem.getOrderId())).orElse(new IssueOrder());
            if("1".equals(issueOrder.getType()) || (StringUtil.isEmpty(issueOrder.getBucketWeight()) && StringUtil.isEmpty(issueOrder.getSpecificationId()) && StringUtil.isNotEmpty(issueOrder.getId()))){
                list.addAll(Optional.ofNullable(issueOrderDetailMapper.getDetailByItemsOfDou(item)).orElse(new ArrayList<>()));
            } else if ("2".equals(issueOrder.getType())) {
                list.addAll(Optional.ofNullable(issueOrderDetailMapper.getDetailByItemsOfDai(item)).orElse(new ArrayList<>()));
            }
        });
        return list;
    }

    /**
     * 曲粉昨日出库统计--制曲看板
     * @return
     */
    @Override
    public OutboundStatisticsDTO getOutboundStatistics() {
        OutboundStatisticsDTO data = new OutboundStatisticsDTO();
        Date startDate = DateUtil.datePlus(-1, DateUtil.getDayFirstSecond(new Date()));
        Date endDate = DateUtil.datePlus(-1, DateUtil.getDayLastSecond(new Date()));
        Map<Integer, SackSpecification> specificationMap = new HashMap<>();
        List<IssueOrderDetail> issueOrderDetails = issueOrderDetailMapper.selectList(new LambdaUpdateWrapper<IssueOrderDetail>().eq(IssueOrderDetail::getDeleted, false).ge(IssueOrderDetail::getCreateTime, startDate).le(IssueOrderDetail::getCreateTime, endDate));
        //黄舣 "709", "710", "711", "712", "713", "714", "715", "716", "717", "718"
        //对应的centerId 2, 92, 93, 94, 3, 95, 96, 97, 67, 4, 165
        List<Integer> hyCenter = Arrays.asList(2, 92, 93, 94, 3, 95, 96, 97, 67, 4, 165);
        List<IssueOrderDetail> hyList = issueOrderDetails.stream().filter(d -> hyCenter.contains(d.getCenterId())).collect(Collectors.toList());
        data.setHy(unitConversion(computeIssueSum(hyList, specificationMap)));
        //罗汉 "702", "703", "704", "705", "706", "707", "708"
        //对应centerId 348, 349, 350, 351, 352, 353, 373
        List<Integer> lhCenter = Arrays.asList(348, 349, 350, 351, 352, 353, 373);
        List<IssueOrderDetail> lhList = issueOrderDetails.stream().filter(d -> lhCenter.contains(d.getCenterId())).collect(Collectors.toList());
        data.setLh(unitConversion(computeIssueSum(lhList, specificationMap)));
        //非遗 国窖 小市 皂角巷
        //对应的centerId 385 384 386
        List<Integer> fyCenter = Arrays.asList(385, 384, 386);
        List<IssueOrderDetail> fyList = issueOrderDetails.stream().filter(d -> fyCenter.contains(d.getCenterId())).collect(Collectors.toList());
        data.setFy(unitConversion(computeIssueSum(fyList, specificationMap)));
        //GWQ 高温曲物料 GWQ 中草药曲 低温大曲 原料强化曲 外采购曲 强化曲 传统8 传统6 传统4 传统2 机械曲6
        //对应的物料id 1423, 2711, 2693, 1418, 1417, 1416, 1415, 1413, 1412, 1411
        List<Integer> gwqMaterialList = Arrays.asList(1423, 2711, 2693, 1418, 1417, 1416, 1415, 1413, 1412, 1411);
        List<IssueOrderDetail> gwqList = issueOrderDetails.stream().filter(d -> gwqMaterialList.contains(d.getMaterialId())).collect(Collectors.toList());
        data.setGwq(unitConversion(computeIssueSum(gwqList, specificationMap)));
        return data;
    }

    @Override
    public List<IssueWeight> weightSummary(IssueWeightQuery issueWeightQuery) {
        List<IssueWeight> weightSummaryList = new ArrayList<>();
        List<IssueWeight> weightContrast = issueOrderDetailMapper.getWeightContrast(issueWeightQuery);
        List<String> licenseList = weightContrast.stream().map(IssueWeight::getLicensePlateNumber).distinct().collect(Collectors.toList());
        for (String license : licenseList) {
            //本车的信息
            List<IssueWeight> licenseWeightList = weightContrast.stream().filter(w -> w.getLicensePlateNumber().equals(license)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(licenseWeightList)) {
                continue;
            }
            IssueWeight issueWeight = new IssueWeight();
            issueWeight.setLicensePlateNumber(license);
            //过磅重量
            issueWeight.setNetWeight(licenseWeightList.stream().filter(w -> w.getNetWeight() != null && w.getAppearanceTime() != null).map(IssueWeight::getNetWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
            List<String> demandList = licenseWeightList.stream().filter(w -> w.getAppearanceTime() == null).map(IssueWeight::getDemandNumber).collect(Collectors.toList());
            //发放重量 -- 需要过滤掉没有过磅的数据
            Integer issueWeightSum = issueOrderDetailMapper.getWeighingInformationDetail(issueWeight, issueWeightQuery.getStartTime(), issueWeightQuery.getEndTime(), demandList);
            issueWeight.setIssueWeight(new BigDecimal(issueWeightSum));
            issueWeight.setNumCount(licenseWeightList.size());
            issueWeight.setDifferenceWeight(issueWeight.getNetWeight().subtract(issueWeight.getIssueWeight()));
            weightSummaryList.add(issueWeight);
        }
        return weightSummaryList;
        //根据车辆过磅信息查询曲粉发放记录
//        //查询出车辆过磅信息根据每次过磅分组
//        org.springframework.data.domain.Page<IssueWeight> page = PageHelperUtil.getPage(issueOrderDetailMapper::getWeighingInformation, issueWeightQuery, IssueWeight.class);
//        //根据车辆过磅信息查询曲粉发放记录
//        if (StringUtil.isNotEmpty(page.getContent())) {
//            page.getContent().forEach(issueWeight -> {
//                Integer issueWeightSum = issueOrderDetailMapper.getWeighingInformationDetail(issueWeight, issueWeightQuery.getStartTime(), issueWeightQuery.getEndTime(), demandList);
//                if (StringUtil.isNotEmpty(issueWeightSum)) {
//                    issueWeight.setIssueWeight(new BigDecimal(issueWeightSum));
//                    if (StringUtil.isNotEmpty(issueWeight.getIssueWeight()) && StringUtil.isNotEmpty(issueWeight.getNetWeight())) {
//                        issueWeight.setDifferenceWeight(issueWeight.getNetWeight().subtract(issueWeight.getIssueWeight()));
//                    }
//                }
//            });
//        }
//        return page;
    }

    /**
     * 曲粉发放重量对比导出
     * @param queryDTO
     * @return
     */
    @Override
    public ResultVO<ExcelExportDto> weightContrastExport(IssueWeightQuery queryDTO) throws IOException, IllegalAccessException {
        //查询出车辆过磅信息根据每次过磅分组
        List<WeightContrastExportDTO> weightContrast = issueOrderDetailMapper.getWeightContrastExportData(queryDTO);
        //统计发放总重量
        Map<String, BigDecimal> weightTotalMap = new HashMap<>();
        for (WeightContrastExportDTO weightContrastExportDTO : weightContrast) {
            if (StringUtils.isNotEmpty(weightContrastExportDTO.getDemandNumber())) {
                BigDecimal weightTotal = weightTotalMap.get(weightContrastExportDTO.getDemandNumber());
                if (weightTotal != null) {
                    weightContrastExportDTO.setIssueWeightTotal(weightTotal);
                } else {
                    BigDecimal reduce = weightContrast.stream().filter(w -> weightContrastExportDTO.getDemandNumber().equals(w.getDemandNumber())).map(WeightContrastExportDTO::getIssueWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    weightTotalMap.put(weightContrastExportDTO.getDemandNumber(), reduce);
                    weightContrastExportDTO.setIssueWeightTotal(reduce);
                }
            }
            //差异值
            if (weightContrastExportDTO.getIssueWeightTotal() != null && weightContrastExportDTO.getNetWeight() != null) {
                weightContrastExportDTO.setDifferenceWeight(weightContrastExportDTO.getNetWeight().subtract(weightContrastExportDTO.getIssueWeightTotal()));
            }
        }
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(weightContrast, "曲粉发放重量对比列表", WeightContrastExportDTO.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("曲粉发放重量对比.xls");
        return ResultVO.success(excelExportDto);
    }

    /**
     * 曲粉需求发放统计--制曲看板
     * @return
     */
    @Override
    public DemandIssueStatisticsDTO getDemandIssueStatistics() {
        DemandIssueStatisticsDTO data = new DemandIssueStatisticsDTO();
        //截止时间 前一天的 23:59:59
        Date endDate = DateUtil.getDayLastSecond(new Date());
        Date startDate = DateUtil.datePlus(-1, endDate);
        //需求
        List<SingleDemand> singleDemands = singleDemandMapper.selectList(new LambdaUpdateWrapper<SingleDemand>().eq(SingleDemand::getDeleted, false).ge(SingleDemand::getUseTime, startDate).le(SingleDemand::getUseTime, endDate));
        if (!CollectionUtils.isEmpty(singleDemands)) {
            List<Integer> demandIds = singleDemands.stream().map(SingleDemand::getId).collect(Collectors.toList());
            List<SingleDemandDetail> singleDemandDetails = singleDemandDetailMapper.selectList(new LambdaUpdateWrapper<SingleDemandDetail>().eq(SingleDemandDetail::getDeleted, false).eq(SingleDemandDetail::getType, 1).in(SingleDemandDetail::getDemandId, demandIds));
            data.setDemandSum(unitConversion(singleDemandDetails.stream().map(SingleDemandDetail::getBucketWeight).reduce(BigDecimal.ZERO, BigDecimal::add)));
        }
        //发放
        List<IssueOrderDetail> issueOrderDetails = issueOrderDetailMapper.selectList(new LambdaUpdateWrapper<IssueOrderDetail>().eq(IssueOrderDetail::getDeleted, 0).eq(IssueOrderDetail::getType, 1).ge(IssueOrderDetail::getIssueTime, startDate).le(IssueOrderDetail::getIssueTime, endDate));
        data.setIssueSum(unitConversion(issueOrderDetails.stream().map(IssueOrderDetail::getBucketWeight).reduce(BigDecimal.ZERO, BigDecimal::add)));
        return data;
    }

    @Override
    public List<IssueOrderDetailVO> listDetailByItemIdAndLocation(Integer id, Integer centerId, Integer locationId) {
        return issueOrderDetailMapper.listDetailByItemIdAndLocation(id, locationId);
    }

    /**
     * 单位转换，这里用kg -> t
     * @return
     */
    private BigDecimal unitConversion(BigDecimal kg){
        return kg.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算发放曲粉重量
     *
     * @param orderDetails
     * @param specificationMap
     * @return
     */
    @NotNull
    private BigDecimal computeIssueSum(List<IssueOrderDetail> orderDetails, Map<Integer, SackSpecification> specificationMap) {
        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal nextNumber;
        for (IssueOrderDetail detail : orderDetails) {
            if ("1".equals(detail.getType())) {
                //斗装
                Integer issueNumber = detail.getIssueNumber();
                if (issueNumber == null) {
                    issueNumber = 1;
                }
                nextNumber = detail.getBucketWeight().multiply(new BigDecimal(issueNumber));
            } else {
                //袋装
                if ("1".equals(detail.getIsSurplus())) {
                    //散装
                    nextNumber = detail.getIssueSurplusNumber();
                } else {
                    SackSpecification sackSpecification = specificationMap.get(detail.getSpecificationId());
                    if (sackSpecification == null) {
                        sackSpecification = sackSpecificationService.getById(detail.getSpecificationId());
                        specificationMap.put(detail.getSpecificationId(), sackSpecification);
                    }
                    nextNumber = BigDecimal.valueOf((long) sackSpecification.getWeight() * detail.getIssueNumber());
                }
            }
            sum = sum.add(nextNumber);
        }
        return sum;
    }
}
