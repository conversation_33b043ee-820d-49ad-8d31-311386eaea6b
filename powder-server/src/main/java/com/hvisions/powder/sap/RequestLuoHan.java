package com.hvisions.powder.sap;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.powder.dto.contactForLuoHa.BaseData;
import com.hvisions.powder.sap.constant.SapConst;
import com.hvisions.powder.sap.dto.HttpInfo;
import com.hvisions.powder.sap.dto.luohan.BaseRequestDto;
import com.hvisions.powder.sap.dto.luohan.LuoHanBaseRequestDto;
import com.hvisions.powder.sap.entity.LhPost;
import com.hvisions.powder.sap.mapper.LhPostMapper;
import com.hvisions.powder.utils.CopyUtil;
import com.hvisions.powder.utils.DateUtil;
import com.hvisions.powder.utils.RestTemplateUtil;
import com.hvisions.powder.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

import static com.hvisions.powder.sap.constant.SapConst.getBaseAuthHeaders;

/**
 * @描述:
 * @作者: 刘文勇
 * @日期: 2024/4/24 17:35
 * @版本 1.0
 */
@Slf4j
@Component
public class RequestLuoHan {

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Autowired
    private LhPostMapper lhPostMapper;

    public Object dockingLuoHan(BaseData baseData, String url) {
        LuoHanBaseRequestDto.Request.List list = CopyUtil.simpleCopy(baseData,LuoHanBaseRequestDto.Request.List.class);
        LuoHanBaseRequestDto.Request.Head head = new LuoHanBaseRequestDto.Request.Head();
        //根据url封装不同的head
        if(url.equals(SapConst.Luo_Han1)){

        }
        LuoHanBaseRequestDto.Request request = new LuoHanBaseRequestDto.Request(head,list);
        LuoHanBaseRequestDto luoHanBaseRequestDto = new LuoHanBaseRequestDto(request);
        LhPost lhPost = new LhPost();
        lhPost.setBaseName("罗汉");
        lhPost.setCenterName("708");
        lhPost.setPostTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        lhPost.setUrl(url);
        lhPost.setStatus("2");
        lhPost.setBody(JSONObject.toJSONString(luoHanBaseRequestDto));
        lhPost.setControllerName("mes调用同步罗汉8中心");
        lhPostMapper.insert(lhPost);
        Object object = baseMethod(luoHanBaseRequestDto, url);
        Map<String, Object> map = BeanUtil.beanToMap(object);
        if(StringUtil.isNotEmpty(object) && "200".equals(map.get("code").toString())){
            lhPost.setStatus("1");
            lhPostMapper.updateById(lhPost);
        }
        return object;
    }

    public Object baseMethod(BaseRequestDto baseRequestDto, String url) {
        final LogDto log = getBaseLog("罗汉8中心", url,
                "同步罗汉8中心");
        log.setLogType(2);

        //封装http请求返回结果
        final HttpInfo httpInfo = new HttpInfo();
        httpInfo.setRequestParams(baseRequestDto);

        //调用tms接口
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = RestTemplateUtil.post(
                    url,
                    getBaseAuthHeaders(),
                    baseRequestDto,
                    String.class);
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(httpInfo));
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "8中心接口调用失败，原因：" + e.getMessage());
        }
        System.out.println("调用8中心MES结果:" + responseEntity.getBody());

        final String responseBody = responseEntity.getBody();
        Object object = JSONObject.parseObject(responseBody, Object.class);
        httpInfo.setResponseBody(object);
        httpInfo.setStatusCode(responseEntity.getStatusCode().toString());
        log.setLogParameter(JSONObject.toJSONString(httpInfo));
        log.setLogType(1);
        logCaptureClient.logRecord(log);
        return object;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用同步罗汉8中心");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}
