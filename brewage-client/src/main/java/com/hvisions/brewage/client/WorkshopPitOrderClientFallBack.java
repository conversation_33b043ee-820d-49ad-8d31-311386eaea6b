package com.hvisions.brewage.client;

import com.hvisions.brewage.dto.mkwine.vo.WorkshopPitOrderQmVO;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;

/**
 * @Description: WorkshopPitOrderClientFallBack
 * @author: yyy
 * @time: 2023/12/12 10:11
 */
@Component
public class WorkshopPitOrderClientFallBack extends BaseFallbackFactory<WorkshopPitOrderClient> {

    @Override
    public WorkshopPitOrderClient getFallBack(ResultVO vo) {
        return new WorkshopPitOrderClient() {
            @Override
            public ResultVO<WorkshopPitOrderQmVO> getOrderQmInfo(String orderCode) {
                return vo;
            }
        };
    }
}









