package com.hvisions.rawmaterial.dto.storage.shipment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @description 盘点任务分页查询条件
 *
 * <AUTHOR>
 * @date 2022/2/28 17:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "盘点任务分页查询条件")
public class ShipmentOrderPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "盘点任务单号")
    private String orderNo;

    @ApiModelProperty(value = "状态：0-待盘点，1-已盘点，2-已过账")
    private String state;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

}
