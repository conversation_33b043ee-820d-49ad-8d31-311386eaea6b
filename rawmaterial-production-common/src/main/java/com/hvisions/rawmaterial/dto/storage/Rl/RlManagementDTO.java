package com.hvisions.rawmaterial.dto.storage.Rl;

import com.hvisions.rawmaterial.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 库区库位管理新增or修改
 * @author: Jcao
 * @time: 2022/4/24 13:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "库区库位管理新增or修改")
public class RlManagementDTO extends SysBaseDTO {

    @ApiModelProperty(value = "仓库id")
    @NotNull(message = "仓库id不能为空！")
    private Integer warehouseId;

    @ApiModelProperty(value = "编码")
    @NotNull(message = "编码不能为空！")
    private String code;

    @ApiModelProperty(value = "名称")
    @NotNull(message = "名称不能为空！")
    private String name;

    @ApiModelProperty(value = "中控对应数据")
    private String centralData;

    @ApiModelProperty(value = "安全库存")
    private BigDecimal safetyStock;

    @ApiModelProperty(value = "容积")
    private BigDecimal volume;

    @ApiModelProperty(value = "父节点id")
    private Integer parentId;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料类型 0：高粱 1：稻壳 2小麦")
    private Integer materialType;
}
