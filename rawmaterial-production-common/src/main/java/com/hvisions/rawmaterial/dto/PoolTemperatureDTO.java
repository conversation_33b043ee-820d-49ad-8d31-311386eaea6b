package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@ApiModel(description = "车辆运输管理配置信息")
public class PoolTemperatureDTO {

    @NotNull(message = "日期不能为空")
    @ApiModelProperty(value = "日期", example = "2023-03-28")
    private LocalDate date;

    @NotNull(message = "酿酒中心不能为空")
    @ApiModelProperty(value = "酿酒中心", example = "709、713、718")
    private String center;

    @NotNull(message = "车间不能为空")
    @ApiModelProperty(value = "车间", example = "1、2、3、4、5")
    private String location;

    @NotNull(message = "窖号不能为空")
    @ApiModelProperty(value = "窖号", example = "19749")
    private String pitCode;

    @NotNull(message = "上层平均温度不能为空")
    @ApiModelProperty(value = "上层平均温度")
    private Float top;

    @NotNull(message = "中层平均温度不能为空")
    @ApiModelProperty(value = "中层平均温度")
    private Float middle;

    @NotNull(message = "下层平均温度不能为空")
    @ApiModelProperty(value = "下层平均温度")
    private Float bottom;
}
