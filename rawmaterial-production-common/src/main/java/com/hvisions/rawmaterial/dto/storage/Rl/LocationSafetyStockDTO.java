package com.hvisions.rawmaterial.dto.storage.Rl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 库位库存和安全库存数量dto
 * @author: Jcao
 * @time: 2022/4/24 13:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "库位库存和安全库存数量dto")
public class LocationSafetyStockDTO {

    @ApiModelProperty(value = "安全库存数量")
    private BigDecimal safetyStock;

    @ApiModelProperty(value = "当前库存数量")
    private BigDecimal stockQuantity;


}
