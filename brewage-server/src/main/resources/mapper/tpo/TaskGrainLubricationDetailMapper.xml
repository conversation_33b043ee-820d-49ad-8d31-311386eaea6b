<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskGrainLubricationDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskGrainLubricationDetail">
    <!--@Table t_po_task_grain_lubrication_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_grain_lubrication_id" jdbcType="INTEGER" property="taskGrainLubricationId" />
    <result column="runliang_machine_no" jdbcType="VARCHAR" property="runliangMachineNo" />
    <result column="runliang_water_temperature" jdbcType="VARCHAR" property="runliangWaterTemperature" />
    <result column="runliang_water_weight" jdbcType="VARCHAR" property="runliangWaterWeight" />
    <result column="runliang_duration" jdbcType="VARCHAR" property="runliangDuration" />
    <result column="stir_duration" jdbcType="VARCHAR" property="stirDuration" />
    <result column="waiting_materials_duration" jdbcType="VARCHAR" property="waitingMaterialsDuration" />
    <result column="sorghum_fragmentation_setting_value" jdbcType="VARCHAR" property="sorghumFragmentationSettingValue" />
    <result column="sorghum_fragmentation_actual_value" jdbcType="VARCHAR" property="sorghumFragmentationActualValue" />
    <result column="single_container_sorghum_setting_value" jdbcType="VARCHAR" property="singleContainerSorghumSettingValue" />
    <result column="single_container_sorghum_actual_value" jdbcType="VARCHAR" property="singleContainerSorghumActualValue" />
    <result column="runliang_circulating_water_consumption" jdbcType="VARCHAR" property="runliangCirculatingWaterConsumption" />
    <result column="runliang_circulating_water_cooling_time" jdbcType="TIMESTAMP" property="runliangCirculatingWaterCoolingTime" />
    <result column="runliang_time" jdbcType="TIMESTAMP" property="runliangTime" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_grain_lubrication_id, 
    runliang_machine_no, runliang_water_temperature, runliang_water_weight, runliang_duration, 
    stir_duration, waiting_materials_duration, sorghum_fragmentation_setting_value, sorghum_fragmentation_actual_value, 
    single_container_sorghum_setting_value, single_container_sorghum_actual_value, runliang_circulating_water_consumption, 
    runliang_circulating_water_cooling_time, runliang_time, pit_no, record_time, out_material_type,
    enter_material_type, material_layer, grain_input_type, steaming_task_no, iot_task_no, 
    task_status, created_at, completed_at,
  </sql>
</mapper>