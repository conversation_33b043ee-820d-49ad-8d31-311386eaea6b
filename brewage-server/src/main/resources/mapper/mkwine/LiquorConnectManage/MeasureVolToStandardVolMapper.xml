<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.LiquorConnectManage.MeasureVolToStandardVolMapper">

    <!-- 查询全部标准酒度列表 -->
    <select id="getAllMeasureVolToStandardVol"
            resultType="com.hvisions.brewage.dto.mkwine.vo.MeasureVolToStandardVolVO">
        select * from t_po_measure_vol_to_standard_vol
    </select>

    <!--  分页获取标准酒度列表  -->
    <select id="getMeasureVolToStandardVolPage"
            resultType="com.hvisions.brewage.dto.mkwine.vo.MeasureVolToStandardVolVO">
        select * from t_po_measure_vol_to_standard_vol
        <where>
            <if test="measureVol != null">
                measure_vol like CONCAT(#{measureVol},'%')
            </if>
            <if test="measureTemperature != null">
                AND measure_temperature like CONCAT(#{measureTemperature},'%')
            </if>
        </where>

        limit #{page},#{pageSize}
    </select>

    <!--  获取标准酒度列表中的总条数  -->
    <select id="getCount" resultType="java.lang.Integer">
        select count(*) from t_po_measure_vol_to_standard_vol
        <where>
            <if test="measureVol != null">
                measure_vol like CONCAT('%',#{measureVol},'%')
            </if>
            <if test="measureTemperature != null">
                AND measure_temperature like CONCAT(#{measureTemperature},'%')
            </if>
        </where>
    </select>

    <!-- 获取当前数据表最大 id -->
    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id) from t_po_measure_vol_to_standard_vol;
    </select>

    <select id="getMeasureVolToStandardVol"
            resultType="com.hvisions.brewage.dto.mkwine.vo.MeasureVolToStandardVolVO">
        select * from t_po_measure_vol_to_standard_vol
        where measure_vol = #{measureVol} and measure_temperature = #{measureTemperature};
    </select>

    <select id="findByMeasureVolAndMeasureTemperature"
            resultType="com.hvisions.brewage.dto.mkwine.vo.MeasureVolToStandardVolVO">
        select * from t_po_measure_vol_to_standard_vol
        where measure_vol = #{measureVol} and measure_temperature = #{measureTemperature};
    </select>

    <update id="importUpdate">
        update t_po_measure_vol_to_standard_vol set standard_vol = #{standardVol}
        where measure_vol = #{measureVol} and measure_temperature = #{measureTemperature};
    </update>

    <!-- 添加标准酒度 -->
    <insert id="addMeasureVolToStandardVol">
        insert into t_po_measure_vol_to_standard_vol(
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="measureVol != null">
                measure_vol,
            </if>
            <if test="measureTemperature != null">
                measure_temperature,
            </if>
            <if test="standardVol != null">
                standard_vol,
            </if>
        </trim>
        )
        value(
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="measureVol != null">
                #{measureVol},
            </if>
            <if test="measureTemperature != null">
                #{measureTemperature},
            </if>
            <if test="standardVol != null">
                #{standardVol},
            </if>
        </trim>
        )
    </insert>
    
    <!-- 修改标准酒度 -->
    <update id="updateMeasureVolToStandardVol">
        update t_po_measure_vol_to_standard_vol
        <trim prefix="set" suffixOverrides=",">
            <if test="measureVol != null">
                measure_vol = #{measureVol},
            </if>
            <if test="measureTemperature != null">
                measure_temperature = #{measureTemperature},
            </if>
            <if test="standardVol != null">
                standard_vol = #{standardVol},
            </if>
        </trim>
        where
            id = #{id}
    </update>
    
    <!-- 删除标准酒度 -->
    <delete id="deleteMeasureVolToStandardVol">
        delete from t_po_measure_vol_to_standard_vol
        where id = #{id}
    </delete>

    <select id="addDataOrUpdate" resultType="java.lang.Integer">
        select count(*) from t_po_measure_vol_to_standard_vol where measure_vol = #{measureVol}
                                                                and measure_temperature = #{measureTemperature}
    </select>

    <select id="standardVol" resultType="java.math.BigDecimal">
        select
            standard_vol
        from
            t_po_measure_vol_to_standard_vol
        where
            measure_vol = #{vol} and
            measure_temperature = #{temperate}
    </select>

    <select id="getStandardVolList"
            resultType="com.hvisions.brewage.dto.mkwine.vo.MeasureVolToStandardVolVO">
        select * from t_po_measure_vol_to_standard_vol
        where measure_vol IN
        <foreach collection="measureVolList" index="index" item="item" open="(" separator=", " close=")">
            #{item,jdbcType=DECIMAL}
        </foreach>
        and measure_temperature IN
        <foreach collection="temperatureList" index="index" item="item" open="(" separator=", " close=")">
            #{item,jdbcType=DECIMAL}
        </foreach>
    </select>
</mapper>