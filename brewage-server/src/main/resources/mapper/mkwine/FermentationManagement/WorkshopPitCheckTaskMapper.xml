<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.FermentationManagement.WorkshopPitCheckTaskMapper">

    <resultMap id="TaskResultMap" type="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskVO">
        <id property="id" column="id"/>
        <result property="taskCode" column="task_code"/>
        <result property="centerId" column="center_id"/>
        <result property="locationId" column="location_id"/>
        <result property="checkItemId" column="check_item_id"/>
        <result property="checkItemCode" column="check_item_code"/>
        <result property="checkItemName" column="check_item_name"/>
        <result property="crewId" column="crew_id"/>
        <result property="crewCode" column="crew_code"/>
        <result property="crewName" column="crew_name"/>
        <result property="planStartTime" column="plan_start_time"/>
        <result property="taskEndTime" column="task_end_time"/>
        <result property="qualifiedCount" column="qualified_count"/>
        <result property="disqualifiedCount" column="disqualified_count"/>
        <result property="taskStatus" column="task_status"/>
    </resultMap>

    <select id="getAllTask" resultMap="TaskResultMap">
        select
            t1.*,
            t2.check_item_code, t2.check_item_name,
            h1.crew_code, h1.crew_name
        from t_po_workshop_pit_check_task t1
        left join t_po_check_items t2 on t1.check_item_id = t2.id
        left join schedule.hv_bm_crew h1 on t1.crew_id = h1.id
        order by t1.plan_start_time desc;
    </select>

    <select id="getTaskByPage" resultMap="TaskResultMap">
        select
            t1.*,
            t2.check_item_code, t2.check_item_name,
            h1.crew_code, h1.crew_name,
            h2.code as cl_code
        from t_po_workshop_pit_check_task t1
        left join t_po_check_items t2 on t1.check_item_id = t2.id
        left join schedule.hv_bm_crew h1 on t1.crew_id = h1.id
        left join equipment.hv_bm_location h2 on h2.id = t1.location_id
        where 1 = 1
        <if test="centerId != null"> and t1.center_id = #{centerId}</if>
        <if test="locationId != null"> and t1.location_id = #{locationId}</if>
        <if test="taskCode != null"> and t1.task_code like CONCAT('%', #{taskCode} , '%')</if>
        <if test="checkItemName != null"> and t2.check_item_name like CONCAT('%', #{checkItemName} , '%')</if>
        <if test="crewName!= null">and h1.crew_name like CONCAT('%', #{crewName} , '%')</if>
        <if test="planStartTime != null and planEndTime != null">
            and (t1.plan_start_time between #{planStartTime} and #{planEndTime})
        </if>
        <if test="taskStartTime != null and taskEndTime != null">
            and (t1.task_end_time between #{taskStartTime} and #{taskEndTime})
        </if>
        order by t1.plan_start_time desc, t1.task_status
        limit #{page}, #{pageSize};
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select count(*)
        from t_po_workshop_pit_check_task t1
        left join t_po_check_items t2 on t1.check_item_id = t2.id
        left join schedule.hv_bm_crew h1 on t1.crew_id = h1.id
        where 1 = 1
        <if test="centerId != null"> and t1.center_id = #{centerId}</if>
        <if test="locationId != null"> and t1.location_id = #{locationId}</if>
        <if test="taskCode != null"> and t1.task_code like CONCAT('%', #{taskCode} , '%')</if>
        <if test="checkItemName != null"> and t2.check_item_name like CONCAT('%', #{checkItemName} , '%')</if>
        <if test="crewName!= null">and h1.crew_name like CONCAT('%', #{crewName} , '%')</if>
        <if test="planStartTime != null and planEndTime != null">
            and (t1.plan_start_time between #{planStartTime} and #{planEndTime})
        </if>
        <if test="taskStartTime != null and taskEndTime != null">
            and (t1.task_end_time between #{taskStartTime} and #{taskEndTime})
        </if>
    </select>

    <!-- 根据班组 id 去窖池包干中查找包干的窖池 id -->
    <select id="getPitResponsibleIds" resultType="java.lang.String">
        select
            pit_responsible_ids
        from
            t_po_pit_responsible_team
        where
            workshop_center_id = #{centerId} and
            workshop_id = #{locationId} and
            team_id = #{teamId};
    </select>

    <!--  获取检查任务最大 id -->
    <select id="getTaskMaxId" resultType="java.lang.Integer">
        select max(id) from t_po_workshop_pit_check_task;
    </select>

    <!-- 根据检查任务 id 去找检查任务明细 -->
    <select id="getTaskItems"
            resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskItemsVO">
        select
            t1.*,
            t2.task_code,
            t3.full_pit_id as pit_code,
            t4.check_item_name as task_name
--             t5.check_anomaly_category_code,
--             t5.check_anomaly_category_name,
--             t6.measure_code,
--             t6.measures_name
        from t_po_workshop_pit_check_task_items t1
        inner join t_po_workshop_pit_check_task t2 on t1.task_id = t2.id
        inner join t_po_workshop_full_pit t3 on t1.pit_id = t3.id
        left join t_po_check_items t4 on t2.check_item_id = t4.id
--         left join t_po_check_anomaly_category t5 on t1.check_anomaly_category_id = t5.id
--         left join t_po_check_anomaly_measures t6 on t1.measures_id = t6.id
        where t1.task_id = #{id}
        order by check_status asc, log_time desc;
    </select>

    <!-- 根据检查任务 id 去明细中找异常的 -->
    <select id="getTaskItemsException"
            resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskItemsVO">
        select
            t1.*,
            t2.task_code,
            t2.plan_start_time,
            t3.full_pit_id as pit_code,
            t4.check_item_name as task_name
--             t5.check_anomaly_category_code,
--             t5.check_anomaly_category_name,
--             t6.measure_code,
--             t6.measures_name
        from t_po_workshop_pit_check_task_items t1
        inner join t_po_workshop_pit_check_task t2 on t1.task_id = t2.id
        inner join t_po_workshop_full_pit t3 on t1.pit_id = t3.id
        left join t_po_check_items t4 on t2.check_item_id = t4.id
--         left join t_po_check_anomaly_category t5 on t1.check_anomaly_category_id = t5.id
--         left join t_po_check_anomaly_measures t6 on t1.measures_id = t6.id
        where t1.check_result = 1
        <if test="ids.length > 0">
            and (
            <foreach collection="ids" item="item" separator="or">
                t1.task_id = #{item}
            </foreach>
            )
        </if>
        order by plan_start_time desc, log_time desc;
    </select>

    <!-- 通过连窖号查询 id -->
    <select id="getPit"
            resultType="com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit">
        select id, full_pit_id from t_po_workshop_full_pit where full_pit_id like CONCAT('%', #{fullPitId}, '%')
        limit 20
    </select>

    <!-- 获取检查项目中未删除并且启用的检查项目 -->
    <select id="getEnableItems"
            resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskVO">
        select
               t1.id as check_item_id,
               t1.first_check_time,
               t1.check_cycle_time,
               t1.check_cycle_time_unit,
               t1.is_ahead,
               t1.ahead_time,
               t1.ahead_time_unit,
               count(t2.id) as nums
        from
             t_po_check_items t1 left join
             t_po_workshop_pit_check_task t2 on t1.id = t2.check_item_id
        where
              is_deleted = false and is_enable = true group by t1.id;
    </select>

    <!-- 根据 check_item_id 查询检查任务中是否有关联该项目的检查任务，若没有则证明该项目还没有生成相关的检查任务 -->
    <select id="getCheckItemNums" resultType="java.lang.Integer">
        select count(id) from t_po_workshop_pit_check_task where check_item_id = #{checkId};
    </select>

    <!-- 根据检查项目去检查任务中找到相关班组最近的检查任务 -->
    <select id="getMaxPlanTime"
            resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskVO">
        select t.* from t_po_workshop_pit_check_task t
        inner join (
            select id, max(plan_start_time) as plan_start_time from t_po_workshop_pit_check_task
            where check_item_id = #{checkId} group by id
            ) t2
        where t.id = t2.id and t.plan_start_time = t2.plan_start_time;
    </select>

    <!-- 当检查项目开启后，自动按窖池包干管理的班组生成检查任务，默认为待执行状态。 -->
    <select id="getEnableCrew"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.PitResponsibleTeam.PitResponsibleTeamVO">
        select * from t_po_pit_responsible_team where is_deleted = false;
    </select>

    <!-- 根据检查项目 id 获取关联的检查任务的班组 -->
    <select id="getCrewIds"
            resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskVO">
        select * from t_po_workshop_pit_check_task where check_item_id = #{checkItemId};
    </select>

    <!-- 根据车间 id 查找编号 -->
    <select id="getLocationCode" resultType="java.lang.String">
        select code from equipment.hv_bm_location where id = #{centerId};
    </select>

    <!-- 获取明细中最大id -->
    <select id="getTaskItemsMaxId" resultType="java.lang.Integer">
        select max(id) from t_po_workshop_pit_check_task_items;
    </select>

    <!-- 获取异常的检查项目明细 -->
    <select id="getException" resultType="java.lang.Integer">
        select id from t_po_workshop_pit_check_task_items where task_id = #{taskId} and check_result = true;
    </select>

    <!-- 获取目前包干窖池的班组 -->
    <select id="TeamPitIds" resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.TeamPitIds">
        select
            workshop_center_id as center_id,
            workshop_id as location_id,
            team_id,
            pit_responsible_ids
        from
            t_po_pit_responsible_team
        where
            is_deleted = false and
            workshop_center_id is not null and
            workshop_id is not null;
    </select>

    <!-- 获取当前启用的检查项目 -->
    <select id="CheckItems"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.CheckItems.CheckItemsVO">
        select * from t_po_check_items where is_deleted = false and is_enable = true;
    </select>

    <select id="CheckTasks"
            resultType="com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.WorkshopPitCheckTaskVO">
        <foreach collection="teamPitIds" item="teamPitId" separator="union">
            select
                id,
                center_id,
                location_id,
                check_item_id,
                crew_id,
                task_status,
                plan_start_time,
                task_end_time
            from
                t_po_workshop_pit_check_task
            where
                id = (select max(id) as id from t_po_workshop_pit_check_task where check_item_id = #{taskItemId} and crew_id = #{teamPitId.teamId} and is_auto = true)
        </foreach>
    </select>

    <select id="getCheckNums" resultType="java.lang.Integer">
        select
            count(id)
        from
            t_po_workshop_pit_check_task
        where
            (task_status = 0 or task_status = 1) and
            center_id = #{centerId} and
            location_id = #{locationId} and
            crew_id = #{crewId} and
            check_item_id = #{checkItemId};
    </select>

    <insert id="insertTask">
        insert into t_po_workshop_pit_check_task(
        <trim prefix="" suffixOverrides=",">
            <if test="taskCode != null">task_code,</if>
            <if test="centerId != null">center_id,</if>
            <if test="locationId != null">location_id,</if>
            <if test="checkItemId != null">check_item_id,</if>
            <if test="crewId != null">crew_id,</if>
            <if test="planStartTime != null">plan_start_time,</if>
            <if test="taskEndTime != null">task_end_time,</if>
            qualified_count,
            disqualified_count,
            is_auto,
            <if test="taskStatus != null">task_status</if>
        </trim>
        ) value(
        <trim prefix="" suffixOverrides=",">
            <if test="taskCode != null">#{taskCode},</if>
            <if test="centerId != null">#{centerId},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="checkItemId != null">#{checkItemId},</if>
            <if test="crewId != null">#{crewId},</if>
            <if test="planStartTime != null">#{planStartTime},</if>
            <if test="taskEndTime != null">#{taskEndTime},</if>
            0,
            0,
            0,
            <if test="taskStatus != null">#{taskStatus}</if>
        </trim>
        )
    </insert>

    <!-- 添加统计列表(这个是外面添加的时候跟着进行添加的) -->
    <insert id="insertTaskItem">
        insert into t_po_workshop_pit_check_task_items(
        <trim prefix="" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="pitId != null">pit_id,</if>
            check_result,
            check_status,
        </trim>
        ) value(
        <trim prefix="" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="pitId != null">#{pitId},</if>
            0,
            true,
        </trim>
        )
    </insert>

    <!-- 添加统计列表(这个是执行操作里面的添加) -->
    <insert id="insertTaskItemByObject">
        insert into t_po_workshop_pit_check_task_items(
        <trim prefix="" suffixOverrides=",">
            <if test="checkStatus != null">check_status,</if>
            <if test="taskId != null">task_id,</if>
            <if test="pitId != null">pit_id,</if>
            <if test="checkResult != null">check_result,</if>
            <if test="anomalyCategoryId != null">anomaly_category_id,</if>
            <if test="anomalyCategoryName != null">anomaly_category_name,</if>
            <if test="measuresName != null">measures_name,</if>
            <if test="logTime != null">log_time,</if>
            <if test="notes != null">notes,</if>
        </trim>
        ) value(
        <trim prefix="" suffixOverrides=",">
            <trim prefix="" suffixOverrides=",">
                <if test="checkStatus != null">#{checkStatus},</if>
                <if test="taskId != null">#{taskId},</if>
                <if test="pitId != null">#{pitId},</if>
                <if test="checkResult != null">#{checkResult},</if>
                <if test="anomalyCategoryId != null">#{anomalyCategoryId},</if>
                <if test="anomalyCategoryName != null">#{anomalyCategoryName},</if>
                <if test="measuresName != null">#{measuresName},</if>
                <if test="logTime != null">#{logTime},</if>
                <if test="notes != null">#{notes},</if>
            </trim>
        </trim>
        )
    </insert>

    <insert id="batchInsertTaskItem">
        ${sql}
    </insert>

    <insert id="Logging">
        insert into t_po_check_task_log(check_id, check_item_name, cur_time, generation_time, notes)
        values
            <foreach collection="taskLogs" item="taskLog" separator=",">
                (#{taskLog.checkId}, #{taskLog.checkItemName}, #{taskLog.curTime}, #{taskLog.generationTime}, #{taskLog.notes})
            </foreach>
    </insert>

    <insert id="batchInsertTask">
        ${checkTask}
    </insert>

    <update id="updateTask">
        update t_po_workshop_pit_check_task
        <trim prefix="set" suffixOverrides=",">
            <if test="taskEndTime != null">task_end_time = #{taskEndTime},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
        </trim>
        where id = #{id};
    </update>

    <!-- 修改正常跟异常数量 -->
    <update id="updateTaskCount">
        update t_po_workshop_pit_check_task
        set qualified_count = #{normalCount}, disqualified_count = #{exceptionCount}
        where id = #{id};
    </update>

    <!-- 废除任务 -->
    <update id="abolitionTask">
        update t_po_workshop_pit_check_task set task_status = 3 where id = #{id};
    </update>

    <!-- 修改统计列表 -->
    <update id="updateTaskItems">
        update t_po_workshop_pit_check_task_items
        <trim prefix="set" suffixOverrides=",">
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkResult != null">check_result = #{checkResult},</if>
            <if test="logTime != null">log_time = #{logTime},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="anomalyCategoryName != null"> anomaly_category_name = #{anomalyCategoryName},</if>
            <if test="measuresName != null">measures_name = #{measuresName},</if>
            <if test="anomalyCategoryId != null">anomaly_category_id = #{anomalyCategoryId},</if>
        </trim>
        where id = #{id};
    </update>

    <delete id="deleteTask">
        delete from t_po_workshop_pit_check_task where id = #{id};
    </delete>

    <!-- 删除统计列表 -->
    <delete id="deleteTaskItemById">
        delete from t_po_workshop_pit_check_task_items where id = #{id};
    </delete>


    <update id="updateByPrimaryKeySelective" parameterType="com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoWorkshopPitCheckTaskItems">
        <!--@mbg.generated-->
        update t_po_workshop_pit_check_task_items
        <set>
            <if test="checkResult != null">
                check_result = #{checkResult,jdbcType=BIT},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=BIT},
            </if>
            <if test="logTime != null">
                log_time = #{logTime,jdbcType=TIMESTAMP},
            </if>
            <if test="notes != null">
                notes = #{notes,jdbcType=VARCHAR},
            </if>
            <if test="pitId != null">
                pit_id = #{pitId,jdbcType=INTEGER},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=INTEGER},
            </if>
            <if test="anomalyCategoryId != null">
                anomaly_category_id = #{anomalyCategoryId,jdbcType=INTEGER},
            </if>
            <if test="anomalyCategoryName != null">
                anomaly_category_name = #{anomalyCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="measuresName != null">
                measures_name = #{measuresName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>