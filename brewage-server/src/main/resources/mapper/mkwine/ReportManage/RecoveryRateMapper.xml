<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.ReportManage.RecoveryRateMapper">
    <select id="getRecoveryRateExcelByVinasse"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RecoveryRate.RecoveryRateMapperVO">
        SELECT DISTINCT DATE_FORMAT(t2.open_pit_finish_time, '%Y-%m') AS `month`,
        t3.`code` AS location,
        t4.`code` AS center,
        t3.id as location_id
        FROM t_po_workshop_pit_order t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.id = t2.order_code_id
        LEFT JOIN equipment.hv_bm_location t3 ON t1.location_id = t3.id
        LEFT JOIN equipment.hv_bm_location t4 ON t1.center_id = t4.id
        LEFT JOIN brewage_plan.t_pp_vinasse_source t5 ON t1.vinasse_id = t5.id
        AND t5.deleted = 0
        WHERE t1.is_deleted = 0
        AND t5.`code` = #{vinasse}
        AND t2.open_pit_finish_time IS NOT NULL
        AND DATE_FORMAT(t2.open_pit_finish_time, '%Y-%m') between #{dto.startTime} and #{dto.endTime}
        <if test="dto.centerId != null">
            and t1.center_id = #{dto.centerId}
        </if>
        ORDER BY `month` DESC,
        location
    </select>

    <select id="getLiangQuality" resultType="java.lang.Double">
        SELECT IFNULL(SUM(p1.crushed_grains_quantity), 0)
        FROM t_po_workshop_pit_order_pot_task p1
                 LEFT JOIN t_po_workshop_pit_order_sap p2 ON p1.in_order_code = p2.order_code
                 LEFT JOIN t_po_workshop_pit_order p3 ON p2.order_code_id = p3.id
        WHERE p1.is_deleted = 0
          AND p3.location_id = #{locationId}
          AND p1.vinasse = #{vinasse}
          AND DATE_FORMAT(p2.open_pit_finish_time, '%Y-%m') = #{month}
    </select>

    <select id="getFj1Quality" resultType="java.lang.Double">
        SELECT IFNULL(SUM(p4.back_alcoholic_quantity_revise), 0)
        FROM t_po_workshop_pit_order_sap p1
                 LEFT JOIN t_po_workshop_pit_order p2 ON p1.order_code_id = p2.id
                 LEFT JOIN brewage_plan.t_pp_formula_detail p3 ON p2.formula_id = p3.formula_id
                 LEFT JOIN t_po_workshop_pit_order_pot_task p4 ON p1.order_code = p4.out_order_code
            AND p4.is_deleted = 0
        WHERE p1.is_deleted = 0
          AND p3.type = 0
          AND p2.location_id = #{locationId}
          AND p4.vinasse = #{vinasse}
          AND p3.quality > 0
          AND DATE_FORMAT(p1.open_pit_finish_time, '%Y-%m') = #{month}
          AND p3.material_name = 'FJ1'
    </select>

    <select id="getZtQuality" resultType="java.lang.Double">
        SELECT IFNULL(SUM(p4.back_alcoholic_quantity_revise), 0)
        FROM t_po_workshop_pit_order_sap p1
                 LEFT JOIN t_po_workshop_pit_order p2 ON p1.order_code_id = p2.id
                 LEFT JOIN brewage_plan.t_pp_formula_detail p3 ON p2.formula_id = p3.formula_id
                 LEFT JOIN t_po_workshop_pit_order_pot_task p4 ON p1.order_code = p4.out_order_code
            AND p4.is_deleted = 0
        WHERE p1.is_deleted = 0
          AND p3.type = 0
          AND p2.location_id = #{locationId}
          AND p4.vinasse = #{vinasse}
          AND p3.quality > 0
          AND DATE_FORMAT(p1.open_pit_finish_time, '%Y-%m') = #{month}
          AND p3.material_name = 'ZT'
    </select>

    <select id="getTo60Quantity" resultType="java.lang.Double">
        SELECT ifnull(SUM(p1.to60_quantity), 0)
        FROM t_po_workshop_receive_wine_order p1
                 LEFT JOIN t_po_workshop_handin_task p2 ON p1.handin_task_id = p2.id
                 LEFT JOIN t_po_workshop_pit_order_sap p3 ON p3.id = p1.pit_order_id
                 LEFT JOIN t_po_workshop_pit_order p4 ON p3.order_code_id = p4.id
                 LEFT JOIN brewage_plan.t_pp_vinasse_source p5 on p4.vinasse_id = p5.id and p5.deleted = 0
        WHERE p2.is_delete = 0
          AND p2.location_id = #{locationId}
          AND p5.code = #{vinasse}
          AND DATE_FORMAT(p3.open_pit_finish_time, '%Y-%m') = #{month}
    </select>

    <select id="getRecoveryRateByVinassePage"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RecoveryRate.RecoveryRateMapperVO">
        SELECT DISTINCT DATE_FORMAT(t2.open_pit_finish_time, '%Y-%m') AS `month`,
        t3.`code` AS location,
        t4.`code` AS center,
        t3.id as location_id
        FROM t_po_workshop_pit_order t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.id = t2.order_code_id
        LEFT JOIN equipment.hv_bm_location t3 ON t1.location_id = t3.id
        LEFT JOIN equipment.hv_bm_location t4 ON t1.center_id = t4.id
        LEFT JOIN brewage_plan.t_pp_vinasse_source t5 ON t1.vinasse_id = t5.id
        AND t5.deleted = 0
        WHERE t1.is_deleted = 0
        AND t5.`code` = #{vinasse}
        AND t2.open_pit_finish_time IS NOT NULL
        AND DATE_FORMAT(t2.open_pit_finish_time, '%Y-%m') between #{dto.startTime} and #{dto.endTime}
        <if test="dto.centerId != null">
            and t1.center_id = #{dto.centerId}
        </if>
        ORDER BY `month` DESC,
        location
        limit #{dto.page},#{dto.pageSize}
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select count(1)
        from (SELECT DISTINCT DATE_FORMAT(t2.open_pit_finish_time, '%Y-%m') AS `month`,
        t3.`code` AS location,
        t4.`code` AS center,
        t3.id as location_id
        FROM t_po_workshop_pit_order t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.id = t2.order_code_id
        LEFT JOIN equipment.hv_bm_location t3 ON t1.location_id = t3.id
        LEFT JOIN equipment.hv_bm_location t4 ON t1.center_id = t4.id
        LEFT JOIN brewage_plan.t_pp_vinasse_source t5 ON t1.vinasse_id = t5.id
        AND t5.deleted = 0
        WHERE t1.is_deleted = 0
        AND t2.open_pit_finish_time IS NOT NULL
        AND t5.`code` = #{vinasse}
        AND DATE_FORMAT(t2.open_pit_finish_time, '%Y-%m') between #{dto.startTime} and #{dto.endTime}
        <if test="dto.centerId != null">
            and t1.center_id = #{dto.centerId}
        </if>

        ORDER BY `month` DESC,
        location) t
    </select>
</mapper>