<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.ReportManage.RegionProductionDetailDataMapper">

    <select id="getInputMaterial"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t1.seal_confirm_time) as year,
            month(t1.seal_confirm_time) as month,
            (coalesce(sum(t3.crushed_grains_quantity),0) + (select coalesce(sum(japonica_sorghum_exc),0) + coalesce(sum(organic_sorghum_exc),0) from
                t_po_workshop_pit_order t4 left join
                t_po_workshop_pit_order_sap t5 on t5.order_code_id = t4.id where year(t4.seal_confirm_time) = year and month(t4.seal_confirm_time) = month and t4.is_deleted = 0 and t5.is_deleted = 0)
            ) as hyGrain,
            (coalesce(sum(t3.qu_quantity),0) + (select coalesce(sum(da_qu_exc),0) + coalesce(sum(organic_qu_exc),0) from
                t_po_workshop_pit_order t4 left join
                t_po_workshop_pit_order_sap t5 on t5.order_code_id = t4.id where year(t4.seal_confirm_time) = year and month(t4.seal_confirm_time) = month and t4.is_deleted = 0 and t5.is_deleted = 0)
            ) as hyWarping,
            sum(t3.ricehull_quantity) as hyChaffShell
        from
            t_po_workshop_pit_order t1 left join
            t_po_workshop_pit_order_sap t2 on t2.order_code_id = t1.id left join
            t_po_workshop_pit_order_pot_task t3 on t3.in_order_code = t2.order_code
        <where>
            t1.is_deleted = 0 and
            t2.is_deleted = 0 and
            t3.is_deleted = 0 and
            (year(t1.seal_confirm_time) between #{dto.startYear} and #{dto.endYear})
        </where>
        group by year,month
        order by year
    </select>

    <select id="getOutputMaterial"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t1.pottask_starttime) as year,
            month(t1.pottask_starttime) as month,
            (coalesce(sum(t1.crushed_grains_quantity),0) + (select coalesce(sum(japonica_sorghum_exc),0) + coalesce(sum(organic_sorghum_exc),0) from
                t_po_workshop_pit_order_pot_task t4 left join
                t_po_workshop_pit_order_sap t5 on t5.order_code = t4.in_order_code where year(t4.pottask_starttime) = year and month(t4.pottask_starttime) = month and t4.is_deleted = 0 and t5.is_deleted = 0)
            ) as hyGrainConsumption,
            (coalesce(sum(t1.qu_quantity),0) + (select coalesce(sum(da_qu_exc),0) + coalesce(sum(organic_qu_exc),0) from
                t_po_workshop_pit_order_pot_task t4 left join
                t_po_workshop_pit_order_sap t5 on t5.order_code = t4.in_order_code where year(t4.pottask_starttime) = year and month(t4.pottask_starttime) = month and t4.is_deleted = 0 and t5.is_deleted = 0)
            ) as hyWarpingConsumption
        from
            t_po_workshop_pit_order_pot_task t1 left join
            t_po_workshop_pit_order_sap t2 on t1.in_order_code = t2.order_code
        <where>
            t1.is_deleted = 0 and
            t1.pottask_starttime is not null and
            t2.is_deleted = 0 and
            (year(t1.pottask_starttime) between #{dto.startYear} and #{dto.endYear})
        </where>
        group by year,month
        order by year
    </select>

    <select id="getInputTX"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t1.pottask_starttime) as year,
            month(t1.pottask_starttime) as month,
            sum(t1.back_alcoholic_quantity_revise) as hyThrowTX
        from
            t_po_workshop_pit_order_pot_task t1 left join
            t_po_workshop_pit_order_sap t2 on t2.order_code = t1.out_order_code left join
            t_po_workshop_pit_order t3 on t3.id = t2.order_code_id left join
            brewage_plan.t_pp_formula_detail t4 on t4.formula_id = t3.formula_id
        where
            t1.is_deleted = 0 and
            t1.back_alcoholic_quantity_revise is not null and
            t3.is_deleted = 0 and
            t4.type = 0 and
            t4.quality > 0 and
            (year(t1.pottask_starttime) between #{dto.startYear} and #{dto.endYear})
        group by year,month
        order by year
    </select>

    <select id="getInputSX"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t1.actual_starttime) as year,
            month(t1.actual_starttime) as month,
            sum(t1.back_alcoholic_quantity) as hyThrowSX
        from
            t_po_workshop_pit_turnover_order t1 left join
            t_po_workshop_pit_order t2 on t2.center_id = t1.center_id left join
            brewage_plan.t_pp_formula_detail t3 on t2.formula_id = t3.formula_id
        where
            t1.back_alcoholic_quantity is not null and
            t2.is_deleted = 0 and
            t3.type = 0 and
            t3.quality > 0 and
            (year(t1.actual_starttime) between #{dto.startYear} and #{dto.endYear})
        group by year,month
        order by year
    </select>

    <select id="getOutputSX"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t1.actual_starttime) as year,
            month(t1.actual_starttime) as month,
            sum(t1.back_alcoholic_quantity) as hyConsumeSX
        from
            t_po_workshop_pit_turnover_order t1 left join
            t_po_workshop_pit_order t2 on t2.center_id = t1.center_id left join
            brewage_plan.t_pp_formula_detail t3 on t2.formula_id = t3.formula_id
        where
            t1.back_alcoholic_quantity is not null and
            t2.is_deleted = 0 and
            t3.type = 1 and
            t3.quality > 0 and
            (year(t1.actual_starttime) between #{dto.startYear} and #{dto.endYear})
        group by year,month
        order by year
    </select>

    <select id="getSteamerMouth"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t1.pottask_endtime) as year,
            month(t1.pottask_endtime) as month,
            count(if(vinasse = 'CB' or vinasse = 'CZ', vinasse,null)) as hySteamerCC,
            count(if(vinasse = 'CJ', vinasse,null)) as hySteamerCJ,
            count(if(vinasse = 'ZL', vinasse,null)) as hySteamerZL,
            count(if(vinasse = 'DZ', vinasse,null)) as hySteamerDZ
        from
            t_po_workshop_pit_order_pot_task t1
        where
            t1.is_deleted = 0 and
            (year(t1.pottask_endtime) between #{dto.startYear} and #{dto.endYear})
        group by year,month
        order by year
    </select>

    <select id="getYield"
            resultType="com.hvisions.brewage.mkwine.vo.ReportManage.RegionProductionDetailData.RegionProductionDetailDataVO">
        select
            year(t.end_time) as year,
            month(t.end_time) as month,
            (select
            sum(t1.to60_quantity) from t_po_workshop_handin_task t1
            where t1.group_level in('A','B','C','D') and year(t1.end_time) = year and month(t1.end_time) = month
            ) as hyAdd,
            sum(t.to60_quantity) as hyYield60,
            sum(t.to60_quantity * 0.9114) as hyYield65
        from
            t_po_workshop_handin_task t
        where
            (year(t.end_time) between #{dto.startYear} and #{dto.endYear})
        group by year,month
        order by year
    </select>
</mapper>
