<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.productiondisposition.StandardPotConfigurationMapper">
    <select id="getAllStandardPotConfiguration"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.StandardPotConfiguration.StandardPotConfigurationVO">
        select * from t_po_standard_pot_configuration where is_deleted = 0 order by workshop_center_id DESC
    </select>
    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id) from t_po_standard_pot_configuration order by create_time
    </select>
    <select id="getStandardPotConfigurationPage"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.StandardPotConfiguration.StandardPotConfigurationVO">
        select t1.*, e1.name as center_name
        from t_po_standard_pot_configuration t1
        left join equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id
        where
        t1.is_deleted = 0
        <if test="workshopCenterId != null">
            and t1.workshop_center_id = #{workshopCenterId}
        </if>
        order by t1.create_time DESC
        limit #{page},#{pageSize}
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        select count(*) from t_po_standard_pot_configuration
        where
        is_deleted = 0
        <if test="workshopCenterId != null">
            and workshop_center_id = #{workshopCenterId}
        </if>
    </select>

    <select id="findWorkShopCenterId"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.StandardPotConfiguration.StandardPotConfigurationVO">
        select *
        from t_po_standard_pot_configuration
        where is_deleted = 0
        <if test="workshopCenterId != null">
            and workshop_center_id = #{workshopCenterId}
        </if>

    </select>


    <insert id="addStandardPotConfiguration">
        insert into t_po_standard_pot_configuration
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="inPitOffset != null">in_pit_offset,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="outPitOffset != null">out_pit_offset,</if>
            <if test="stdInPitNum != null">std_in_pit_num,</if>
            <if test="stdOutPitNum != null">std_out_pit_num,</if>
            <if test="workshopCenterId != null">workshop_center_id,</if>
        </trim>
        )
        value
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="inPitOffset != null">#{inPitOffset},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="outPitOffset != null">#{outPitOffset},</if>
            <if test="stdInPitNum != null">#{stdInPitNum},</if>
            <if test="stdOutPitNum != null">#{stdOutPitNum},</if>
            <if test="workshopCenterId != null">#{workshopCenterId},</if>
        </trim>
        )
    </insert>
    <update id="updateStandardPotConfiguration">
        update t_po_standard_pot_configuration
        <trim prefix="set" suffixOverrides=",">
            <if test="inPitOffset != null">in_pit_offset =#{inPitOffset},</if>
            <if test="modifyTime != null">modify_time =#{modifyTime},</if>
            <if test="outPitOffset != null">out_pit_offset =#{outPitOffset},</if>
            <if test="stdInPitNum != null">std_in_pit_num =#{stdInPitNum},</if>
            <if test="stdOutPitNum != null">std_out_pit_num =#{stdOutPitNum},</if>
        </trim>
        WHERE id=#{id}
    </update>
    <update id="deleteStandardPotConfiguration">
        update t_po_standard_pot_configuration
        set is_deleted = 1,modify_time=#{now}
        where id = #{id}
    </update>
</mapper>