<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.InspectionItem.InspectionItemDetailMapper">

    <resultMap id="Transformation" type="com.hvisions.brewage.mkwine.vo.InspectionItem.InspectionItemDetailVO">
        <result column="inspection_anomaly_measures" property="inspectionAnomalyMeasures"
                typeHandler="com.hvisions.brewage.utils.JsonTypeHandler"/>
    </resultMap>

    <!--获取全部巡检项目明细-->
    <select id="getAllInspectionItemDetail" resultMap="Transformation">
        select *
        from t_po_inspection_item_detail
        where is_deleted = 0
        order by create_time
    </select>

    <!--根据id获取指定的巡检明细项-->
    <select id="getInspectionItemDetailById" resultMap="Transformation">
        select * from t_po_inspection_item_detail where is_deleted = 0 and id = #{id}
    </select>

    <!--分页获取巡检项目明细-->
    <select id="getInspectionItemDetailPage" resultMap="Transformation">
        select * from t_po_inspection_item_detail
        where is_deleted = 0
        <if test="inspectionItemId != null">
            and inspection_item_id like CONCAT('%',#{inspectionItemId},'%')
        </if>
        order by create_time DESC
        limit #{page},#{pageSize}
    </select>

    <!--新增巡检项目明细-->
    <insert id="addInspectionItemDetail">
        insert into t_po_inspection_item_detail
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="inspectionItemId != null">
                inspection_item_id,
            </if>
            <if test="inspectionItemDetailCode != null">
                inspection_item_detail_code,
            </if>
            <if test="inspectionItemDetailName != null">
                inspection_item_detail_name,
            </if>
            <if test="violateRulesLevelId != null">
                violate_rules_level_id,
            </if>
            <if test="inspectionAnomalyMeasures != null">
                inspection_anomaly_measures,
            </if>
            <if test="standardNotes != null">
                standard_notes,
            </if>
            <if test="isSpecial != null">
                is_special,
            </if>
            <if test="isUploadPic != null">
                is_upload_pic,
            </if>
            <if test="notes != null">
                notes,
            </if>
        </trim>
        )
        value
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="inspectionItemId != null">
                #{inspectionItemId},
            </if>
            <if test="inspectionItemDetailCode != null">
                #{inspectionItemDetailCode},
            </if>
            <if test="inspectionItemDetailName != null">
                #{inspectionItemDetailName},
            </if>
            <if test="violateRulesLevelId != null">
                #{violateRulesLevelId},
            </if>
            <if test="inspectionAnomalyMeasures != null">
                #{inspectionAnomalyMeasures, typeHandler=com.hvisions.brewage.utils.JsonTypeHandler},
            </if>
            <if test="standardNotes != null">
                #{standardNotes},
            </if>
            <if test="isSpecial != null">
                #{isSpecial},
            </if>
            <if test="isUploadPic != null">
                #{isUploadPic},
            </if>
            <if test="notes != null">
                #{notes},
            </if>
        </trim>
        )
    </insert>

    <!--查询数据表最大Id值-->
    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id)
        from t_po_inspection_item_detail
    </select>

    <!--修改巡检项目明细-->
    <update id="updateInspectionItemDetail">
        update t_po_inspection_item_detail
        <trim prefix="set" suffixOverrides=",">
            <if test="modifyTime !=null">
                modify_time = #{modifyTime},
            </if>
            <if test="inspectionItemId != null">
                inspection_item_id = #{inspectionItemId},
            </if>
            <if test="inspectionItemDetailCode != null">
                inspection_item_detail_code = #{inspectionItemDetailCode},
            </if>
            <if test="inspectionItemDetailName != null">
                inspection_item_detail_name = #{inspectionItemDetailName},
            </if>
<!--            <if test="violateRulesLevelId != null">-->
<!--                violate_rules_level_id = #{violateRulesLevelId},-->
<!--            </if>-->
            violate_rules_level_id = #{violateRulesLevelId},
            <if test="inspectionAnomalyMeasures != null">
                inspection_anomaly_measures = #{inspectionAnomalyMeasures, typeHandler=com.hvisions.brewage.utils.JsonTypeHandler},
            </if>
            <if test="standardNotes != null">
                standard_notes = #{standardNotes},
            </if>
            <if test="isSpecial != null">
                is_special = #{isSpecial},
            </if>
            <if test="isUploadPic != null">
                is_upload_pic = #{isUploadPic},
            </if>
            <if test="notes != null">
                notes = #{notes},
            </if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateInspectionItemDetailById">
        update t_po_inspection_item_detail
        <trim prefix="set" suffixOverrides=",">
            <if test="modifyTime !=null">
                modify_time = #{modifyTime},
            </if>
            <if test="inspectionItemId != null">
                inspection_item_id = #{inspectionItemId},
            </if>
            <if test="inspectionItemDetailCode != null">
                inspection_item_detail_code = #{inspectionItemDetailCode},
            </if>
            <if test="inspectionItemDetailName != null">
                inspection_item_detail_name = #{inspectionItemDetailName},
            </if>
            <if test="violateRulesLevelId != null">
                violate_rules_level_id = #{violateRulesLevelId},
            </if>
            <if test="inspectionAnomalyMeasures != null">
                inspection_anomaly_measures = #{inspectionAnomalyMeasures, typeHandler=com.hvisions.brewage.utils.JsonTypeHandler},
            </if>
            <if test="standardNotes != null">
                standard_notes = #{standardNotes},
            </if>
            <if test="isSpecial != null">
                is_special = #{isSpecial},
            </if>
            <if test="isUploadPic != null">
                is_upload_pic = #{isUploadPic},
            </if>
            <if test="notes != null">
                notes = #{notes},
            </if>
        </trim>
        where id = #{id}
    </update>

    <!--删除巡检项目明细-->
    <update id="deleteInspectionItemDetail">
        update t_po_inspection_item_detail
        set modify_time = #{currentTime},
            is_deleted = 1
        where id = #{id}
    </update>

    <!--获取巡检项目明细条数-->
    <select id="getCount" resultType="java.lang.Integer">
        select COUNT(*) from t_po_inspection_item_detail
        where is_deleted = 0
        <if test="inspectionItemId != null">
            and inspection_item_id like CONCAT('%',#{inspectionItemId},'%')
        </if>
    </select>

</mapper>