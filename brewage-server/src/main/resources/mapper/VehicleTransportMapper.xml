<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.brewage.dao.VehicleTransportMapper">


    <select id="getVehicleTransportsByPage" resultType="com.hvisions.brewage.dto.VehicleTransportDTO">
        SELECT
        a.id,
        a.create_time,
        a.creator_id,
        a.license_plate_number,
        a.driver_name,
        a.state,
        b.user_name AS creator_name
        FROM vehicle_transport a
        LEFT JOIN sys_user b ON a.creator_id = b.id
        WHERE a.deleted = 0
        <if test="query.licensePlateNumber != null and query.licensePlateNumber != '' ">
            AND a.license_plate_number LIKE '%${query.licensePlateNumber}%'
        </if>
        <if test="query.driverName != null and query.driverName != '' ">
            AND a.driver_name LIKE '%${query.driverName}%'
        </if>
        <if test="query.state != null">
            AND a.state = ${query.state}
        </if>
    </select>
    <select id="getVehicleTransportsById" resultType="com.hvisions.brewage.entity.VehicleTransport">
        SELECT *
        FROM vehicle_transport
        WHERE deleted = 0
        AND id = ${id}
    </select>
</mapper>
