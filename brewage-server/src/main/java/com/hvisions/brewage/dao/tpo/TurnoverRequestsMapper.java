package com.hvisions.brewage.dao.tpo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.TurnoverRequestsQueryDTO;
import com.hvisions.brewage.dto.tpo.TurnoverCalculationDTO;
import com.hvisions.brewage.vo.tpo.TurnoverRequestsVO;
import com.hvisions.brewage.vo.tpo.TurnoverCalculationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TurnoverRequestsMapper {

    /**
     * 查询翻窖需求列表
     * 需求：
     * 1.订单状态为发酵状态 && 满足发酵时间（窖池订单的糟源类型+翻窖次数+封窖完成时间>当前的时间)
     * && 当糟源类型为DZ时，判断当前窖池除了DZ外还有没有订单状态为开窖的窖池订单
     * @param page
     * @param queryDTO
     * @return
     */
    List<TurnoverRequestsVO> selectPageList(Page<TurnoverRequestsVO> page,@Param("query")  TurnoverRequestsQueryDTO queryDTO);

    /**
     * 翻窖需求计算：只查询回酒（HJ2）和曲粉（QF3）
     * @param calculationDTO
     * @return
     */
    TurnoverCalculationVO calculateTurnoverRequirements(@Param("query") TurnoverCalculationDTO calculationDTO);
}
