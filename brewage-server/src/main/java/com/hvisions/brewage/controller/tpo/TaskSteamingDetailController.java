package com.hvisions.brewage.controller.tpo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.TaskSteamingDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskSteamingDetailQueryDTO;
import com.hvisions.brewage.service.tpo.TaskSteamingDetailService;
import com.hvisions.brewage.vo.tpo.TaskSteamingDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSteamingDetailController
 * @description: 出甑任务详情Controller
 * @date 2025/7/7 14:12
 */
@RestController
@RequestMapping("/tpo/task/steamingDetail")
@Api(value = "出甑任务详情相关接口", tags = "出甑任务详情相关接口")
public class TaskSteamingDetailController {

    @Resource
    private TaskSteamingDetailService taskSteamingDetailService;

    @PostMapping("/findPageList")
    @ApiOperation(value = "查询出甑任务详情列表")
    public Page<TaskSteamingDetailVO> findPageList(@RequestBody @Valid TaskSteamingDetailQueryDTO queryDTO) {
        return taskSteamingDetailService.findPageList(queryDTO);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增")
    private String addTaskSpreading(@RequestBody @Valid TaskSteamingDetailAddDTO addDTO) {
        return taskSteamingDetailService.add(addDTO);
    }

    @PostMapping("/addList")
    @ApiOperation(value = "批量新增")
    private String addList(@RequestBody @Valid List<TaskSteamingDetailAddDTO> addDTOList) {
        return taskSteamingDetailService.addList(addDTOList);
    }

    @DeleteMapping("/deleteIds")
    @ApiOperation(value = "删除")
    private String deleteIds(@RequestBody List<Integer> ids) {
        return taskSteamingDetailService.deleteIds(ids);
    }

    @PostMapping("/syncIotToMes")
    @ApiOperation(value = "IOT同步到MES")
    private String syncIotToMes(@RequestBody List<TaskSteamingDetailAddDTO> addDTOList) {
        return taskSteamingDetailService.addList(addDTOList);
    }

}
