package com.hvisions.brewage.controller.tpo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskSistillationAddDTO;
import com.hvisions.brewage.service.tpo.TaskSistillationService;
import com.hvisions.brewage.vo.tpo.TaskSistillationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSistillationController
 * @description: 蒸馏任务Controller
 * @date 2025/7/7 14:12
 */
@RestController
@RequestMapping("/tpo/task/sistillation")
@Api(value = "蒸馏任务相关接口", tags = "蒸馏任务相关接口")
public class TaskSistillationController {

    @Resource
    private TaskSistillationService taskSistillationService;

    @PostMapping("/findPageList")
    @ApiOperation(value = "查询蒸馏任务列表")
    public Page<TaskSistillationVO> findPageList(@RequestBody @Valid TaskAllQueryDTO queryDTO) {
        return taskSistillationService.findPageList(queryDTO);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增")
    private String addTaskSpreading(@RequestBody @Valid TaskSistillationAddDTO addDTO) {
        return taskSistillationService.add(addDTO);
    }

    @PostMapping("/addList")
    @ApiOperation(value = "批量新增")
    private String addList(@RequestBody @Valid List<TaskSistillationAddDTO> addDTOList) {
        return taskSistillationService.addList(addDTOList);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    private String updateTaskSpreading(@RequestBody @Valid TaskSistillationAddDTO updateDTO) {
        return taskSistillationService.update(updateDTO);
    }

    @DeleteMapping("/deleteIds")
    @ApiOperation(value = "删除")
    private String deleteIds(@RequestBody List<Integer> ids) {
        return taskSistillationService.deleteIds(ids);
    }

}
