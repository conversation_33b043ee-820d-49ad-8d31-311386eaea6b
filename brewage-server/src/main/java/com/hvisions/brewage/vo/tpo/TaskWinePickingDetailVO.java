package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.brewage.entity.tpo.IotTaskDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskWinePickingDetailVO
 * @description: 摘酒任务详情查询返回VO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="摘酒任务详情查询返回VO")
@Data
public class TaskWinePickingDetailVO extends IotTaskDetail {

    @TableField(value = "task_wine_picking_id")
    @ApiModelProperty(value="摘酒任务id")
    private Integer taskWinePickingId;

    @TableField(value = "weight_flowing_wine_one")
    @ApiModelProperty(value="一段流酒重量")
    private String weightFlowingWineOne;

    @TableField(value = "weight_flowing_wine_two")
    @ApiModelProperty(value="二段流酒重量")
    private String weightFlowingWineTwo;

    @TableField(value = "weight_flowing_wine_three")
    @ApiModelProperty(value="三段流酒重量")
    private String weightFlowingWineThree;

    @TableField(value = "weight_flowing_wine_four")
    @ApiModelProperty(value="四段流酒重量")
    private String weightFlowingWineFour;

    @TableField(value = "alcohol_content_one")
    @ApiModelProperty(value="一段酒精度")
    private String alcoholContentOne;

    @TableField(value = "alcohol_content_two")
    @ApiModelProperty(value="二段酒精度")
    private String alcoholContentTwo;

    @TableField(value = "alcohol_content_three")
    @ApiModelProperty(value="三段酒精度")
    private String alcoholContentThree;

    @TableField(value = "alcohol_content_four")
    @ApiModelProperty(value="四段酒精度")
    private String alcoholContentFour;

    @TableField(value = "proportion_flowing_alcohol_one")
    @ApiModelProperty(value="一段流酒占比")
    private String proportionFlowingAlcoholOne;

    @TableField(value = "proportion_flowing_alcohol_two")
    @ApiModelProperty(value="二段流酒占比")
    private String proportionFlowingAlcoholTwo;

    @TableField(value = "proportion_flowing_alcohol_three")
    @ApiModelProperty(value="三段流酒占比")
    private String proportionFlowingAlcoholThree;

    @TableField(value = "proportion_flowing_alcohol_four")
    @ApiModelProperty(value="四段流酒占比")
    private String proportionFlowingAlcoholFour;

    @TableField(value = "flowing_wine_duration")
    @ApiModelProperty(value="流酒时长")
    private String flowingWineDuration;

    @TableField(value = "duration_one")
    @ApiModelProperty(value="一段时长")
    private String durationOne;

    @TableField(value = "duration_two")
    @ApiModelProperty(value="二段时长")
    private String durationTwo;

    @TableField(value = "duration_three")
    @ApiModelProperty(value="三段时长")
    private String durationThree;

    @TableField(value = "duration_four")
    @ApiModelProperty(value="四段时长")
    private String durationFour;

    @TableField(value = "flowing_wine_temperature")
    @ApiModelProperty(value="流酒温度")
    private String flowingWineTemperature;

    @TableField(value = "flowing_wine_speed")
    @ApiModelProperty(value="流酒速度")
    private String flowingWineSpeed;

    @TableField(value = "cover_plate_flowing_wine_duration")
    @ApiModelProperty(value="盖盘到流酒时长")
    private String coverPlateFlowingWineDuration;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

}
