package com.hvisions.brewage.mkwine.vo.TraceabilityManagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "批次投料-大曲")
public class WineBatchBackQuDouVO {

    @ApiModelProperty(value = "曲斗号")
    private String quDouCode;

    @ApiModelProperty(value = "大曲批次")
    private String batch;

    @ApiModelProperty(value = "加曲重量确认")
    private BigDecimal quQuantity;

    @ApiModelProperty(value = "加曲机重量变化(kg)")
    private BigDecimal quChangedWeight;

    @ApiModelProperty(value = "甑号（一条产线6口甑）")
    private String potNum;

    @ApiModelProperty(value = "甑口任务号(中控传递过来的，每条产线一个流水号)")
    private Integer potSerialNumber;
}
