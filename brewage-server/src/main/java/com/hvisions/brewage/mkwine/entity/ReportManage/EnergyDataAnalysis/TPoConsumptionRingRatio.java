package com.hvisions.brewage.mkwine.entity.ReportManage.EnergyDataAnalysis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ProjectName brewage
 * @Description: 
 * @Date 2022/9/28 10:40
 */
@Data
public class TPoConsumptionRingRatio {
    @ApiModelProperty(value = "单位id")
    private Integer centerId;

    @ApiModelProperty(value = "单位")
    private String centerName;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "月份")
    private String month;//记录这个月

    @ApiModelProperty(value = "水单耗-上月")
    private Float waterConsumptionLast;

    @ApiModelProperty(value = "水单耗-今月")
    private Float waterConsumptionThis;

    @ApiModelProperty(value = "水单耗-环比")
    private String waterConsumptionRatio;

    @ApiModelProperty(value = "电单耗-上月")
    private Float electricityConsumptionLast;

    @ApiModelProperty(value = "电单耗-今月")
    private Float electricityConsumptionThis;

    @ApiModelProperty(value = "电单耗-环比")
    private String electricityConsumptionRatio;

    @ApiModelProperty(value = "汽单耗-上月")
    private Float steamProductionConsumptionLast;

    @ApiModelProperty(value = "汽单耗-今月")
    private Float steamProductionConsumptionThis;

    @ApiModelProperty(value = "汽单耗-环比")
    private String steamProductionConsumptionRatio;

    /**
     * 计算环比
     */
    public void calculation() {
        //水单耗
        this.setWaterConsumptionRatio(countYearOnYear(this.waterConsumptionThis, this.waterConsumptionLast));
        //电单耗
        this.setElectricityConsumptionRatio(countYearOnYear(this.electricityConsumptionThis, this.electricityConsumptionLast));
        //汽单耗
        this.setSteamProductionConsumptionRatio(countYearOnYear(this.steamProductionConsumptionThis, this.steamProductionConsumptionLast));
    }

    /**
     * 计算环比公式
     */
    private String countYearOnYear(float thisYear, float lastYear){
        if (thisYear == 0 && lastYear == 0){
            return "0%";
        }else if(thisYear == 0){
            return "-100%";
        }else if (lastYear == 0){
            return "100%";
        }else {
            return BigDecimal.valueOf(100 * (thisYear - lastYear) / lastYear)
                    .setScale(2, BigDecimal.ROUND_HALF_UP).floatValue() + "%";
        }
    }
}
