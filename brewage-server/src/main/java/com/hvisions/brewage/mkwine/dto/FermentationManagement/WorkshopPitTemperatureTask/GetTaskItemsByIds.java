package com.hvisions.brewage.mkwine.dto.FermentationManagement.WorkshopPitTemperatureTask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: brewage
 * @description: 根据 id 列表获取升温任务明细列表
 * @author: DengWeiTao
 **/
@Data
@ApiModel(value = "根据 id 列表获取升温任务明细列表DTO")
public class GetTaskItemsByIds {
    @ApiModelProperty(value = "id列表", example = "[1,2,3,4]")
    private Integer[] ids;
}
