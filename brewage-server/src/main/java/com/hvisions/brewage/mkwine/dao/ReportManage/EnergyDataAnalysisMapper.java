package com.hvisions.brewage.mkwine.dao.ReportManage;

import com.hvisions.brewage.mkwine.dto.ReportManage.EnergyDataAnalysis.EnergyDataAnalysisDTO;
import com.hvisions.brewage.mkwine.dto.ReportManage.EnergyDataAnalysis.EnergyDataAnalysisPageDTO;
import com.hvisions.brewage.mkwine.entity.ReportManage.EnergyDataAnalysis.TPoConsumption;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author: LiuYu
 * @Date: 2022/7/21 17:14
 * @Describe:
 **/

@Mapper
public interface EnergyDataAnalysisMapper {

    List<TPoConsumption> getEnergyDataAnalysisPage(EnergyDataAnalysisDTO dto);
}
