package com.hvisions.brewage.mkwine.vo.productiondisposition.ProductionPlanningBoard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @title: 今日甑口生产情况VO
 * @projectName brewage
 * @date 2022/12/5 17:42:02
 */
@Data
@ApiModel(value = "今日甑口生产情况VO")
public class TodayPotDataVO {

    @ApiModelProperty("糟源类别")
    private String vinasse;

    @ApiModelProperty(value = "计划甑口数")
    private Integer planPotCount;

    @ApiModelProperty(value = "实际甑口数")
    private Integer actualPotCount;
}
