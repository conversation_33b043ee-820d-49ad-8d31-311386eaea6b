package com.hvisions.brewage.mkwine.entity.EnergyManagement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * @author: DengWeiTao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.brewage.mkwine.entity.EnergyManagement.TPoSewageStation
 * @Date: 2022年08月10日 15:47
 * @Description:
 */
@Data
@Entity
@ApiModel(value = "污水站数据明细表")
@Table(name = "t_po_sewage_station")
public class TPoSewageStation {

    @Id
    @TableId(type = IdType.AUTO)
    @TableField("id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键", example = "1")
    private Integer id;

    @TableField("year")
    @ApiModelProperty(value = "年份", example = "2022")
    private Integer year;

    @TableField("month")
    @ApiModelProperty(value = "月份", example = "1")
    private Integer month;

    @TableField("sludge_production")
    @ApiModelProperty(value = "污泥产生量", example = "100.00")
    private BigDecimal sludgeProduction;

    @TableField("sewage_inflow")
    @ApiModelProperty(value = "污水进水量", example = "100.00")
    private BigDecimal sewageInflow;

    @TableField("sewage_outflow")
    @ApiModelProperty(value = "污水出水量", example = "100.00")
    private BigDecimal sewageOutflow;

    /**
     * 污水产生情况（区域）（转运到黄舣）
     */
    @TableField("hy_an_sewage_volume")
    @ApiModelProperty(value = "安宁污水量", example = "100.00")
    private BigDecimal hyAnSewageVolume;

    @TableField("hy_xs_sewage_volume")
    @ApiModelProperty(value = "小市污水量", example = "100.00")
    private BigDecimal hyXsSewageVolume;

    @TableField("hy_gj_sewage_volume")
    @ApiModelProperty(value = "国窖污水量", example = "100.00")
    private BigDecimal hyGjSewageVolume;

    @TableField("hy_zjx_sewage_volume")
    @ApiModelProperty(value = "皂角巷污水量", example = "100.00")
    private BigDecimal hyZjxSewageVolume;

    /**
     * 污水产生情况（区域）（转运到罗汉）
     */
    @TableField("lh_an_sewage_volume")
    @ApiModelProperty(value = "安宁污水量", example = "100.00")
    private BigDecimal lhAnSewageVolume;

    @TableField("lh_xs_sewage_volume")
    @ApiModelProperty(value = "小市污水量", example = "100.00")
    private BigDecimal lhXsSewageVolume;

    @TableField("lh_gj_sewage_volume")
    @ApiModelProperty(value = "国窖污水量", example = "100.00")
    private BigDecimal lhGjSewageVolume;

    @TableField("lh_zhi_qu_center")
    @ApiModelProperty(value = "制曲中心", example = "100.00")
    private BigDecimal lhZhiQuCenter;

    @TableField("lh_zjx_sewage_volume")
    @ApiModelProperty(value = "皂角巷污水量", example = "100.00")
    private BigDecimal lhZjxSewageVolume;

    /**
     * 进水污水理化指标
     */
    @TableField("in_cod")
    @ApiModelProperty(value = "COD", example = "100.00")
    private BigDecimal inCod;

    @TableField("in_ammonia_nitrogen")
    @ApiModelProperty(value = "氨氮", example = "100.00")
    private BigDecimal inAmmoniaNitrogen;

    @TableField("in_total_nitrogen")
    @ApiModelProperty(value = "总氮", example = "100.00")
    private BigDecimal inTotalNitrogen;

    @TableField("in_total_phosphorus")
    @ApiModelProperty(value = "总磷", example = "100.00")
    private BigDecimal inTotalPhosphorus;

    @TableField("in_ph")
    @ApiModelProperty(value = "ph", example = "100.00")
    private BigDecimal inPh;

    /**
     * 出水污水理化指标
     */
    @TableField("out_cod")
    @ApiModelProperty(value = "COD", example = "100.00")
    private BigDecimal outCod;

    @Column(name = "out_ammonia_nitrogen", columnDefinition="decimal(19,3)")
    @ApiModelProperty(value = "氨氮", example = "100.00")
    private BigDecimal outAmmoniaNitrogen;

    @TableField("out_total_nitrogen")
    @ApiModelProperty(value = "总氮", example = "100.00")
    private BigDecimal outTotalNitrogen;

    @Column(name = "out_total_phosphorus", columnDefinition="decimal(19,4)")
    @ApiModelProperty(value = "总磷", example = "100.00")
    private BigDecimal outTotalPhosphorus;

    @TableField("out_ph")
    @ApiModelProperty(value = "ph", example = "100.00")
    private BigDecimal outPh;

    @TableField("run_days")
    @ApiModelProperty(value = "运行天数", example = "30")
    private Integer runDays;

    @TableField("start_time")
    @ApiModelProperty(value = "统计开始时间", example = "08-02")
    private String startTime;

    @TableField("end_time")
    @ApiModelProperty(value = "统计结束时间", example = "09-02")
    private String endTime;

    @TableField("is_zzq")
    @ApiModelProperty(value = "用来判读是否是轧帐期", example = "轧帐期")
    private Boolean isZzq;
}
