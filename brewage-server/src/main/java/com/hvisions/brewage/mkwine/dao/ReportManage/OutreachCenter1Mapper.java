package com.hvisions.brewage.mkwine.dao.ReportManage;

import com.hvisions.brewage.mkwine.dto.EnergyManagement.MonthReport1DTO;
import com.hvisions.brewage.mkwine.vo.ReportManage.WineCenter.WineGradeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: Deng<PERSON><PERSON><PERSON>ao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.brewage.mkwine.dao.ReportManage.OutreachCenter1Mapper
 * @Date: 2022年08月30日 11:04
 * @Description:
 */
@Mapper
@Repository
public interface OutreachCenter1Mapper {

    // 交酒任务
    List<WineGradeVO> WineGradeData(@Param("dto") MonthReport1DTO monthReport1DTO);
}
