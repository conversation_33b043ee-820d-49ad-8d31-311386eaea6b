package com.hvisions.brewage.mkwine.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 中控糟源第二中转换情况
 */
public class CentralSecondVinasse {

    final static List<String> CJList = Arrays.asList("1", "2", "20", "21");

    final static List<String> CBList = Collections.singletonList("3");

    final static List<String> HZList = Collections.singletonList("4");

    final static List<String> MZList = Collections.singletonList("5");

    final static List<String> ZLList = Collections.singletonList("6");

    final static List<String> DZList = Collections.singletonList("7");


    public static String getVinasseName(String vinasseKey) {
        String name = null;
        if(CJList.contains(vinasseKey)){
            name = "CJ";
        } else if (CBList.contains(vinasseKey)){
            name = "CB";
        } else if (HZList.contains(vinasseKey)){
            name = "HZ";
        } else if (MZList.contains(vinasseKey)){
            name = "MZ";
        } else if (ZLList.contains(vinasseKey)){
            name = "ZL";
        } else if (DZList.contains(vinasseKey)){
            name = "DZ";
        }
        return name;
    }

    public static String getVinasseLevel(String vinasseKey) {
        if("1".equals(vinasseKey)){
            return "上层";
        } else if ("2".equals(vinasseKey)){
            return "中层";
        } else {
            return "下层";
        }
    }
}
