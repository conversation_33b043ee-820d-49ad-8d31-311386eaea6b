package com.hvisions.brewage.mkwine.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 通过角色组名查询人员
 *
 * @BelongsProject: brewage
 * @BelongsPackage: com.hvisions.brewage.mkwine.dto
 * <AUTHOR>
 * @Date 2022-09-07  14:44
 * @Version: 1.0
 */
@Data
public class QueryUserByRoleDTO {

    @Builder.Default
    @ApiModelProperty(value = "中心id")
    private Integer centerId = -1;

    @Builder.Default
    @ApiModelProperty(value = "车间id")
    private Integer locationId = -1;

    @ApiModelProperty(value = "组名")
    private List<String> list;

}
