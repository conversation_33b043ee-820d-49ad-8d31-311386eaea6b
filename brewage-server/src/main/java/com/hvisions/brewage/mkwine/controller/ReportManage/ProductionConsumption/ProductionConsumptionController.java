package com.hvisions.brewage.mkwine.controller.ReportManage.ProductionConsumption;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.google.common.collect.Lists;
import com.hvisions.brewage.mkwine.ReportHandler.HeadAndContentCellStyle;
import com.hvisions.brewage.mkwine.ReportHandler.StatisticalCellWriteHandler;
import com.hvisions.brewage.mkwine.dao.ReportManage.ProductionConsumptionMapper;
import com.hvisions.brewage.mkwine.dao.configuration.ConfigurationMapper;
import com.hvisions.brewage.mkwine.dto.ReportManage.ProductionConsumption.ProductionConsumptionDTO;
import com.hvisions.brewage.mkwine.entity.configuration.TPoConfiguration;
import com.hvisions.brewage.mkwine.vo.ReportManage.ProductionConsumption.ProductionConsumptionMapperVO;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.common.dto.ExcelExportDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 
 * @title: ProductionConsumptionController
 * @projectName brewage
 * @date 2022/12/28 16:15:29
 */
@RestController
@Api(description = "生产消耗报表")
@RequestMapping(value = "/ProductionConsumption")
public class ProductionConsumptionController {

    @Resource
    private ConfigurationMapper configurationMapper;
    @Resource
    private ProductionConsumptionMapper productionConsumptionMapper;

    @PostMapping("/getProductionConsumptionExcel")
    @ApiOperation("获取生产消耗报表")
    public ExcelExportDto getProductionConsumptionExcel(@RequestBody ProductionConsumptionDTO dto) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
                .registerWriteHandler(new StatisticalCellWriteHandler())
                .build();

        List<List<String>> head = head();
        // 动态添加表头，适用一些表头动态变化的场景
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetName("生产消耗报表");
        sheet1.setSheetNo(0);
        // 创建一个表格，用于 Sheet 中使用
        WriteTable table = new WriteTable();
        table.setTableNo(0);
        table.setHead(head);

        writer.write(contentData(dto), sheet1, table);
        writer.finish();
        outputStream.close();

        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("生产消耗报表.xlsx");
        exportDto.setBody(outputStream.toByteArray());

        return exportDto;
    }

    @PostMapping("/getProductionConsumption")
    @ApiOperation(value = "获取生产消耗报表前端数据")
    public List<ProductionConsumptionMapperVO> getProductionConsumption(@RequestBody ProductionConsumptionDTO dto) {
        List<String> months = new ArrayList<>();
        List<List<String>> dateRange = new ArrayList<>();

        // 获取12个月的月份并获取每个月的扎帐日期
        for (int i = 1; i <= 12; i++) {
            String year = dto.getYear();
            String month = String.valueOf(i).length() < 2 ? "0" + i : String.valueOf(i);
            String date = year + "-" + month;
            ArrayList<String> strings = new ArrayList<>();
            strings.add(getSettleAccountsMonth(date, true));
            strings.add(getSettleAccountsMonth(date, false));
            if (strings.get(0) == null || strings.get(1) == null)
                continue;
            dateRange.add(strings);
            months.add(date);
        }


        // 循环获取数据
        List<ProductionConsumptionMapperVO> datas = new ArrayList<>();
        for (int i = 0; i < dateRange.size(); i++) {
            List<String> range = dateRange.get(i);
            String month = months.get(i);
            List<ProductionConsumptionMapperVO> productionConsumptionData = productionConsumptionMapper.getProductionConsumptionData(dto.getCenterId(), range.get(0), range.get(1));
            for (ProductionConsumptionMapperVO productionConsumptionDatum : productionConsumptionData) {
                // 补充ZT FJ数据
                productionConsumptionDatum.setMonth(month);
                productionConsumptionDatum.setFj(productionConsumptionMapper.getFjData(productionConsumptionDatum.getLocation(), range.get(0), range.get(1)));
                productionConsumptionDatum.setZt(productionConsumptionMapper.getZtData(productionConsumptionDatum.getLocation(), range.get(0), range.get(1)));
            }

            datas.addAll(productionConsumptionData);
        }

        // 计算粮耗 曲耗 糠耗
        for (ProductionConsumptionMapperVO data : datas) {
            // 粮耗
            double d = data.getAllProduction() - (data.getZt() * 1.0645) - (data.getFj() * 0.7521);
            if (d == 0d) {
                data.setLiangHao(0d);
            } else {
                data.setLiangHao(data.getLiangQuality() / d);
            }

            // 曲耗
            if (data.getAllProduction() == 0d) {
                data.setQuHao(0d);
            } else {
                data.setQuHao(data.getQuQuantity() / data.getAllProduction());
            }

            // 糠耗
            if (data.getAllProduction() == 0d) {
                data.setKangHao(0d);
            } else {
                data.setKangHao(data.getDaokeQuality() / data.getAllProduction());
            }
        }

        // 加入一个大总计
        LinkedHashMap<String, List<ProductionConsumptionMapperVO>> collect = datas.stream().collect(Collectors.groupingBy(ProductionConsumptionMapperVO::getLocation, LinkedHashMap::new, Collectors.toList()));
        for (List<ProductionConsumptionMapperVO> value : collect.values()) {
            ProductionConsumptionMapperVO dd = new ProductionConsumptionMapperVO();
            dd.setMonth("总计");
            dd.setLocation(value.get(0).getLocation());
            dd.setCenter(value.get(0).getCenter());
            dd.setLiangQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getLiangQuality).sum());
            dd.setQuQuantity(value.stream().mapToDouble(ProductionConsumptionMapperVO::getQuQuantity).sum());
            dd.setDaokeQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getDaokeQuality).sum());
            dd.setFj(value.stream().mapToDouble(ProductionConsumptionMapperVO::getFj).sum());
            dd.setZt(value.stream().mapToDouble(ProductionConsumptionMapperVO::getZt).sum());
            dd.setAllProduction(value.stream().mapToDouble(ProductionConsumptionMapperVO::getAllProduction).sum());

            // 粮耗
            double d = dd.getAllProduction() - (dd.getZt() * 1.0645) - (dd.getFj() * 0.7521);
            if (d == 0d) {
                dd.setLiangHao(0d);
            } else {
                dd.setLiangHao(dd.getLiangQuality() / d);
            }

            // 曲耗
            if (dd.getAllProduction() == 0d) {
                dd.setQuHao(0d);
            } else {
                dd.setQuHao(dd.getQuQuantity() / dd.getAllProduction());
            }

            // 糠耗
            if (dd.getAllProduction() == 0d) {
                dd.setKangHao(0d);
            } else {
                dd.setKangHao(dd.getDaokeQuality() / dd.getAllProduction());
            }
            datas.add(dd);
        }

        // 弄出中心总计
        LinkedHashMap<String, List<ProductionConsumptionMapperVO>> collect1 = datas.stream().collect(Collectors.groupingBy(t -> t.getMonth() + "_" + t.getCenter(), LinkedHashMap::new, Collectors.toList()));
        for (List<ProductionConsumptionMapperVO> value : collect1.values()) {
            ProductionConsumptionMapperVO productionConsumptionMapperVO = new ProductionConsumptionMapperVO();
            productionConsumptionMapperVO.setMonth(value.get(0).getMonth());
            productionConsumptionMapperVO.setLocation(value.get(0).getCenter());
            productionConsumptionMapperVO.setLiangQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getLiangQuality).sum());
            productionConsumptionMapperVO.setQuQuantity(value.stream().mapToDouble(ProductionConsumptionMapperVO::getQuQuantity).sum());
            productionConsumptionMapperVO.setDaokeQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getDaokeQuality).sum());
            productionConsumptionMapperVO.setFj(value.stream().mapToDouble(ProductionConsumptionMapperVO::getFj).sum());
            productionConsumptionMapperVO.setZt(value.stream().mapToDouble(ProductionConsumptionMapperVO::getZt).sum());
            productionConsumptionMapperVO.setAllProduction(value.stream().mapToDouble(ProductionConsumptionMapperVO::getAllProduction).sum());

            // 粮耗
            double d = productionConsumptionMapperVO.getAllProduction() - (productionConsumptionMapperVO.getZt() * 1.0645) - (productionConsumptionMapperVO.getFj() * 0.7521);
            if (d == 0d) {
                productionConsumptionMapperVO.setLiangHao(0d);
            } else {
                productionConsumptionMapperVO.setLiangHao(productionConsumptionMapperVO.getLiangQuality() / d);
            }

            // 曲耗
            if (productionConsumptionMapperVO.getAllProduction() == 0d) {
                productionConsumptionMapperVO.setQuHao(0d);
            } else {
                productionConsumptionMapperVO.setQuHao(productionConsumptionMapperVO.getQuQuantity() / productionConsumptionMapperVO.getAllProduction());
            }

            // 糠耗
            if (productionConsumptionMapperVO.getAllProduction() == 0d) {
                productionConsumptionMapperVO.setKangHao(0d);
            } else {
                productionConsumptionMapperVO.setKangHao(productionConsumptionMapperVO.getDaokeQuality() / productionConsumptionMapperVO.getAllProduction());
            }

            value.add(productionConsumptionMapperVO);
        }

        List<ProductionConsumptionMapperVO> list = new ArrayList<>();
        for (List<ProductionConsumptionMapperVO> value : collect1.values()) {
            for (ProductionConsumptionMapperVO productionConsumptionMapperVO : value) {
                list.add(productionConsumptionMapperVO);
            }
        }

        return list;
    }

    private List<List<Object>> contentData(ProductionConsumptionDTO dto) {
        List<List<Object>> contentList = Lists.newArrayList();
        List<String> months = new ArrayList<>();
        List<List<String>> dateRange = new ArrayList<>();

        // 获取今年12个月的月份并获取每个月的扎帐日期
        for (int i = 1; i <= 12; i++) {
            String year = dto.getYear();
            String month = String.valueOf(i).length() < 2 ? "0" + i : String.valueOf(i);
            String date = year + "-" + month;
            ArrayList<String> strings = new ArrayList<>();
            strings.add(getSettleAccountsMonth(date, true));
            strings.add(getSettleAccountsMonth(date, false));
            if (strings.get(0) == null || strings.get(1) == null)
                continue;
            dateRange.add(strings);
            months.add(date);
        }

        // 循环获取数据
        List<ProductionConsumptionMapperVO> datas = new ArrayList<>();
        for (int i = 0; i < dateRange.size(); i++) {
            List<String> range = dateRange.get(i);
            String month = months.get(i);
            List<ProductionConsumptionMapperVO> productionConsumptionData = productionConsumptionMapper.getProductionConsumptionData(dto.getCenterId(), range.get(0), range.get(1));
            for (ProductionConsumptionMapperVO productionConsumptionDatum : productionConsumptionData) {
                // 补充ZT FJ数据
                productionConsumptionDatum.setMonth(month);
                productionConsumptionDatum.setFj(productionConsumptionMapper.getFjData(productionConsumptionDatum.getLocation(), range.get(0), range.get(1)));
                productionConsumptionDatum.setZt(productionConsumptionMapper.getZtData(productionConsumptionDatum.getLocation(), range.get(0), range.get(1)));
            }

            datas.addAll(productionConsumptionData);
        }
        // 计算粮耗 曲耗 糠耗
        for (ProductionConsumptionMapperVO data : datas) {
            // 粮耗
            double d = data.getAllProduction() - (data.getZt() * 1.0645) - (data.getFj() * 0.7521);
            if (d == 0d) {
                data.setLiangHao(0d);
            } else {
                data.setLiangHao(data.getLiangQuality() / d);
            }

            // 曲耗
            if (data.getAllProduction() == 0d) {
                data.setQuHao(0d);
            } else {
                data.setQuHao(data.getQuQuantity() / data.getAllProduction());
            }

            // 糠耗
            if (data.getAllProduction() == 0d) {
                data.setKangHao(0d);
            } else {
                data.setKangHao(data.getDaokeQuality() / data.getAllProduction());
            }
        }

        // 加入一个大总计
        LinkedHashMap<String, List<ProductionConsumptionMapperVO>> collect = datas.stream().collect(Collectors.groupingBy(ProductionConsumptionMapperVO::getLocation, LinkedHashMap::new, Collectors.toList()));
        for (List<ProductionConsumptionMapperVO> value : collect.values()) {
            ProductionConsumptionMapperVO dd = new ProductionConsumptionMapperVO();
            dd.setMonth("总计");
            dd.setLocation(value.get(0).getLocation());
            dd.setCenter(value.get(0).getCenter());
            dd.setLiangQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getLiangQuality).sum());
            dd.setQuQuantity(value.stream().mapToDouble(ProductionConsumptionMapperVO::getQuQuantity).sum());
            dd.setDaokeQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getDaokeQuality).sum());
            dd.setFj(value.stream().mapToDouble(ProductionConsumptionMapperVO::getFj).sum());
            dd.setZt(value.stream().mapToDouble(ProductionConsumptionMapperVO::getZt).sum());
            dd.setAllProduction(value.stream().mapToDouble(ProductionConsumptionMapperVO::getAllProduction).sum());

            // 粮耗
            double d = dd.getAllProduction() - (dd.getZt() * 1.0645) - (dd.getFj() * 0.7521);
            if (d == 0d) {
                dd.setLiangHao(0d);
            } else {
                dd.setLiangHao(dd.getLiangQuality() / d);
            }

            // 曲耗
            if (dd.getAllProduction() == 0d) {
                dd.setQuHao(0d);
            } else {
                dd.setQuHao(dd.getQuQuantity() / dd.getAllProduction());
            }

            // 糠耗
            if (dd.getAllProduction() == 0d) {
                dd.setKangHao(0d);
            } else {
                dd.setKangHao(dd.getDaokeQuality() / dd.getAllProduction());
            }
            datas.add(dd);
        }

        // 弄出中心总计
        LinkedHashMap<String, List<ProductionConsumptionMapperVO>> collect1 = datas.stream().collect(Collectors.groupingBy(t -> t.getMonth() + "_" + t.getCenter(), LinkedHashMap::new, Collectors.toList()));
        for (List<ProductionConsumptionMapperVO> value : collect1.values()) {
            ProductionConsumptionMapperVO productionConsumptionMapperVO = new ProductionConsumptionMapperVO();
            productionConsumptionMapperVO.setMonth(value.get(0).getMonth());
            productionConsumptionMapperVO.setLocation(value.get(0).getCenter());
            productionConsumptionMapperVO.setLiangQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getLiangQuality).sum());
            productionConsumptionMapperVO.setQuQuantity(value.stream().mapToDouble(ProductionConsumptionMapperVO::getQuQuantity).sum());
            productionConsumptionMapperVO.setDaokeQuality(value.stream().mapToDouble(ProductionConsumptionMapperVO::getDaokeQuality).sum());
            productionConsumptionMapperVO.setFj(value.stream().mapToDouble(ProductionConsumptionMapperVO::getFj).sum());
            productionConsumptionMapperVO.setZt(value.stream().mapToDouble(ProductionConsumptionMapperVO::getZt).sum());
            productionConsumptionMapperVO.setAllProduction(value.stream().mapToDouble(ProductionConsumptionMapperVO::getAllProduction).sum());

            // 粮耗
            double d = productionConsumptionMapperVO.getAllProduction() - (productionConsumptionMapperVO.getZt() * 1.0645) - (productionConsumptionMapperVO.getFj() * 0.7521);
            if (d == 0d) {
                productionConsumptionMapperVO.setLiangHao(0d);
            } else {
                productionConsumptionMapperVO.setLiangHao(productionConsumptionMapperVO.getLiangQuality() / d);
            }

            // 曲耗
            if (productionConsumptionMapperVO.getAllProduction() == 0d) {
                productionConsumptionMapperVO.setQuHao(0d);
            } else {
                productionConsumptionMapperVO.setQuHao(productionConsumptionMapperVO.getQuQuantity() / productionConsumptionMapperVO.getAllProduction());
            }

            // 糠耗
            if (productionConsumptionMapperVO.getAllProduction() == 0d) {
                productionConsumptionMapperVO.setKangHao(0d);
            } else {
                productionConsumptionMapperVO.setKangHao(productionConsumptionMapperVO.getDaokeQuality() / productionConsumptionMapperVO.getAllProduction());
            }

            value.add(productionConsumptionMapperVO);
        }

        // 拼接excel数据
        DecimalFormat df = new DecimalFormat("0.00");//格式化小数
        for (List<ProductionConsumptionMapperVO> value : collect1.values()) {
            for (ProductionConsumptionMapperVO data : value) {
                List<Object> objects = Lists.newArrayList();
                objects.add(data.getMonth());
                objects.add(data.getLocation());
                objects.add(data.getLiangQuality());
                objects.add(data.getQuQuantity());
                objects.add(data.getDaokeQuality());
                objects.add(data.getFj());
                objects.add(data.getZt());
                objects.add(df.format(data.getLiangHao()));
                objects.add(df.format(data.getQuHao()));
                objects.add(df.format(data.getKangHao()));
                objects.add(data.getAllProduction());

                contentList.add(objects);
            }
        }

        return contentList;
    }

    private List<List<String>> head() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("生产月", "生产月"));
        headTitles.add(Lists.newArrayList("车间", "车间"));
        headTitles.add(Lists.newArrayList("投入消耗", "高粱"));
        headTitles.add(Lists.newArrayList("投入消耗", "曲药"));
        headTitles.add(Lists.newArrayList("投入消耗", "糠壳"));
        headTitles.add(Lists.newArrayList("投入消耗", "FJ" + System.lineSeparator() + "(75.21%)"));
        headTitles.add(Lists.newArrayList("投入消耗", "ZT" + System.lineSeparator() + "(106.45%)"));
        headTitles.add(Lists.newArrayList("投入消耗", "粮耗"));
        headTitles.add(Lists.newArrayList("投入消耗", "曲耗"));
        headTitles.add(Lists.newArrayList("投入消耗", "糠耗"));
        headTitles.add(Lists.newArrayList("总产", "总产"));

        for (List<String> headTitle : headTitles) {
            headTitle.add(0, "生产消耗报表");
        }

        return headTitles;
    }

    /**
     * 根据时间得到扎账月份
     *
     * @param time YYYY-MM-dd
     * @return 月份
     */
    private String getSettleAccountsMonth(String time, TPoConfiguration tPoConfiguration) {
        if (StringUtils.isEmpty(time) || !time.contains("-")) {
            return null;
        }
        String[] array = time.split("-");//分割字符串得到数组
        int month = Integer.parseInt(array[1]); //月份
        int day = Integer.parseInt(array[2]); //天数
        String re = String.valueOf(month);//返回的月份

        //得到配置
//        TPoConfiguration tPoConfiguration = configurationMapper.seletByCode(array[0]);
        if (month == 12) {
            //12月份扎账区间为11月23号到配置的日期
            if (tPoConfiguration != null && tPoConfiguration.getDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(tPoConfiguration.getDate());
                int thisMonth = cal.get(Calendar.MONTH) + 1;
                int thisDay = cal.get(Calendar.DAY_OF_MONTH);
                if (thisMonth < month) return re;
                if (day > thisDay) {
                    re = String.valueOf(1);
                }
            }
        } else {
            //其它月份指定上个月24号到今月23号
            //24号到23号[24,23] 24---- 23 24 -- 23
            if (day > 23) {
                re = String.valueOf(month + 1);
            }
        }
        return re;
    }

    /**
     * 根据日期得到扎帐开始或结束时间
     *
     * @param time    YYYY-MM
     * @param isStart 是否为开始时间
     */
    private String getSettleAccountsMonth(String time, boolean isStart) {
        if (StringUtils.isEmpty(time) || !time.contains("-")) {
            return null;
        }
        String[] array = time.split("-");//分割字符串得到数组
        int year = Integer.parseInt(array[0]); //年份
        int month = Integer.parseInt(array[1]); //月份
        String re;//返回的日期

        //得到配置
        TPoConfiguration configuration = configurationMapper.seletByCode(array[0]);
        if (configuration == null || configuration.getDate() == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(configuration.getDate());
        int thisMonth = cal.get(Calendar.MONTH) + 1;
        int thisDay = cal.get(Calendar.DAY_OF_MONTH);
        if (month == 1) {
            //1月份扎账区间为配置的日期到1月23号 24---- 23 24 -- 23
            if (thisMonth == 1) {
                re = isStart ? year + "-" + thisMonth + "-" + (thisDay + 1) : year + "-" + month + "-23";
            } else {
                re = isStart ? (year - 1) + "-" + thisMonth + "-" + (thisDay + 1) : year + "-" + month + "-23";
            }

        } else if (month == 12) {
            //12月份扎账区间为11月23号到配置的日期
            if (thisMonth < month) return null;
            re = isStart ? year + "-" + (month - 1) + "-24" : year + "-" + thisMonth + "-" + thisDay;
        } else {
            //其它月份指定上个月24号到今月23号
            //24号到23号
            re = isStart ? year + "-" + (month - 1) + "-24" : year + "-" + month + "-23";
        }
        String[] split = re.split("-");
        re = MessageFormat.format("{0}-{1}-{2}", split[0], (split[1].length() < 2 ? "0" + split[1] : split[1]), split[2]);
        return re;
    }
}
