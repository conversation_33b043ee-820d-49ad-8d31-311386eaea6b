package com.hvisions.brewage.mkwine.entity.productiondisposition;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: 订单类型实体类
 * @projectName test
 * @description:
 * @date 2022/3/416:48:39
 */
@Data
@Entity
@ApiModel(description = "订单类型")
@Table(name = "t_po_order_type")
public class TPoOrderType {
    @Id
    @ApiModelProperty(value = "id",example = "1")
    @TableField("id")
    private Integer id;

    @TableField("order_category_name")
    @ApiModelProperty(value = "订单类型名称")
    private String orderCategoryName;

    @TableField("order_category_code")
    @ApiModelProperty(value = "订单类型编号")
    private String orderCategoryCode;

    @TableField("is_excess_receipt")
    @ApiModelProperty(value = "是否过量收货")
    private Boolean isExcessReceipt;

    @TableField("notes")
    @ApiModelProperty(value = "备注")
    private String notes;

    @TableField("is_deleted")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("modify_time")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
