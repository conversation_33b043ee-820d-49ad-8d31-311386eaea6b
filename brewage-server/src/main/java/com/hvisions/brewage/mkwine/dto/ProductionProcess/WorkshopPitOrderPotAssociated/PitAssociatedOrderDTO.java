package com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 
 * @title: 窖号订单关联数据DTO
 * @projectName brewage
 * @date 2022/5/26 10:05:48
 */
@Data
@ApiModel(value = "获取窖号订单关联数据DTO")
public class PitAssociatedOrderDTO {

    @ApiModelProperty(value = "甑口数据列表的所有id")
    private List<Integer> ids;

    @ApiModelProperty(value = "生产起窖号数据列表")
    private List<OutPitAssociatedOrderDTO> outPitData;

    @ApiModelProperty(value = "生产入窖号数据列表")
    private List<InPitAssociatedOrderDTO> inPitData;

    @ApiModelProperty(value = "是否进行甑口平衡")
    private Boolean isPotCountUpdate;
}
