package com.hvisions.brewage.mkwine.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderOutAppraiseMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshoppitSapApproveMapper;
import com.hvisions.brewage.mkwine.dao.SapManage.TPoWorkshopPitOrderSapEnableMapper;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshoppitSapApprove.WorkshoppitSapApproveConditionDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshoppitSapApprove.WorkshoppitSapApproveDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshoppitSapApprove.WorkshoppitSapApproveInsertDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshoppitSapApprove.WorkshoppitUpdateSyncTag;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderLog;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.enums.ResultTipEnum;
import com.hvisions.brewage.mkwine.enums.SapStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderLogService;
import com.hvisions.brewage.mkwine.service.WorkshoppitSapApproveService;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshoppitSapApprove.WorkshoppitSapApproveProcessVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshoppitSapApprove.WorkshoppitSapApproveVO;
import com.hvisions.brewage.dto.mkwine.vo.PageVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO;
import com.hvisions.brewage.mkwine.vo.productiondisposition.WorkshopPitOrder.WorkshopPitOrderVO;
import com.hvisions.brewage.sap.dto.SapBaseResponseDto;
import com.hvisions.brewage.sap.service.SapService;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.note.SendSms;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @program: brewage
 * @description:
 * @author: DengWeiTao
 **/
@Service
public class WorkshoppitSapApproveServiceImpl implements WorkshoppitSapApproveService {

    @Autowired
    private WorkshoppitSapApproveMapper workshoppitSapApproveMapper;

    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;

    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    private SapService sapService;

    @Resource
    WorkshopPitOrderLogService workshopPitOrderLogService;

    //    @Value("${mkwine.isUseSap}")
//    Boolean isUseSap;
    @Resource
    TPoWorkshopPitOrderSapEnableMapper tPoWorkshopPitOrderSapEnableMapper;
    @Autowired
    private WorkshopPitOrderOutAppraiseMapper workshopPitOrderOutAppraiseMapper;

    /**
     * 新增订单审批
     *
     * @param dto
     * @return
     */
    @Override
    public ResultVO insertApprove(WorkshoppitSapApproveInsertDTO dto) {

        WorkshoppitSapApproveVO vo = workshoppitSapApproveMapper.getStatusByCode(dto.getSapOrderCode());
        if (vo != null && vo.getIsAgree() != null && vo.getIsAgree() != 3)
            return ResultVO.error("当前sap窖池订单号（单窖）已经在审批中，新增失败");

        // 查询当前sap订单有没有数据
        LambdaQueryWrapper<TPoWorkshopPitOrderSap> query = new LambdaQueryWrapper<>();
        query.eq(TPoWorkshopPitOrderSap::getSapOrderCode, dto.getSapOrderCode())
                .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0);
        TPoWorkshopPitOrderSap tPoWorkshopPitOrderSap = workshopPitOrderSapMapper.selectOne(query);
        if (tPoWorkshopPitOrderSap == null) {
            throw new BaseKnownException(10000, "当前mes不存在该sap订单");
        }


        if (workshoppitSapApproveMapper.insertApprove(dto)) {
            // 设置订单为申请开启
            LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update = new LambdaUpdateWrapper<>();
            update.eq(TPoWorkshopPitOrderSap::getSapOrderCode, dto.getSapOrderCode())
                    .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                    .set(TPoWorkshopPitOrderSap::getApproveStatus, 1);
            workshopPitOrderSapMapper.update(null, update);
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }
        return ResultVO.error("新增订单审批失败");
    }

    /**
     * 分页查询订单审批管理
     *
     * @param dto
     * @return
     */
    @Override
    public PageVO<WorkshoppitSapApproveVO> getSapApproveList(WorkshoppitSapApproveConditionDTO dto) {
        dto.setPage((dto.getPage() - 1) * dto.getPageSize());
        PageVO<WorkshoppitSapApproveVO> pageVO = new PageVO<>();
        /*List<WorkshoppitSapApproveVO> sapApproveList = workshoppitSapApproveMapper.getSapApproveList(dto);
        Integer count = workshoppitSapApproveMapper.getCount(dto);*/
//        List<OrderAndPit> orderAndPits = null;
//        if ((dto.getPitCode() != null && !dto.getPitCode().equals("")) || (dto.getSapOrderCode() != null && !dto.getSapOrderCode().equals("")) ||
//                dto.getCenterId() != null || dto.getLocationId() != null) {
//            orderAndPits = workshoppitSapApproveMapper.getOrderAndPit(dto.getCenterId(), dto.getLocationId(), dto.getSapOrderCode(), dto.getPitCode());
//            if (orderAndPits.size() <= 0) {
//                pageVO.setCount(0);
//                pageVO.setData(new ArrayList<>());
//                return pageVO;
//            }
//        }
//
//        // 把sap订单号对应的窖池信息放进map中
//        HashMap<String, OrderAndPit> hashMap = new HashMap<>();
//        if (orderAndPits != null) {
//            for (OrderAndPit orderAndPit : orderAndPits) {
//                hashMap.put(orderAndPit.getSapOrderCode(), orderAndPit);
//            }
//        }

        // 根据sap订单号和其他的查询条件查询审批
        List<WorkshoppitSapApproveVO> sapApproveList = workshoppitSapApproveMapper.getApproveList(dto, null);
        Integer count = workshoppitSapApproveMapper.getApproveCount(dto, null);
//        if (hashMap.size() > 0) {
//            for (WorkshoppitSapApproveVO workshoppitSapApproveVO : sapApproveList) {
//                if (hashMap.containsKey(workshoppitSapApproveVO.getSapOrderCode())) {
//                    OrderAndPit orderAndPit = hashMap.get(workshoppitSapApproveVO.getSapOrderCode());
//                    workshoppitSapApproveVO.setPitId(orderAndPit.getPitId());
//                    workshoppitSapApproveVO.setPitCode(orderAndPit.getPitCode());
//                }
//            }
//        } else {
//            List<OrderAndPit> pitInfo = workshoppitSapApproveMapper.getPitInfo(sapApproveList);
//            for (OrderAndPit orderAndPit : pitInfo) {
//                hashMap.put(orderAndPit.getSapOrderCode(), orderAndPit);
//            }
//
//            for (WorkshoppitSapApproveVO workshoppitSapApproveVO : sapApproveList) {
//                if (hashMap.containsKey(workshoppitSapApproveVO.getSapOrderCode())) {
//                    OrderAndPit orderAndPit = hashMap.get(workshoppitSapApproveVO.getSapOrderCode());
//                    workshoppitSapApproveVO.setPitId(orderAndPit.getPitId());
//                    workshoppitSapApproveVO.setPitCode(orderAndPit.getPitCode());
//                }
//            }
//        }

        // 待处理数据处理
        if (dto.getApproveStatus() != null && dto.getApproveStatus() == 0) {
            for (Iterator<WorkshoppitSapApproveVO> it = sapApproveList.iterator(); it.hasNext(); ) {
                WorkshoppitSapApproveVO next = it.next();
                if (Objects.equals(dto.getUserId(), next.getApprove1UserId()) && isRemove(next.getApprove1IsAgree()) == 0) {
                    continue;
                }
                if (Objects.equals(dto.getUserId(), next.getApprove2UserId()) && isRemove(next.getApprove2IsAgree()) == 0) {
                    continue;
                }
                if (Objects.equals(dto.getUserId(), next.getApprove3UserId()) && isRemove(next.getApprove3IsAgree()) == 0) {
                    continue;
                }
                it.remove();
                count--;
            }
        }

        // 已完成数据处理
        if (dto.getApproveStatus() != null && dto.getApproveStatus() == 1) {
            for (Iterator<WorkshoppitSapApproveVO> it = sapApproveList.iterator(); it.hasNext(); ) {
                WorkshoppitSapApproveVO next = it.next();
                if (Objects.equals(dto.getUserId(), next.getApprove1UserId()) && isRemove(next.getApprove1IsAgree()) == 0) {
                    it.remove();
                    count--;
                    continue;
                }
                if (Objects.equals(dto.getUserId(), next.getApprove2UserId()) && isRemove(next.getApprove2IsAgree()) == 0) {
                    it.remove();
                    count--;
                    continue;
                }
                if (Objects.equals(dto.getUserId(), next.getApprove3UserId()) && isRemove(next.getApprove3IsAgree()) == 0) {
                    it.remove();
                    count--;
                    continue;
                }

                // 需要该用户进行审批的部分已经是完成审批了，让该用户处理的审批显示成已完成状态并且显示完成时间
                if (next.getIsAgree() == null || next.getIsAgree() == 0 || next.getIsAgree() == 4) {
                    next.setIsAgree(1);
                    if (Objects.equals(dto.getUserId(), next.getApprove3UserId())) {
                        next.setApproveEndtime(next.getApprove3Time());
                    } else if (Objects.equals(dto.getUserId(), next.getApprove2UserId())) {
                        next.setApproveEndtime(next.getApprove2Time());
                    } else if (Objects.equals(dto.getUserId(), next.getApprove1UserId())) {
                        next.setApproveEndtime(next.getApprove1Time());
                    }
                }
            }
        }
        pageVO.setData(sapApproveList);
        pageVO.setCount(count);
        return pageVO;
    }

    // 判断当前用户需要进行审批的是不是已经完成
    private int isRemove(Integer isAgree) {
        if (isAgree == null || isAgree == 0 || isAgree == 4) {
            // 没有完成审批
            return 0;
        } else {
            // 完成了审批
            return 1;
        }
    }

    /**
     * 进行审批操作
     *
     * @param dto
     * @return
     */
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    @Override
    public synchronized void approveOrder(WorkshoppitSapApproveDTO dto) {
        Boolean isUseSap = tPoWorkshopPitOrderSapEnableMapper.selectList(new LambdaQueryWrapper<>()).get(0).getIsEnabled();
        if (dto.getId() == null) throw new RuntimeException("唯一标识不能为空");
        // 根据 id 查询 1、2、3级的审批人名字，用来判断有多少层审批，多少个名字就多少级
        WorkshoppitSapApproveVO approveNames = workshoppitSapApproveMapper.getApprovelNameById(dto.getId());

        // 该条记录已经被同意或者被驳回，不能修改其他信息
        if (approveNames.getIsAgree() != null && (approveNames.getIsAgree() == 1 || approveNames.getIsAgree() == 2 || approveNames.getIsAgree() == 3)) {
            //return ResultVO.error("该条记录已经被同意或者被驳回或者是被取消，不能进行修改");
            throw new RuntimeException("该条记录已经被同意或者被驳回或者是被取消，不能进行修改");
        }

        int level = 0;
        if (approveNames.getApprove1UserName() != null) level++;
        if (approveNames.getApprove2UserName() != null) level++;
        if (approveNames.getApprove3UserName() != null) level++;
        if (dto.getLevel() == level || dto.getApproveUserStatus() == 2) {
            // 说明审批到最后一级，审批完成了，修改审批完成时间和状态
            workshoppitSapApproveMapper.updateApproveTime(dto.getApproveTime(), dto.getApproveUserStatus(), dto.getId());

            // 更新开窖鉴定状态
            if (approveNames.getOutAppraiseId() != null) {
                if (dto.getApproveUserStatus() == 2) {
                    // 驳回删除开窖鉴定
                    workshoppitSapApproveMapper.deleteOutAppraise(approveNames.getOutAppraiseId());
                }
                if (dto.getApproveUserStatus() == 1) {
                    // 判断该订单是否全部是已同意状态
                    Integer[] status = workshoppitSapApproveMapper.getOrderApproveStatus(approveNames.getOutAppraiseId());
                    boolean update = true;
                    for (Integer integer : status) {
                        if (integer == null || integer != 1) {
                            update = false;
                            break;
                        }
                    }
                    // 修改状态
                    if (update) {
                        if (approveNames.getOutStatus() != null && approveNames.getOutStatus() == 2) {
                            // 获取开窖鉴定时间
                            Date outTime = workshopPitOrderOutAppraiseMapper.getOutTimeById(approveNames.getOutAppraiseId());
                            updateCellarLiftingCompletedTime(approveNames.getOrderId(), outTime);
                            updateExuding(approveNames.getOrderId(), outTime);
                        }
                        workshoppitSapApproveMapper.updateOutAppraise(approveNames.getOutAppraiseId());
                    }
                }
            }

            if (!approveNames.getApproveTitle().equals("非计划窖池订单开窖申请") && dto.getApproveUserStatus() == 2) {
                // 如果驳回，更新窖池订单的状态
                LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update = new LambdaUpdateWrapper<>();
                update.eq(TPoWorkshopPitOrderSap::getSapOrderCode, approveNames.getSapOrderCode())
                        .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                        .set(TPoWorkshopPitOrderSap::getApproveStatus, 3);
                workshopPitOrderSapMapper.update(null, update);
            }

            if (!approveNames.getApproveTitle().equals("非计划窖池订单开窖申请") && dto.getApproveUserStatus() == 1) {
                // 如果同意，更新窖池订单的状态
                LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update = new LambdaUpdateWrapper<>();
                update.eq(TPoWorkshopPitOrderSap::getSapOrderCode, approveNames.getSapOrderCode())
                        .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                        .set(TPoWorkshopPitOrderSap::getApproveStatus, 2);
                workshopPitOrderSapMapper.update(null, update);
                // 同时使用sap状态更改接口
                LambdaQueryWrapper<TPoWorkshopPitOrderSap> query = new LambdaQueryWrapper<>();
                query.eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                        .eq(TPoWorkshopPitOrderSap::getSapOrderCode, approveNames.getSapOrderCode());
                TPoWorkshopPitOrderSap tPoWorkshopPitOrderSap = workshopPitOrderSapMapper.selectOne(query);

                List<Integer> ids = new ArrayList<>();
                ids.add(tPoWorkshopPitOrderSap.getId());
                if (!isUseSap) {
                    // 技术撤销成功，更改订单的关闭状态为已开启
                    // 通过sap传来的订单号获取连窖订单数据
                    LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update1 = new LambdaUpdateWrapper<>();
                    update1.eq(TPoWorkshopPitOrderSap::getSapOrderCode, approveNames.getSapOrderCode())
                            .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                            .set(TPoWorkshopPitOrderSap::getOrderCloseTag, 3);
                    workshopPitOrderSapMapper.update(null, update1);
                } else {
                    SapBaseResponseDto sapBaseResponseDto = sapService.orderChangeStatus(ids, SapStatus.COMPLETE);
                    WorkshopPitOrderVO workshopPitOrderBySapOrderCode = workshopPitOrderSapMapper.getWorkshopPitOrderBySapOrderCode(tPoWorkshopPitOrderSap.getSapOrderCode());
                    LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update1 = new LambdaUpdateWrapper<>();
                    if (sapBaseResponseDto.getEsMessage().getMsgty().equals("S")) {
                        // 技术撤销成功，更改订单的关闭状态为已开启
                        // 通过sap传来的订单号获取连窖订单数据
                        update1.eq(TPoWorkshopPitOrderSap::getSapOrderCode, approveNames.getSapOrderCode())
                                .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                                .set(TPoWorkshopPitOrderSap::getOrderCloseTag, 3);
                        workshopPitOrderSapMapper.update(null, update1);
                        sapLog(true, "订单开启", approveNames.getApprove3UserId(),
                                workshopPitOrderBySapOrderCode.getOrderCode(), workshopPitOrderBySapOrderCode.getFullPitId(),
                                approveNames.getSapOrderCode(), "");
                    } else {
                        // 技术撤销成功，更改订单的关闭状态为未开启
                        // 通过sap传来的订单号获取连窖订单数据
                        update1.eq(TPoWorkshopPitOrderSap::getSapOrderCode, approveNames.getSapOrderCode())
                                .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                                .set(TPoWorkshopPitOrderSap::getOrderCloseTag, 2);
                        workshopPitOrderSapMapper.update(null, update1);
                        sapLog(false, "订单开启", approveNames.getApprove3UserId(),
                                workshopPitOrderBySapOrderCode.getOrderCode(), workshopPitOrderBySapOrderCode.getFullPitId(),
                                approveNames.getSapOrderCode(), sapBaseResponseDto.getEsMessage().getMsgtx());
                    }
                }
            }

            // 审批完成一个单窖的，发送短信
            //sendMS(approveNames);
        }
        // 现在写死是有三级，因为数据库是写死的，所以代码这里也写死
        if (dto.getLevel() == 1) {
            // 修改的应该就只有三个字段，写死
            // 一级审批人进行审批
            String[] approveColumn = {"approve1_is_agree", "approve1_notes", "approve1_time"};
            if (workshoppitSapApproveMapper.approveOrder(approveColumn, dto)) {
                if (approveNames.getApprove2UserName() != null && dto.getApproveUserStatus() == 1) {
                    // 第一个审批人已同意审批，第二个改为处理中
                    workshoppitSapApproveMapper.updateApproveStatus("approve2_is_agree", dto.getId());
                }
                //return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                return;
            }
        } else if (dto.getLevel() == 2) {
            // 二级审批人进行审批
            String[] approveColumn = {"approve2_is_agree", "approve2_notes", "approve2_time"};
            if (workshoppitSapApproveMapper.approveOrder(approveColumn, dto)) {
                if (approveNames.getApprove3UserName() != null && dto.getApproveUserStatus() == 1) {
                    // 第二个审批人已同意审批，第三个改为处理中
                    workshoppitSapApproveMapper.updateApproveStatus("approve3_is_agree", dto.getId());
                }
                //return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                return;
            }
        } else if (dto.getLevel() == 3) {
            // 三级审批人进行审批
            String[] approveColumn = {"approve3_is_agree", "approve3_notes", "approve3_time"};
            if (workshoppitSapApproveMapper.approveOrder(approveColumn, dto)) {
                //return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                return;
            }
        }
        throw new RuntimeException("审批失败，需要查明具体原因请联系维护人员进行排查");
        //return ResultVO.error("审批失败");
    }

    /**
     * 审批完成发送短信
     */
    public void sendMS(WorkshoppitSapApproveVO approveNames) {
        // 发起人部门
        String department = workshoppitSapApproveMapper.getDepartment(approveNames.getUserId());
        department = department == null ? "" : department;

        // 单窖订单号
        String orderCode = workshoppitSapApproveMapper.getSimpleOrderCode(approveNames.getSapOrderCode());
        orderCode = orderCode == null ? "" : orderCode;

        // 找到角色为 酿酒中心生产主管、生产管理部SAP、财务SAP 的用户的手机号
        String[] numbers = workshoppitSapApproveMapper.getNumbers();
        // 窖池订单审批通过完成后进行短信通知，通知人员：酿酒中心生产主管、生产管理部SAP、财务SAP；
        // 一级二级审批员一定会有，三级不一定
        SendSms.sendNote(
                "1566397",
                new String[]{
                        DateUtil.currentDateFormat() + "，" + department + approveNames.getUserName(),
                        orderCode + "；SAP订单号为: " + approveNames.getSapOrderCode() + (approveNames.getNotes() == null ? "" : "；备注: " + approveNames.getNotes()) +
                                ("；审批人员依次为: " + approveNames.getApprove1UserName() + "-" + approveNames.getApprove2UserName() + (approveNames.getApprove3UserName() == null ? "" : "-" + approveNames.getApprove3UserName()))
                },
                numbers
        );
    }

    /**
     * 修改窖池订单的起窖完成时间
     */
    public void updateCellarLiftingCompletedTime(Integer orderId, Date outTime) {
        // 修改开窖鉴定状态为 已完成 状态，更新起窖完成时间
        if (orderId == null)
            throw new RuntimeException("开窖鉴定更新执行状态，窖池订单 id 不能为空");
        workshopPitOrderOutAppraiseMapper.updateOutPitFinishTime(outTime, orderId);
        workshopPitOrderOutAppraiseMapper.updateOutPitFinishTimeSap(outTime, orderId);
        // 更新开窖完成时间
        workshopPitOrderMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrder>()
                .eq(TPoWorkshopPitOrder::getId, orderId)
                .set(TPoWorkshopPitOrder::getOpenPitFinishTime, outTime));
        workshopPitOrderSapMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderSap>()
                .eq(TPoWorkshopPitOrderSap::getOrderCodeId, orderId)
                .set(TPoWorkshopPitOrderSap::getOpenPitFinishTime, outTime));

        // 查询窖池订单状态 窖池状态（枚举0入窖 1 发酵 2开窖 3 空窖 4 异常）pit_status
        if (workshopPitOrderOutAppraiseMapper.getPitStatus(orderId) == 1) {
            // 当该窖池订单的窖池状态为发酵时，自动修改为【开窖】
            workshopPitOrderOutAppraiseMapper.updatePitStatus(2, orderId);
            workshopPitOrderOutAppraiseMapper.updateSapPitStatus(2, orderId);
        }

        // 窖池订单执行状态 工单状态 枚举 0 入窖完成 1翻窖完成 2起窖完成 3开窖完成 4交酒完成 5待执行 6 异常 OrderStatus
        Integer orderStatus = workshopPitOrderOutAppraiseMapper.getOrderStatus(orderId);
        if (orderStatus == 0 || orderStatus == 1) {
            workshopPitOrderOutAppraiseMapper.updateOrderStatus(2, orderId);
            workshopPitOrderOutAppraiseMapper.updateSapOrderStatus(2, orderId);
        }
    }

    /**
     * "滴窖任务的完成状态与开窖鉴定做联动，当同个订单的开窖鉴定完成时(选择状态为完成，点击保存时)，自动检测该订单的滴窖是否完成：
     * 1、若状态为滴窖中，则更新该条滴窖任务，状态改为已完成，滴窖完成时间为开窖鉴定完成时间；
     * 2、若状态为已完成，则对于滴窖任务不需任何操作；
     * 3、若无该滴窖任务或者状态为创建，则无法点击保存按钮，且则给予提示：DD220704_11975-11976_1 该订单对应的窖池还未滴窖，请优先执行滴窖！"
     */
    public void updateExuding(Integer orderId, Date outAppraiseTime) {
        if (orderId == null)
            throw new RuntimeException("开窖鉴定更新执行状态，窖池订单 id 不能为空");
        // 查询窖池订单的滴窖任务
        List<WorkshopPitOrderExudingTaskVO> exudingTaskVO = workshopPitOrderOutAppraiseMapper.getExudingTask(orderId);
        if (exudingTaskVO.size() > 1) {
            throw new RuntimeException("该窖池订单存在多个滴窖任务，请联系管理员进行排查");
        }

        if (exudingTaskVO.size() == 0 || exudingTaskVO.get(0).getStatus() == 1) {
            String orderCode = workshopPitOrderOutAppraiseMapper.getOrderCode(orderId);
            throw new RuntimeException(orderCode + " 该订单对应的窖池还未滴窖，请优先执行滴窖！");
        }

        if (exudingTaskVO.get(0).getStatus() == 2) {
            workshopPitOrderOutAppraiseMapper.updateExudingStatus(outAppraiseTime, exudingTaskVO.get(0).getId());
        }
    }

    private void sapLog(Boolean isSuccess, String logContent, Integer userId, String orderCode, String pitCode, String sapOrderCode, String notes) {
        // 写入日志
        TPoWorkshopPitOrderLog tPoWorkshopPitOrderLog = new TPoWorkshopPitOrderLog();
        tPoWorkshopPitOrderLog.setIsSucceed(isSuccess);
        tPoWorkshopPitOrderLog.setLogContent(logContent);
        tPoWorkshopPitOrderLog.setLogTime(new Date());
        tPoWorkshopPitOrderLog.setUserId(userId);
        tPoWorkshopPitOrderLog.setOrderCode(orderCode);
        tPoWorkshopPitOrderLog.setPitCode(pitCode);
        tPoWorkshopPitOrderLog.setSapOrderCode(sapOrderCode);
        tPoWorkshopPitOrderLog.setNotes(notes);
        workshopPitOrderLogService.addSAPLog(tPoWorkshopPitOrderLog);
    }

    /**
     * 根据 id 查询订单审批管理
     *
     * @param id
     * @return
     */
    @Override
    public WorkshoppitSapApproveProcessVO getApproveById(String id, String sapOrderCode) {
        if (id.equals("null")) id = null;
        if (sapOrderCode.equals("null")) sapOrderCode = null;
        return workshoppitSapApproveMapper.getApprovelById(id, sapOrderCode);
    }

    /**
     * 根据 id 修改订单同步标志
     *
     * @param tag
     * @return
     */
    @Override
    public WorkshoppitSapApproveProcessVO updateSyncTag(WorkshoppitUpdateSyncTag tag) {
        return workshoppitSapApproveMapper.updateSyncTag(tag);
    }

    /**
     * 根据 sap窖池订单（单窖） 取消审批申请
     *
     * @param sapOrderCode
     * @return
     */
    @Override
    public ResultVO cancelApplication(String sapOrderCode) {
        // 根据 sap窖池订单号查找审核状态
        WorkshoppitSapApproveVO vo = workshoppitSapApproveMapper.getStatusByCode(sapOrderCode);
        if (vo.getIsAgree() != null && vo.getIsAgree() != 0)
            return ResultVO.error("该订单已经处理完成，不能再进行修改");

        if (workshoppitSapApproveMapper.cancelApplication(sapOrderCode)) {
            // 设置为已取消
            LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update = new LambdaUpdateWrapper<>();
            update.eq(TPoWorkshopPitOrderSap::getSapOrderCode, sapOrderCode)
                    .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0)
                    .set(TPoWorkshopPitOrderSap::getApproveStatus, 4);
            workshopPitOrderSapMapper.update(null, update);
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }

        return ResultVO.error("取消审批失败");
    }

    /**
     * 获取sap订单id列表
     *
     * @param sapOrderCode
     * @return
     */
    @Override
    public List<Integer> getSapIds(String sapOrderCode) {
        return workshoppitSapApproveMapper.getSapIds(sapOrderCode);
    }

    /**
     * 新增订单审批(连窖)
     *
     * @param dto
     * @return
     */
    @Override
    public ResultVO insertApproveEvenCellar(WorkshoppitSapApproveInsertDTO dto) {
        // 根据连窖 id 查找单窖
        String[] sapOrderCodes = workshoppitSapApproveMapper.getSapOrderCodes(dto.getId());
        // 根据连窖订单id找连窖订单编号
        dto.setOrderCode(workshoppitSapApproveMapper.getOrderCode(dto.getId()));
        if (sapOrderCodes.length > 0) {
            int i = sapOrderCodes.length;
            for (String sapOrderCode : sapOrderCodes) {
                i--;
                dto.setSapOrderCode(sapOrderCode);
                WorkshoppitSapApproveVO vo = workshoppitSapApproveMapper.getStatusByCode(dto.getSapOrderCode());
                if (vo != null && vo.getIsAgree() != null && vo.getIsAgree() != 3)
                    return ResultVO.error("当前sap窖池订单号（单窖）已经在审批中，新增失败");

                // 查询当前sap订单有没有数据，可能会存在两个空值
                LambdaQueryWrapper<TPoWorkshopPitOrderSap> query = new LambdaQueryWrapper<>();
                query.eq(TPoWorkshopPitOrderSap::getSapOrderCode, dto.getSapOrderCode())
                        .eq(TPoWorkshopPitOrderSap::getIsDeleted, 0);
                TPoWorkshopPitOrderSap tPoWorkshopPitOrderSap = workshopPitOrderSapMapper.selectOne(query);
                if (tPoWorkshopPitOrderSap == null && !dto.getApproveTitle().equals("非计划窖池订单开窖申请")) {
                    throw new BaseKnownException(10000, "当前mes不存在该窖池订单对应的sap订单");
                }


                if (workshoppitSapApproveMapper.insertApprove(dto)) {
                    if (i == 0) {
                        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                    }
                    continue;
                }
                return ResultVO.error("新增订单审批失败");
            }
        }
        return ResultVO.error("该订单没有SAP订单号，请完成SAP同步后再进行新增！");
    }
}
