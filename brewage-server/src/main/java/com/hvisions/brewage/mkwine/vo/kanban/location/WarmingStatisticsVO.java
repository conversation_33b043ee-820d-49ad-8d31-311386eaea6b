package com.hvisions.brewage.mkwine.vo.kanban.location;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: DengWeiTao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.brewage.mkwine.vo.kanban.location.WarmingStatisticsVO
 * @Date: 2022年08月26日 16:13
 * @Description:
 */
@ApiModel("看板-车间升温统计VO")
@Data
public class WarmingStatisticsVO {

    @ApiModelProperty(value = "窖池升温任务id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "连窖号", example = "10089-10092")
    private String pitCode;

    @ApiModelProperty(value = "层级", example = "0")
    private String layer;

    @ApiModelProperty(value = "窖池订单", example = "0")
    private String orderCode;

    @ApiModelProperty(value = "窖池的升温明细列表")
    private List<TempVO> tempVOS;
}
