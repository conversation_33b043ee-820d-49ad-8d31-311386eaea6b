package com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @title: 入窖号订单关联DTO
 * @projectName brewage
 * @date 2022/5/26 10:11:06
 */
@Data
@ApiModel(value = "入窖号订单关联DTO")
public class InPitAssociatedOrderDTO {

    @ApiModelProperty(value = "糟源类型")
    private String vinasse;

    @ApiModelProperty(value = "入窖号关联订单（单窖）")
    private String inOrderCode;

    @ApiModelProperty(value = "单窖编号", example = "1")
    private String inPitCode;
}
