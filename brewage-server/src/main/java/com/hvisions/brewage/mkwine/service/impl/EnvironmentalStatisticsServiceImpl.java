package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.google.common.collect.Lists;
import com.hvisions.brewage.mkwine.ReportHandler.HeadAndContentCellStyle;
import com.hvisions.brewage.mkwine.ReportHandler.StatisticalCellWriteHandler;
import com.hvisions.brewage.mkwine.dao.ReportManage.EnvironmentalStatisticsMapper;
import com.hvisions.brewage.mkwine.dto.ReportManage.EnvironmentalStatistics.EnvironmentalStatisticsDTO;
import com.hvisions.brewage.mkwine.service.EnvironmentalStatisticsService;
import com.hvisions.brewage.mkwine.vo.ReportManage.ConsumptionOfRawMaterials.ConsumptionOfRawMaterialsMonthVO;
import com.hvisions.brewage.mkwine.vo.ReportManage.EnvironmentalStatistics.EnvironmentalStatisticsDataVO;
import com.hvisions.brewage.mkwine.vo.ReportManage.EnvironmentalStatistics.EnvironmentalStatisticsIndicatorVO;
import com.hvisions.brewage.mkwine.vo.ReportManage.EnvironmentalStatistics.EnvironmentalStatisticsVO;
import com.hvisions.brewage.utils.NumberUtil;
import com.hvisions.common.dto.ExcelExportDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * <AUTHOR>
 * @ProjectName brewage
 * @Description: 环境统计表填报数据计算逻辑-年度报送serviceImpl
 * @Date 2022/9/9 9:43
 */
@Service
public class EnvironmentalStatisticsServiceImpl implements EnvironmentalStatisticsService {

    @Value("${material.type.sorghum}")
    String sorghum;

    @Value("${material.type.da-qu}")
    String daQu;

    @Value("${material.type.rice-husk}")
    String riceHusk;

    @Resource
    private EnvironmentalStatisticsMapper mapper;

    @Override
    public EnvironmentalStatisticsVO getEnvironmentalStatistics(EnvironmentalStatisticsDTO dto) {
        EnvironmentalStatisticsVO vo = new EnvironmentalStatisticsVO();
        if (dto == null || StringUtils.isEmpty(dto.getYear())){
            return vo;
        }
        //sheet1
        List<EnvironmentalStatisticsIndicatorVO> indicator = new ArrayList<>();
        EnvironmentalStatisticsIndicatorVO environmentalStatisticsIndicatorVO;
        //年正常生产时间
        environmentalStatisticsIndicatorVO = this.mapper.getProductionTime(dto);
        if (environmentalStatisticsIndicatorVO == null) {
            environmentalStatisticsIndicatorVO = new EnvironmentalStatisticsIndicatorVO("年正常生产时间", "小时", "2");
        }
        environmentalStatisticsIndicatorVO.setActual(NumberUtil.toDouble(environmentalStatisticsIndicatorVO.getActual(), 2));
        indicator.add(environmentalStatisticsIndicatorVO);
        //主要原辅材料用量 不填数据
        environmentalStatisticsIndicatorVO = new EnvironmentalStatisticsIndicatorVO();
        environmentalStatisticsIndicatorVO.setIndicatorName("主要原辅材料用量");
        indicator.add(environmentalStatisticsIndicatorVO);
        //高粱、糠壳、曲药等原辅料用量
        //这里的高粱、糠壳、曲药用量指投入
        environmentalStatisticsIndicatorVO = new EnvironmentalStatisticsIndicatorVO();
        environmentalStatisticsIndicatorVO.setIndicatorName("高粱、糠壳、曲药、小麦等原辅料用量");
        environmentalStatisticsIndicatorVO.setUnit("吨");
        environmentalStatisticsIndicatorVO.setCode("23");
        ConsumptionOfRawMaterialsMonthVO materialsUsage = this.mapper.getMaterialsUsage(dto);
        if (materialsUsage == null) {
            materialsUsage = new ConsumptionOfRawMaterialsMonthVO();
        }
        float add = materialsUsage.getBranConsumption() +
                materialsUsage.getFoodConsumption() +
                materialsUsage.getTortuousConsumption();
        environmentalStatisticsIndicatorVO.setActual(NumberUtil.toDouble(add, 2));
        indicator.add(environmentalStatisticsIndicatorVO);
        //主要产品生产情况 不填数据
        environmentalStatisticsIndicatorVO = new EnvironmentalStatisticsIndicatorVO();
        environmentalStatisticsIndicatorVO.setIndicatorName("主要产品生产情况");
        indicator.add(environmentalStatisticsIndicatorVO);
        //白酒基础酒（65%vol）
        environmentalStatisticsIndicatorVO = this.mapper.getBasicWine(dto);
        if (environmentalStatisticsIndicatorVO == null) {
            environmentalStatisticsIndicatorVO = new EnvironmentalStatisticsIndicatorVO("白酒基础酒（65%vol）", "吨", "26");
        }
        environmentalStatisticsIndicatorVO.setActual(NumberUtil.toDouble(environmentalStatisticsIndicatorVO.getActual(), 2));
        indicator.add(environmentalStatisticsIndicatorVO);

        //sheet2
        List<EnvironmentalStatisticsDataVO> data;
        EnvironmentalStatisticsDataVO dataVO;
        EnvironmentalStatisticsDataVO count;
        HashMap<Integer, EnvironmentalStatisticsDataVO> map = new HashMap<>();
        //产量(60度)
        data = this.mapper.getBasicWineBy60(dto);
        if (data != null) {
            for (EnvironmentalStatisticsDataVO item : data) {
                if (map.containsKey(item.getCentreId())) {
                    item.setYield(NumberUtil.toDouble(map.get(item.getCentreId()).getYield() + item.getYield(), 2));
                }
                map.put(item.getCentreId(), item);
            }
        }
        //投入
        data = this.mapper.getInputData(dto);
        if (data != null) {
            for (EnvironmentalStatisticsDataVO item : data) {
                if (map.containsKey(item.getCentreId())) {
                    item.setThrowFood(NumberUtil.toFloat(map.get(item.getCentreId()).getThrowFood() + item.getThrowFood(), 2));
                    item.setThrowKoji(NumberUtil.toFloat(map.get(item.getCentreId()).getThrowKoji() + item.getThrowKoji(), 2));
                    item.setChaffShell(NumberUtil.toFloat(map.get(item.getCentreId()).getChaffShell() + item.getChaffShell(), 2));
                }
                map.put(item.getCentreId(), item);
            }
        }
        //消耗
        data = this.mapper.getOutputData(dto);
        for (EnvironmentalStatisticsDataVO item : data){
            if (map.containsKey(item.getCentreId())){
                item.setConsumeFood(NumberUtil.toFloat(map.get(item.getCentreId()).getConsumeFood()+item.getConsumeFood(), 2));
                item.setConsumeKoji(NumberUtil.toFloat(map.get(item.getCentreId()).getConsumeKoji()+item.getConsumeKoji(), 2));
            }
            map.put(item.getCentreId(), item);
        }
        //白酒原(65度)
        data = this.mapper.getBasicWineBy65(dto);
        if (data != null) {
            for (EnvironmentalStatisticsDataVO item : data) {
                if (map.containsKey(item.getCentreId())) {
                    item.setDrinkingCapacity(NumberUtil.toDouble(map.get(item.getCentreId()).getDrinkingCapacity() + item.getDrinkingCapacity(), 2));
                }
                map.put(item.getCentreId(), item);
            }
        }
        //将map转换为list并合并总计
        data = new ArrayList<>();
        count = new EnvironmentalStatisticsDataVO();//记录总计
        count.setCentreName("总计");
        for (Integer key : map.keySet()){
            dataVO = map.get(key);
            data.add(dataVO);
            count.setYield(NumberUtil.toDouble(count.getYield() + dataVO.getYield(), 2));
            count.setThrowFood(NumberUtil.toFloat(count.getThrowFood() + dataVO.getThrowFood(), 2));
            count.setThrowKoji(NumberUtil.toFloat(count.getThrowKoji() + dataVO.getThrowKoji(), 2));
            count.setConsumeFood(NumberUtil.toFloat(count.getConsumeFood() + dataVO.getConsumeFood(), 2));
            count.setConsumeKoji(NumberUtil.toFloat(count.getConsumeKoji() + dataVO.getConsumeKoji(), 2));
            count.setChaffShell(NumberUtil.toFloat(count.getChaffShell() + dataVO.getChaffShell(), 2));
            count.setDrinkingCapacity(NumberUtil.toDouble(count.getDrinkingCapacity() + dataVO.getDrinkingCapacity(), 2));
        }
        data.add(count);
        vo.setIndicator(indicator);
        vo.setData(data);
        return vo;
    }

    @Override
    public ExcelExportDto exportEnvironmentalStatisticsExcel(EnvironmentalStatisticsDTO dto) {
        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("环境统计表填报数据计算逻辑-年度报送.xlsx");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
                .registerWriteHandler(new StatisticalCellWriteHandler())
                .build();
        //定义excel
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetName("Sheet1");
        sheet1.setSheetNo(0);
        WriteSheet sheet2 = new WriteSheet();
        sheet2.setSheetName("Sheet2");
        sheet2.setSheetNo(1);
        //创建Sheet1表格
        WriteTable table1 = new WriteTable();
        table1.setTableNo(0);
        table1.setHead(head1());
        //创建Sheet2表格
        WriteTable table2 = new WriteTable();
        table2.setTableNo(1);
        table2.setHead(head2());

        //写数据
        EnvironmentalStatisticsVO environmentalStatistics = getEnvironmentalStatistics(dto);
        writer.write(environmentalStatistics.getIndicator(), sheet1, table1);
        writer.write(environmentalStatistics.getData(), sheet2, table2);
        writer.finish();
        try {
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        exportDto.setBody(outputStream.toByteArray());
        return exportDto;
    }

    /**
     * Sheet1表格
     */
    private List<List<String>> head1() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("指标名称", "指标名称"));
        headTitles.add(Lists.newArrayList("计量单位", "计量单位"));
        headTitles.add(Lists.newArrayList("代码", "代码"));
        headTitles.add(Lists.newArrayList("本年实际", "本年实际"));
        return headTitles;
    }

    /**
     * Sheet2表格
     */
    private List<List<String>> head2() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("中心", "中心"));
        headTitles.add(Lists.newArrayList("产量", "产量"));
        headTitles.add(Lists.newArrayList("投粮", "投粮"));
        headTitles.add(Lists.newArrayList("投曲", "投曲"));
        headTitles.add(Lists.newArrayList("耗粮", "耗粮"));
        headTitles.add(Lists.newArrayList("耗曲", "耗曲"));
        headTitles.add(Lists.newArrayList("糠壳", "糠壳"));
        headTitles.add(Lists.newArrayList("白酒原酒用量", "白酒原酒用量"));
        return headTitles;
    }

}
