package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.google.common.collect.Lists;
import com.hvisions.brewage.mkwine.ReportHandler.HeadAndContentCellStyle;
import com.hvisions.brewage.mkwine.ReportHandler.StatisticalCellWriteHandler;
import com.hvisions.brewage.mkwine.dao.ReportManage.QuarterlySubmitMapper;
import com.hvisions.brewage.mkwine.dto.ReportManage.QuarterlySubmit.QuarterlySubmitDTO;
import com.hvisions.brewage.mkwine.service.QuarterlySubmitService;
import com.hvisions.brewage.mkwine.vo.ReportManage.QuarterlySubmit.QuarterlySubmitListOne;
import com.hvisions.brewage.mkwine.vo.ReportManage.QuarterlySubmit.QuarterlySubmitListTwo;
import com.hvisions.brewage.mkwine.vo.ReportManage.QuarterlySubmit.QuarterlySubmitVO;
import com.hvisions.common.dto.ExcelExportDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: LiuYu
 * @Date: 2022/9/16 17:58
 * @Describe:
 **/

@Service
public class QuarterlySubmitImpl implements QuarterlySubmitService {

    @Value("${material.type.sorghum}")
    String sorghum;//高粱

    @Value("${material.type.da-qu}")
    String daQu;

    @Value("${material.type.rice-husk}")
    String riceHusk;

    @Value("${material.type.back-alcoholic")
    String backAlcoholic;

    @Value("${material.type.level-wine}")
    String LevelWine;

    @Resource
    private QuarterlySubmitMapper mapper;

    /**
     * @Author: LiuYu
     * @Description: 查询数据
     * @Date: 2022/9/16 17:58
     **/
    @Override
    public QuarterlySubmitVO getData(QuarterlySubmitDTO dto) {
        QuarterlySubmitVO vo = new QuarterlySubmitVO();
        if (dto == null || dto.getYear() == null || dto.getQuarter() == null) {
            return vo;
        }
        List<QuarterlySubmitListOne> listOne = new ArrayList<>();
        List<QuarterlySubmitListTwo> listTwo = new ArrayList<>();
        QuarterlySubmitListTwo two;

        //原料为高粱
        addItem(listOne, this.mapper.getSorghum(dto, this.sorghum));
        //白酒原酒
        addItem(listOne, this.mapper.getWine(dto, this.backAlcoholic));
        //酒曲
        addItem(listOne, this.mapper.getWineKoji(dto, this.daQu));
        //谷壳
        addItem(listOne, this.mapper.getChaff(dto, this.riceHusk));
        //产出白酒
        addItem(listOne, this.mapper.getProduceLiquor(dto, this.LevelWine));
        //蒸馏系统
        two = this.mapper.getDistillationNeedTime(dto);
        if (two == null){
            two = new QuarterlySubmitListTwo();
        }
        two.setRecordContents("蒸馏系统");
        two.setDepartment("生产管理部");
        two.setProductionLoad(this.mapper.getDistillationProductionLoad(dto));
        listTwo.add(two);

        vo.setListOne(listOne);
        vo.setListTwo(listTwo);
        return vo;
    }

    @Override
    public ExcelExportDto exportExcel(QuarterlySubmitDTO dto) {
        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("季度报表—生产管理部-季度报送.xlsx");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
                .registerWriteHandler(new StatisticalCellWriteHandler())
                .build();
        //定义excel
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetName("sheet1");
        sheet1.setSheetNo(0);
        WriteSheet sheet2 = new WriteSheet();
        sheet2.setSheetName("sheet2");
        sheet2.setSheetNo(1);
        WriteTable table1 = new WriteTable();
        table1.setTableNo(0);
        table1.setHead(head1());
        WriteTable table2 = new WriteTable();
        table2.setTableNo(1);
        table2.setHead(head2());

        QuarterlySubmitVO data = this.getData(dto);
        writer.write(data.getListOne(), sheet1, table1);
        writer.write(data.getListTwo(), sheet2, table2);
        writer.finish();
        try {
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        exportDto.setBody(outputStream.toByteArray());
        return exportDto;
    }

    private List<List<String>> head1() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("记录内容", "记录内容"));
        headTitles.add(Lists.newArrayList("名称", "名称"));
        headTitles.add(Lists.newArrayList("数量", "数量"));
        headTitles.add(Lists.newArrayList("单位", "单位"));
        headTitles.add(Lists.newArrayList("备注", "备注"));
        headTitles.add(Lists.newArrayList("填写部门", "填写部门"));
        return headTitles;
    }

    private List<List<String>> head2() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("记录内容", "记录内容"));
        headTitles.add(Lists.newArrayList("运行时间/h", "运行时间/h"));
        headTitles.add(Lists.newArrayList("停产时间/h", "停产时间/h"));
        headTitles.add(Lists.newArrayList("生产负荷/％", "生产负荷/％"));
        headTitles.add(Lists.newArrayList("备注", "备注"));
        headTitles.add(Lists.newArrayList("填写部门", "填写部门"));
        return headTitles;
    }

    /**
     * 用于处理为空情况下进行合并
     * @param target 目标data
     * @param source 来源data
     */
    private void addItem(List<QuarterlySubmitListOne> target, List<QuarterlySubmitListOne> source) {
        if (target != null){
            if (source != null){
                target.addAll(source);
            }
        }
    }

}
