package com.hvisions.brewage.mkwine.controller.productiondisposition;


import com.hvisions.brewage.mkwine.dto.QueryMenuButtonInfoDTO;
import com.hvisions.brewage.mkwine.dto.QueryUserByRoleDTO;
import com.hvisions.brewage.mkwine.service.CommonService;
import com.hvisions.brewage.mkwine.vo.MenuButtonVO;
import com.hvisions.brewage.mkwine.vo.UserLocationVO;
import com.hvisions.brewage.mkwine.vo.UserVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @title: 通用控制器
 * @projectName test
 * @description:
 * @date 2022/3/416:58:16
 * 假接口
 */
@RestController
@RequestMapping("/Common")
@Api(description = "通用接口")
@Slf4j
public class CommonController {
    @Resource
    CommonService commonService;


    @ApiOperation(value = "通过用户id获取用户的车间和中心id")
    @GetMapping("/getUserCenterIdAndLocationId/{userId}")
    public UserLocationVO getUserCenterIdAndLocationId(@PathVariable @ApiParam("用户id") Integer userId) {
        return commonService.getUserCenterIdAndLocationId(userId);
    }


    @ApiOperation(value = "通过 车间或中心 获取全部人员")
    @GetMapping("/getAllPeopleByCenterOrLocation")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "centerId", value = "中心ID,-1代表中心为空", dataType = "int", required = true),
            @ApiImplicitParam(name = "locationId", value = "车间ID,-1代表车间为空", dataType = "int", required = true)
    })
    public List<UserVO> getCenterAllPeople(@RequestParam("centerId") Integer centerId, @RequestParam("locationId") Integer locationId) {
        return commonService.getAllPeopleByCenterOrLocation(centerId, locationId);
    }

    @ApiOperation(value = "通过名字模糊搜索用户")
    @GetMapping("/getUserByName/{name}")
    public List<UserVO> getUserByName(@ApiParam(name = "name", value = "姓名") @PathVariable("name") String name) {
        return commonService.getUserByName(name);
    }

    @ApiOperation(value = "通过用户角色名查询人员")
    @PostMapping("/getCenterPeopleByUserRole")
    public List<UserVO> getCenterPeopleByUserRole(@RequestBody QueryUserByRoleDTO dto) {
        return commonService.getCenterPeopleByUserRole(dto);
    }

    @ApiOperation(value = "APP - 获取按钮权限信息")
    @PostMapping("/getMenuButtonInfo")
    public List<MenuButtonVO> getMenuButtonInfo(@RequestBody QueryMenuButtonInfoDTO dto) {
        return commonService.getMenuButtonInfo(dto);
    }


}
