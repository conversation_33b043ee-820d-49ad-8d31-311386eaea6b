package com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @program: brewage
 * @description: 获取早中晚班
 * @author: DengWeiTao
 **/
@Data
@ApiModel(description = "获取早中晚班")
public class ExecutionDetailVO {

    private String startTime;

    private String endTime;

    private Integer outShiftId;

    private String outOrderCode;
}
