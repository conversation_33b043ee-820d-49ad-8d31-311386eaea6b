package com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderSap;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @title: 批量同步提交DTO（单窖）
 * @projectName brewage
 * @date 2022/6/5 17:41:41
 */
@ApiModel(value = "批量同步提交DTO（单窖）")
@Data
public class BatchSyncPitDTO {
    @ApiModelProperty(value = "SAP订单id")
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "同步类型 1-入窖投入同步 2-工时同步 3-翻窖投入修正 4-产出同步 5-出窖投入同步 6-翻窖投入同步")
    private Integer syncType;

    @ApiModelProperty(value = "是否提交在制同步（1是-0否）")
    private Boolean isSyncFermentationWorkinghours;

    @ApiModelProperty(value = "过账日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pstngDate;
}
