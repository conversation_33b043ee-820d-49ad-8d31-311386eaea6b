package com.hvisions.brewage.mkwine.controller.InspectionItem;

import com.hvisions.brewage.mkwine.dto.InspectionItem.InspectionItemDetailDTO;
import com.hvisions.brewage.mkwine.dto.InspectionItem.InspectionItemDetailPageDTO;
import com.hvisions.brewage.mkwine.enums.ResultTipEnum;
import com.hvisions.brewage.mkwine.service.InspectionItemDetailService;
import com.hvisions.brewage.mkwine.vo.InspectionItem.InspectionItemDetailPageVO;
import com.hvisions.brewage.mkwine.vo.InspectionItem.InspectionItemDetailVO;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: brewage
 * @description: 巡检项目明细
 * @author: DengWeiTao
 **/
@Api(description = "巡检项目明细")
@RequestMapping("/InspectionItemDetail")
@RestController
public class InspectionItemDetailController {

    @Autowired
    private InspectionItemDetailService inspectionItemDetailService;

    @ApiOperation(value = "获取全部巡检项目明细")
    @GetMapping("/getAllInspectionItemDetail")
    public List<InspectionItemDetailVO> getAllInspectionItemDetail(){
        return inspectionItemDetailService.getAllInspectionItemDetail();
    }

    @ApiOperation(value = "根据id获取指定的巡检明细")
    @PostMapping("/getInspectionItemDetailById/{id}")
    public InspectionItemDetailVO getInspectionItemDetailById(@ApiParam(value = "id",example = "1") @PathVariable Integer id){
        return inspectionItemDetailService.getInspectionItemDetailById(id);
    }

    @ApiOperation(value = "分页获取巡检项目明细")
    @PostMapping("/getInspectionItemDetailPage")
    public InspectionItemDetailPageVO getInspectionItemDetailPage(@RequestBody InspectionItemDetailPageDTO inspectionItemDetailPageDTO){
        return inspectionItemDetailService.getInspectionItemDetailPage(inspectionItemDetailPageDTO);
    }

    @ApiOperation(value = "新增巡检项目明细")
    @PostMapping("/addInspectionItemDetail")
    public ResultVO addInspectionItemDetail(@RequestBody InspectionItemDetailDTO anomalyCategoryDTO){
        inspectionItemDetailService.addInspectionItemDetail(anomalyCategoryDTO);
        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
    }

    @ApiOperation(value = "修改巡检项目明细")
    @PostMapping("/updateInspectionItemDetail")
    public ResultVO updateInspectionItemDetail(@RequestBody InspectionItemDetailDTO anomalyCategoryDTO){
        inspectionItemDetailService.updateInspectionItemDetail(anomalyCategoryDTO);
        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
    }

    @ApiOperation(value = "删除巡检项目明细")
    @DeleteMapping("/deleteInspectionItemDetail/{id}")
    public ResultVO deleteInspectionItemDetail(@ApiParam(value = "id",example = "1") @PathVariable Integer id){
        inspectionItemDetailService.deleteInspectionItemDetail(id);
        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
    }

}
