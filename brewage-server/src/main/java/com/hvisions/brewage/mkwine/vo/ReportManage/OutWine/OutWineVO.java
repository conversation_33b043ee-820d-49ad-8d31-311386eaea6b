package com.hvisions.brewage.mkwine.vo.ReportManage.OutWine;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @title: OutWineVO
 * @projectName brewage
 * @date 2022/12/22 23:04:11
 */
@Data
public class OutWineVO {
    private List<OutWineItemVO> data;

    private Integer count;

    @Data
    public static class OutWineItemVO {
        private String location;

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date openPitFinishTime;

        private String pitCode;

        private Double twoQuality;

        private Float twoModifyStandardVol;

        private Double threeQuality;

        private Float threeModifyStandardVol;

        private Double fourQuality;

        private Float fourModifyStandardVol;

        private Double to60Quantity;

        private String increase;

        private String outWineLv;
    }
}
