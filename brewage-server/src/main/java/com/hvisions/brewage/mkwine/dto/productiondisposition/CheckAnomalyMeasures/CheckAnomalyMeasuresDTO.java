package com.hvisions.brewage.mkwine.dto.productiondisposition.CheckAnomalyMeasures;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: 异常处理措施DTO
 * @projectName test
 * @description:
 * @date 2022/3/14 11:27:33
 */
@Data
@ApiModel(value = "异常处理措施DTO")
public class CheckAnomalyMeasuresDTO {
    @ApiModelProperty(value = "id",example = "1")
    private Integer id;

    @ApiModelProperty(value = "检查异常类别id",example = "1")
    private Integer checkAnomalyCategoryId;

    @ApiModelProperty(value = "异常措施编号")
    private String measureCode;

    @ApiModelProperty(value = "异常措施")
    private String measuresName;

    @ApiModelProperty(value = "是否删除",hidden = true)
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createTime;

    @ApiModelProperty(value = "修改时间",hidden = true)
    private Date modifyTime;
}
