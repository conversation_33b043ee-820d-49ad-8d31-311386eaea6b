package com.hvisions.brewage.mkwine.entity.dayplan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * @program: brewage
 * @description: 滴窖任务
 * @author: DengWeiTao
 **/
@Entity
@Data
@ApiModel(description = "滴窖任务")
@Table(name = "t_po_workshop_pit_order_exuding_task")
public class TPoWorkshopPitOrderExudingTask {
    @Id
    @TableId(type = IdType.AUTO)
    @TableField("id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "滴窖任务id", example = "1")
    private Integer id;

    @TableField("exuding_task_code")
    @ApiModelProperty(value = "滴窖任务号")
    private String exudingTaskCode;

    @TableField("order_code")
    @ApiModelProperty(value = "窖池订单号")
    private String orderCode;

    @TableField("day_plan_code")
    @ApiModelProperty(value = "日计划编号")
    private String dayPlanCode;

    @TableField("plan_start_time")
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;

    @TableField("center_id")
    @ApiModelProperty(value = "中心ID")
    private Integer centerId;

    @TableField("location_id")
    @ApiModelProperty(value = "车间ID")
    private Integer locationId;

    @TableField("pit_id")
    @ApiModelProperty(value = "窖池id(连窖)")
    private Integer pitId;

    @TableField("pit_code")
    @ApiModelProperty(value = "窖池编号（连窖）")
    private String pitCode;

    @TableField("in_pit_date")
    @ApiModelProperty(value = "入窖时间")
    private Date inPitDate;

    @TableField("status")
    @ApiModelProperty(value = "状态(枚举创建、滴窖中、已完成)")
    private Integer status;

    @TableField("user")
    @ApiModelProperty(value = "创建人")
    private Integer user;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间（预留）")
    private Date createTime;

    @TableField("exec_start_time")
    @ApiModelProperty(value = "执行开始时间")
    private Date execStartTime;

    @TableField("complete_time")
    @ApiModelProperty(value = "完成时间")
    private Date completeTime;
}
