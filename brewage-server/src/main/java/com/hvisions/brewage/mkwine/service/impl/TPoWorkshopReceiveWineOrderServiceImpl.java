package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.api.R;
import com.google.common.collect.Lists;
import com.hvisions.brewage.entity.SysBase;
import com.hvisions.brewage.mkwine.ReportHandler.HeadAndContentCellStyle;
import com.hvisions.brewage.mkwine.ReportHandler.LongestCellWidthHandler;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TPoWorkshopHandinTaskMapper;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TPoWorkshopWinePotTaskMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.HandInTaskSaveReceiveOrderDTO;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.QueryHandInDTO;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.QueryReceiveDataWorkShopOrderDTO;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.ReceiveWineOrderQueryDTO;
import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopHandinTask;
import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopWinePotTask;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.service.TPoWorkshopHandinTaskService;
import com.hvisions.brewage.mkwine.service.WineHandInTaskPIService;
import com.hvisions.brewage.mkwine.vo.AjaxResult;
import com.hvisions.brewage.mkwine.vo.ExceptionManagement.ViolateRulesLevelVO;
import com.hvisions.brewage.mkwine.vo.LiquorConnectManage.*;
import com.hvisions.brewage.utils.IdHelper;
import com.hvisions.brewage.utils.ReportHeadUtil;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopReceiveWineOrder;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TPoWorkshopReceiveWineOrderMapper;
import com.hvisions.brewage.mkwine.service.TPoWorkshopReceiveWineOrderService;
import org.springframework.transaction.annotation.Transactional;

/**
 * @BelongsProject: brewage
 * @BelongsPackage: com.hvisions.brewage.mkwine.service.impl
 * <AUTHOR>
 * @Description: 收酒码单Service
 * @Date 2022-06-09  18:00
 * @Version: 1.0
 */

@Service
public class TPoWorkshopReceiveWineOrderServiceImpl implements TPoWorkshopReceiveWineOrderService {

    @Resource
    private TPoWorkshopReceiveWineOrderMapper tPoWorkshopReceiveWineOrderMapper;

    @Resource
    private TPoWorkshopHandinTaskMapper tPoWorkshopHandinTaskMapper;

    @Resource
    TPoWorkshopHandinTaskService handinTaskService;

    @Resource
    TPoWorkshopWinePotTaskMapper receivePotTaskMapper;

    @Resource
    WineHandInTaskPIService piService;

    /**
     * 窖池订单
     */
    @Autowired
    WorkshopPitOrderMapper workshopPitOrderMapper;

    /**
     * 单窖窖池订单
     */
    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;

    /**
     * 甑口任务
     */
    @Resource
    WorkshopPitOrderPotTaskMapper workshopPitOrderPotTaskMapper;

    @Resource
    IdHelper idHelper;

    @Override
    public int insert(TPoWorkshopReceiveWineOrder record) {
        return tPoWorkshopReceiveWineOrderMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(TPoWorkshopReceiveWineOrder record) {
        return tPoWorkshopReceiveWineOrderMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<TPoWorkshopReceiveWineOrder> list) {
        return tPoWorkshopReceiveWineOrderMapper.batchInsert(list);
    }

    @Override
    public List<TPoWorkshopReceiveWineOrder> getData(Integer id) {
        List<TPoWorkshopReceiveWineOrder> orders = tPoWorkshopReceiveWineOrderMapper.selectAllByHandinTaskId(id);
        resetReceiveOrderPercent(orders);
        return orders;
    }

    @Override
    public void resetReceiveOrderPercent(List<TPoWorkshopReceiveWineOrder> orders){
        if (CollectionUtils.isEmpty(orders)){
            return;
        }
        double sum = orders.stream().mapToDouble(TPoWorkshopReceiveWineOrder::getOutPitNum).sum();
        if (sum != 0) {
            orders.forEach(p -> {
                double percent = BigDecimal.valueOf((p.getOutPitNum() / sum) * 100).setScale(2, RoundingMode.HALF_UP).doubleValue();
                p.setPercentStr(percent + "%");
            });
        }
    }

    /**
     * 获取窖池订单数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<QueryReceiveDataWorkShopOrderVO> receiveGetWorkShopOrderVO(QueryReceiveDataWorkShopOrderDTO dto) {
        List<QueryReceiveDataWorkShopOrderVO> queryReceiveDataWorkShopOrderVOS = tPoWorkshopReceiveWineOrderMapper.receiveGetWorkShopOrderVO(dto);
        if (CollectionUtils.isEmpty(queryReceiveDataWorkShopOrderVOS)) {
            return new ArrayList<>();
        }
        // 获取甑口任务数据
        List<String> collect = queryReceiveDataWorkShopOrderVOS.stream().map(QueryReceiveDataWorkShopOrderVO::getPitOrderCode).collect(Collectors.toList());
        List<QueryReceiveDataWorkShopOrderVO> potTaskOutPutData = tPoWorkshopReceiveWineOrderMapper.getPotTaskOutPutData(collect);
        for (QueryReceiveDataWorkShopOrderVO orderVO : queryReceiveDataWorkShopOrderVOS) {
            QueryReceiveDataWorkShopOrderVO vo = potTaskOutPutData.stream().filter(p -> p.getPitOrderCode().equals(orderVO.getPitOrderCode())).findFirst().orElse(null);
            if (vo == null) {
                continue;
            }
            orderVO.setDistillationCount(vo.getDistillationCount());
        }

        return queryReceiveDataWorkShopOrderVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateReceiveOrderData(HandInTaskSaveReceiveOrderDTO dto) {

        if (dto.getList() == null) {
            dto.setList(new ArrayList<>());
        }

        List<TPoWorkshopReceiveWineOrder> addNewData = new ArrayList<TPoWorkshopReceiveWineOrder>();
        List<TPoWorkshopReceiveWineOrder> updateData = new ArrayList<TPoWorkshopReceiveWineOrder>();
        List<TPoWorkshopReceiveWineOrder> deleteData = new ArrayList<TPoWorkshopReceiveWineOrder>();

        TPoWorkshopHandinTask handinTask = tPoWorkshopHandinTaskMapper.getHandInTaskWithLocationCode(dto.getHandInTaskId());
        if (handinTask == null) {
            return AjaxResult.error("交酒任务数据为空");
        }

        // 检查交酒任务收酒码单是否存在
        if (StringUtils.isBlank(handinTask.getReceiveTankCode())) {
            // 获取收酒码单编号
            DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyMMdd");
            String dateString = LocalDate.now().format(formatters);
            String key = String.format("%s_%s_", handinTask.getLocationName(), dateString);
            String receiveTaskCode = idHelper.getKeyValue(key, true, 1, TimeUnit.DAYS, 3);
            handinTask.setReceiveWineCode(receiveTaskCode);
            tPoWorkshopHandinTaskMapper.updateByPrimaryKeySelective(handinTask);
        }

        List<TPoWorkshopReceiveWineOrder> receiveWineOrders = tPoWorkshopReceiveWineOrderMapper.selectAllByHandinTaskId(dto.getHandInTaskId());
        List<Integer> idCollect = new ArrayList<>();

        for (TPoWorkshopReceiveWineOrder order : dto.getList()) {

            idCollect.add(order.getId());

            if (order.getHandinTaskId() == null) {
                throw new BaseKnownException(1000, "窖池订单：" + order.getPitOrderCode() + "，交酒任务Id为空");
            }

            // 新增
            if (order.getId() == null) {
                addNewData.add(order);
                continue;
            }

            // 更新
            updateData.add(order);
        }


        deleteData = receiveWineOrders.stream().filter(p -> !idCollect.contains(p.getId())).collect(Collectors.toList());
        List<Integer> deleteDataIds = deleteData.stream().map(TPoWorkshopReceiveWineOrder::getId).collect(Collectors.toList());

        //updateOrderAndPotTask(addNewData, deleteData, handinTask);

        if (addNewData.size() > 0) {
            tPoWorkshopReceiveWineOrderMapper.batchInsert(addNewData);
        }

        if (updateData.size() > 0) {
            tPoWorkshopReceiveWineOrderMapper.updateBatchSelective(updateData);
        }

        if (deleteDataIds.size() > 0) {
            tPoWorkshopReceiveWineOrderMapper.deleteById(deleteDataIds);
        }

        updateReceivePotTask(dto,deleteDataIds,updateData);

        return AjaxResult.success();
    }


    private void updateReceivePotTask(HandInTaskSaveReceiveOrderDTO dto,List<Integer> deleteDataIds,List<TPoWorkshopReceiveWineOrder> updateData){

        LambdaQueryWrapper<TPoWorkshopWinePotTask> queryWrapper = new LambdaQueryWrapper<TPoWorkshopWinePotTask>()
                .eq(TPoWorkshopWinePotTask::getHandinTaskId,dto.getHandInTaskId())
                .eq(TPoWorkshopWinePotTask::getIsDeleted,false);
        List<TPoWorkshopWinePotTask> winePotTasks = receivePotTaskMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(winePotTasks)){
            return;
        }

        List<Integer> deleteReceivePotIds = winePotTasks.stream().filter(p -> deleteDataIds.contains(p.getWineOrderId())).map(TPoWorkshopWinePotTask::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteReceivePotIds)){
            receivePotTaskMapper.deleteBatchIds(deleteReceivePotIds);
        }

        List<TPoWorkshopWinePotTask> updateReceivePotTask = winePotTasks.stream().filter(p -> !deleteDataIds.contains(p.getWineOrderId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(updateReceivePotTask)){
            return;
        }

        handinTaskService.updateAndSaveWinePotTasks(dto.getHandInTaskId(),updateData,updateReceivePotTask);
        //for (TPoWorkshopReceiveWineOrder receiveWineOrder : updateData) {
        //    List<TPoWorkshopWinePotTask> collect = updateReceivePotTask.stream().filter(p -> p.getWineOrderId().equals(receiveWineOrder.getId())).collect(Collectors.toList());
        //    handinTaskService.checkPotTaskQuantityAndTo60Quantity(receiveWineOrder,collect);
        //}
        //
        //receivePotTaskMapper.updateBatchSelective(updateReceivePotTask);
    }

    /**
     * 导出收酒码单报表
     *
     * @param taskId
     * @return
     */
    @Override
    public ExcelExportDto getReceiveReportExcel(Integer taskId) throws Exception {

        QueryHandInDTO dto = new QueryHandInDTO();
        dto.setTaskId(taskId);
        dto.setPage(0);
        dto.setPageSize(1);
        List<TPoWorkshopHandinTask> handinTaskList = tPoWorkshopHandinTaskMapper.selectAllData2(dto);
        if (CollectionUtils.isEmpty(handinTaskList)) {
            throw new BaseKnownException(1000, "查找失败");
        }

        ReceiveOrderMainData mainData = new ReceiveOrderMainData().convertReceiveOrder(handinTaskList.get(0));

        List<ReceiveOrderDetailData> detailData = new ArrayList<ReceiveOrderDetailData>();
        List<TPoWorkshopReceiveWineOrder> receiveWineOrders = tPoWorkshopReceiveWineOrderMapper.selectAllByHandinTaskId(taskId);

        for (TPoWorkshopReceiveWineOrder receiveWineOrder : receiveWineOrders) {
            detailData.add(new ReceiveOrderDetailData().getDetailData(receiveWineOrder, handinTaskList.get(0)));
        }

        // 文件输出位置
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
                .registerWriteHandler(HeadAndContentCellStyle.getCellCommonStyle())
                .registerWriteHandler(new LongestCellWidthHandler())
                .excelType(ExcelTypeEnum.XLSX)
                .build();


        // 动态添加表头，适用一些表头动态变化的场景
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetNo(0);
        sheet1.setSheetName("sheet");

        // 创建一个表格，用于 Sheet 中使用
        WriteTable table = new WriteTable();
        table.setTableNo(0);
        table.setClazz(ReceiveOrderMainData.class);

        WriteTable table2 = new WriteTable();
        table2.setTableNo(1);
        table2.setRelativeHeadRowIndex(3);
        table2.setClazz(ReceiveOrderDetailData.class);

        // 写数据
        writer.write(Lists.newArrayList(mainData), sheet1, table);
        writer.write(detailData, sheet1, table2);

        writer.finish();
        outputStream.close();

        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("收酒码单.xlsx");
        exportDto.setBody(outputStream.toByteArray());

        return exportDto;
    }

    @Override
    public ExcelExportDto getReceiveReportExcelBySearch(QueryHandInDTO dto) throws Exception {

        dto.setPage(null);
        dto.setPageSize(null);

        List<QueryReceiveWineOrderVO> queryReceiveWineOrderVOS = tPoWorkshopHandinTaskMapper.queryReceiveWineOrder(dto);

        if (CollectionUtils.isEmpty(queryReceiveWineOrderVOS)) {
            throw new BaseKnownException(1000, "数据为空");
        }

        List<Integer> idCollect = queryReceiveWineOrderVOS.stream().map(QueryReceiveWineOrderVO::getId).collect(Collectors.toList());

        List<TPoWorkshopReceiveWineOrder> receiveWineOrders = tPoWorkshopReceiveWineOrderMapper.selectAllByHandinTaskIdList(idCollect);
        List<ReceiveOrderDetailData> detailData = new ArrayList<ReceiveOrderDetailData>();

        for (TPoWorkshopReceiveWineOrder receiveWineOrder : receiveWineOrders) {
            QueryReceiveWineOrderVO data = queryReceiveWineOrderVOS.stream().filter(p -> p.getId().equals(receiveWineOrder.getHandinTaskId())).findFirst().orElse(null);
            if (data == null) {
                continue;
            }
            detailData.add(new ReceiveOrderDetailData().getDetailData(receiveWineOrder, data));
        }

        // 文件输出位置
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
                .registerWriteHandler(HeadAndContentCellStyle.getCellCommonStyle())
                .registerWriteHandler(new LongestCellWidthHandler())
                .excelType(ExcelTypeEnum.XLSX)
                .build();

        WriteTable table2 = new WriteTable();
        table2.setTableNo(1);
        table2.setClazz(ReceiveOrderDetailData.class);

        // 动态添加表头，适用一些表头动态变化的场景
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetNo(0);
        sheet1.setSheetName("sheet");

        // 写数据
        writer.write(detailData, sheet1, table2);

        writer.finish();
        outputStream.close();

        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("收酒码单.xlsx");
        exportDto.setBody(outputStream.toByteArray());

        return exportDto;
    }

    /**
     * 分页查询收酒码单
     * @param queryDto
     * @return
     */
    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReceiveWineOrderPageVO> getReceiveWineOrderPage(ReceiveWineOrderQueryDTO queryDto) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReceiveWineOrderPageVO> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        //有的页面page传0
        if (queryDto.getPage() == 0) {
            queryDto.setPage(1);
        }
        queryDto.setPage((queryDto.getPage() - 1) * queryDto.getPageSize());
        List<ReceiveWineOrderPageVO> record = tPoWorkshopReceiveWineOrderMapper.selectReceiveWineOrderPage(queryDto);
        page.setTotal(tPoWorkshopReceiveWineOrderMapper.selectReceiveWineOrderCount(queryDto));
        page.setSize(queryDto.getPageSize());
        page.setPages((page.getTotal() + page.getSize() - 1) / page.getSize());
        page.setRecords(record);
        return page;
    }

    /**
     * 更新单/双窖池订单
     * 更新甑口任务
     */
    private void updateOrderAndPotTask(List<TPoWorkshopReceiveWineOrder> addNewData, List<TPoWorkshopReceiveWineOrder> deleteData, TPoWorkshopHandinTask task) {

        ArrayList<Integer> ordersIds = new ArrayList<>();

        List<Integer> addIds = addNewData.stream().map(TPoWorkshopReceiveWineOrder::getPitOrderId).collect(Collectors.toList());
        List<Integer> deleteIds = deleteData.stream().map(TPoWorkshopReceiveWineOrder::getPitOrderId).collect(Collectors.toList());

        ordersIds.addAll(addIds);
        ordersIds.addAll(deleteIds);

        if (ordersIds.size() == 0) {
            return;
        }

        LambdaQueryWrapper<TPoWorkshopPitOrderSap> sapQuery = new LambdaQueryWrapper<>();
        sapQuery.in(TPoWorkshopPitOrderSap::getId, ordersIds);
        sapQuery.select(TPoWorkshopPitOrderSap::getOrderCodeId);
        sapQuery.select(TPoWorkshopPitOrderSap::getId);

        List<TPoWorkshopPitOrderSap> orderSapDatas = workshopPitOrderSapMapper.selectList(sapQuery);

        if (deleteData.size() > 0) {
            updateOrderAndPotData(orderSapDatas, deleteIds, deleteData, false, task);
        }

        if (addNewData.size() > 0) {
            updateOrderAndPotData(orderSapDatas, addIds, addNewData, true, task);
        }

    }

    private void updateOrderAndPotData(List<TPoWorkshopPitOrderSap> orderSapDatas, List<Integer> idList, List<TPoWorkshopReceiveWineOrder> receiveWineOrderData, boolean isAddNew, TPoWorkshopHandinTask task) {
        // 获取连窖订单id
        List<Integer> collect = orderSapDatas.stream().filter(p -> idList.contains(p.getId())).map(TPoWorkshopPitOrderSap::getOrderCodeId).collect(Collectors.toList());
        // 获取单窖订单
        List<String> orderCodeList = receiveWineOrderData.stream().map(TPoWorkshopReceiveWineOrder::getPitOrderCode).collect(Collectors.toList());

        // 1.更新单窖订单
        LambdaUpdateWrapper<TPoWorkshopPitOrderSap> updateSapOrder = new LambdaUpdateWrapper<TPoWorkshopPitOrderSap>();
        updateSapOrder.set(TPoWorkshopPitOrderSap::getOrderStatus, isAddNew ? 4 : 3);
        updateSapOrder.set(TPoWorkshopPitOrderSap::getHandinFinishTime, isAddNew ? task.getEndTime() : null);
        updateSapOrder.in(TPoWorkshopPitOrderSap::getId, idList);
        workshopPitOrderSapMapper.update(null, updateSapOrder);

        // 2.更新连窖订单
        LambdaUpdateWrapper<TPoWorkshopPitOrder> updateOrder = new LambdaUpdateWrapper<TPoWorkshopPitOrder>();
        updateOrder.set(TPoWorkshopPitOrder::getOrderStatus, isAddNew ? 4 : 3);
        updateOrder.set(TPoWorkshopPitOrder::getHandinFinishTime, isAddNew ? task.getEndTime() : null);
        updateOrder.in(TPoWorkshopPitOrder::getId, collect);
        workshopPitOrderMapper.update(null, updateOrder);

        String[] split = task.getStageNum().split(",");

        // 删除 - 更新甑口任务
        if (!isAddNew) {

            LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask> updatePotTask = new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>();
            updatePotTask.in(TPoWorkshopPitOrderPotTask::getOutOrderCode, orderCodeList);
            for (String s : split) {
                if ("1".equals(s)) {
                    updatePotTask.set(TPoWorkshopPitOrderPotTask::getDistillateFirstTankId, null);
                } else if ("2".equals(s)) {
                    updatePotTask.set(TPoWorkshopPitOrderPotTask::getDistillate2_TankId, null);
                } else if ("3".equals(s)) {
                    updatePotTask.set(TPoWorkshopPitOrderPotTask::getDistillate3_TankId, null);
                } else if ("4".equals(s)) {
                    updatePotTask.set(TPoWorkshopPitOrderPotTask::getDistillate4_TankId, null);
                }
            }
            workshopPitOrderPotTaskMapper.update(null, updatePotTask);
            return;

        }

        // 新增 - 更新甑口任务
        // 查询相关甑口任务
        List<HandTaskGetPitOrderPotTaskVO> potTaskData = tPoWorkshopReceiveWineOrderMapper.getPitOrderPotTaskByCode(orderCodeList);

        //if (potTaskData == null || potTaskData.size() == 0) {
        //    throw new BaseKnownException(10000, "新增的窖池订单甑口任务数为空");
        //}


        // 3.1 检查订单的甑口任务相关段次有没有交过酒 ( 8月24号 取消限制)
        //for (HandTaskGetPitOrderPotTaskVO data : potTaskData) {
        //    for (String s : split) {
        //        if (("1".equals(s) && data.getDistillateFirstTankId() != null)
        //                || ("2".equals(s) && data.getDistillate2_TankId() != null)
        //                || ("3".equals(s) && data.getDistillate3_TankId() != null)
        //                || ("4".equals(s) && data.getDistillate4_TankId() != null)) {
        //            throw new BaseKnownException(10000, "窖池订单：" + data.getOutOrderCode() + "，" + task.getStageNum() + "段次已交酒");
        //        }
        //    }
        //}

        // 3.2 检查订单有没有这么多的甑口任务 （预留步骤）

        // 3.3 更新甑口任务
        List<HandTaskGetPitOrderPotTaskVO> potTasks = getTmpTankIdInPitOrderPotTask(potTaskData, task);

        if (!CollectionUtils.isEmpty(potTasks)) {
            tPoWorkshopReceiveWineOrderMapper.updateBatchPitOrderPotTask(potTasks);
        }

    }

    /**
     * 获取需要更新的甑口任务
     *
     * @param pitOrderPotTask
     * @param handinTask
     * @return
     */
    private List<HandTaskGetPitOrderPotTaskVO> getTmpTankIdInPitOrderPotTask(List<HandTaskGetPitOrderPotTaskVO> pitOrderPotTask, TPoWorkshopHandinTask handinTask) {

        String[] split = handinTask.getStageNum().split(",");

        List<HandTaskGetPitOrderPotTaskVO> list = new ArrayList<>();

        for (HandTaskGetPitOrderPotTaskVO taskVO : pitOrderPotTask) {

            HandTaskGetPitOrderPotTaskVO vo = new HandTaskGetPitOrderPotTaskVO();
            vo.setId(taskVO.getId());

            for (String s : split) {
                if ("1".equals(s)) {
                    vo.setDistillateFirstTankId(handinTask.getTmptankId());
                } else if ("2".equals(s)) {
                    vo.setDistillate2_TankId(handinTask.getTmptankId());
                } else if ("3".equals(s)) {
                    vo.setDistillate3_TankId(handinTask.getTmptankId());
                } else if ("4".equals(s)) {
                    vo.setDistillate4_TankId(handinTask.getTmptankId());
                }
            }

            list.add(vo);
        }

        return list;
    }


}
