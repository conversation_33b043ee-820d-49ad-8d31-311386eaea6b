package com.hvisions.brewage.utils;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: FlowGenerationUtril
 * @description:
 * @date 2025/6/13 16:54
 */
public class FlowGenerationUtil {

    /**
     * 流水码生成，格式：业务类型+4位流水
     * @param typeName 业务类型
     * @param serialNum
     * @return
     */
    public static String generation(String typeName,Integer serialNum){
        //判断传过来的是不是null
        if(null==serialNum){
            serialNum=0;
        }
        return typeName+String.format("%04d", serialNum+1);
    }

    /**
     * 流水码生成，格式：业务类型+年月日+三位流水
     * @param typeName 业务类型
     * @param serialNum 当前流水位数
     * @return
     */
    public static String generationYH(String typeName,Integer serialNum){
        //判断传过来的是不是null
        if(null==serialNum){
            serialNum=0;
        }
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 定义格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 格式化日期
        String formattedDate = today.format(formatter);
        return typeName+formattedDate+String.format("%03d", serialNum+1);
    }
}
