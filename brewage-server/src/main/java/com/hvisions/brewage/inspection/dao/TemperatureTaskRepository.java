package com.hvisions.brewage.inspection.dao;

import com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoWorkshopPitTemperatureTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TemperatureTaskRepository extends JpaRepository<TPoWorkshopPitTemperatureTask, Integer> {

    Optional<TPoWorkshopPitTemperatureTask> findByPitOrderCode(String pitOrderCode);
}
