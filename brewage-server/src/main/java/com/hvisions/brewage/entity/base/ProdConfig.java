package com.hvisions.brewage.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 生产配置
 */
@ApiModel(value = "生产配置")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "base_prod_config")
public class ProdConfig extends SysBaseNew {
    /**
     * 生产配置编码
     */
    @TableField(value = "config_code")
    @ApiModelProperty(value = "生产配置编码")
    private String configCode;

    /**
     * 生产配置名称
     */
    @TableField(value = "config_name")
    @ApiModelProperty(value = "生产配置名称")
    private String configName;

    /**
     * 父级id
     */
    @TableField(value = "pid")
    @ApiModelProperty(value = "父级id")
    private Integer pid;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;
}