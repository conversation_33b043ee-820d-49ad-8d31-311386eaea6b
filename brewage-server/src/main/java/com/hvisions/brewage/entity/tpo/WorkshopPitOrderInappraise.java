package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.*;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
    * 入窖鉴定
    */
@ApiModel(value="入窖鉴定")
@Data
@TableName(value = "t_po_workshop_pit_order_inappraise")
public class WorkshopPitOrderInappraise{
    /**
     * 中心id
     */
    @TableField(value = "center_id")
    @ApiModelProperty(value="中心id")
    private Integer centerId;

    /**
     * 来源窖号id(连窖的窖池id)
     */
    @TableField(value = "from_pit_id")
    @ApiModelProperty(value="来源窖号id(连窖的窖池id)")
    private Integer fromPitId;

    /**
     * 入窖鉴定时间
     */
    @TableField(value = "in_appraise_time")
    @ApiModelProperty(value="入窖鉴定时间")
    private Date inAppraiseTime;

    /**
     * 车间id
     */
    @TableField(value = "location_id")
    @ApiModelProperty(value="车间id")
    private Integer locationId;

    /**
     * 窖池订单id
     */
    @TableField(value = "order_id")
    @ApiModelProperty(value="窖池订单id")
    private String orderId;

    /**
     * 入窖窖池id(连窖的窖池id)
     */
    @TableField(value = "pit_id")
    @ApiModelProperty(value="入窖窖池id(连窖的窖池id)")
    private Integer pitId;

    /**
     * 感官糠(稻壳)水状况
     */
    @TableField(value = "sense_ferment_moist")
    @ApiModelProperty(value="感官糠(稻壳)水状况")
    private String senseFermentMoist;

    /**
     * 感官形态
     */
    @TableField(value = "sense_form")
    @ApiModelProperty(value="感官形态")
    private String senseForm;

    /**
     * 鉴定人id
     */
    @TableField(value = "user")
    @ApiModelProperty(value="鉴定人id")
    private Integer user;

    /**
     * 入窖温度(min)
     */
    @TableField(value = "min_loading_temp")
    @ApiModelProperty(value="入窖温度(min)")
    private BigDecimal minLoadingTemp;

    /**
     * 入窖温度(max)
     */
    @TableField(value = "max_loading_temp")
    @ApiModelProperty(value="入窖温度(max)")
    private BigDecimal maxLoadingTemp;

    /**
     * 稻壳(%)
     */
    @TableField(value = "rice_hull_percent")
    @ApiModelProperty(value="稻壳(%)")
    private BigDecimal riceHullPercent;

    /**
     * 量水(%)
     */
    @TableField(value = "water_addition_percent")
    @ApiModelProperty(value="量水(%)")
    private BigDecimal waterAdditionPercent;

    /**
     * 糟醅取样数量
     */
    @TableField(value = "fermented_grain_samples")
    @ApiModelProperty(value="糟醅取样数量")
    private Integer fermentedGrainSamples;

    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.AUTO)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 创建时间
     */
    @Column(updatable = false)
    @TableField(fill = FieldFill.INSERT)
    @CreatedDate
    protected Date createTime;

    /**
     * 修改时间
     */
    @LastModifiedDate
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Date updateTime;

    /**
     * 创建人
     */
    @Column(updatable = false)
    @CreatedBy
    @TableField(fill = FieldFill.INSERT)
    protected Integer creatorId;

    /**
     * 修改人
     */
    @LastModifiedBy
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Integer updaterId;

    @TableLogic
    @Column(columnDefinition = "bit default false not null")
    private Boolean isDeleted;

}