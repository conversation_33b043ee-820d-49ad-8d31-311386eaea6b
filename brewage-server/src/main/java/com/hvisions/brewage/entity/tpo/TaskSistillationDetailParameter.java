package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
*@Author：JIA WANG
*@Package：com.hvisions.brewage.entity.tpo
*@Project：酿酒二期
*@name：TaskSistillationDetailParameter
*@Date：2025/7/9  10:27
*@Filename：TaskSistillationDetailParameter
*/
/**
 * 蒸馏任务详情参数
 */
@ApiModel(description="蒸馏任务详情参数")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "t_po_task_sistillation_detail_parameter")
public class TaskSistillationDetailParameter extends SysBaseNew {

    @TableField(value = "task_sistillation_detail_id")
    @ApiModelProperty(value="蒸馏任务详情主键id")
    private Integer taskSistillationDetailId;

    @TableField(value = "metric_name")
    @ApiModelProperty(value="采集名称")
    private String metricName;

    @TableField(value = "collection_time")
    @ApiModelProperty(value="采集时间")
    private Date collectionTime;

    @TableField(value = "measured_value")
    @ApiModelProperty(value="采集值")
    private String measuredValue;

}