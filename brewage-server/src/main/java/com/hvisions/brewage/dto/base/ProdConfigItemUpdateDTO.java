package com.hvisions.brewage.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ProdConfigItemUpdateDTO
 * @description: 生产配置项修改DTO
 * @date 2025/6/26 11:48
 */
@ApiModel(value="生产配置项修改DTO")
@Data
public class ProdConfigItemUpdateDTO {
    @ApiModelProperty(value="生产配置项主键id",required = true)
    @NotNull(message = "id不能为空")
    private Integer id;

    @ApiModelProperty(value = "生产配置主键id",required = true)
    @NotNull(message = "生产配置主键id不能为空")
    private Integer prodConfigId;

    @ApiModelProperty(value="中心",required = true)
    @NotBlank(message = "中心不能为空")
    private String center;

    @ApiModelProperty(value="车间",required = true)
    @NotBlank(message = "车间不能为空")
    private String workshop;

    @ApiModelProperty(value="配置项名称",required = true)
    @NotBlank(message = "配置项名称不能为空")
    private String itemName;

    @ApiModelProperty(value="配置项值",required = true)
    @NotBlank(message = "配置项值不能为空")
    private String itemValue;

    @ApiModelProperty(value="是否定时执行(0:否 1:是)",required = true)
    @NotNull(message = "是否定时执行不能为空")
    private Boolean isTimedEx;

    @ApiModelProperty(value="备注")
    private String remarks;
}
