package com.hvisions.brewage.dto.tpo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskLoadingQueryDTO
 * @description: 入窖任务PDA查询DTO
 * @date 2025/7/8 17:02
 */
@ApiModel(value="入窖任务PDA查询DTO")
@Data
public class TaskLoadingPDAQueryDTO extends PageInfo {
    @ApiModelProperty(value="任务号")
    private String taskNo;

    @ApiModelProperty(value="中心id")
    private Integer centerId;

    @ApiModelProperty(value="车间id")
    private Integer locationId;

    @ApiModelProperty(value="窖池号")
    private String pitNo;

    @ApiModelProperty(value="窖池订单号")
    private String pitOrder;

    @ApiModelProperty(value="任务状态(待执行、已完成)")
    private String status;

    @ApiModelProperty(value = "创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
}
