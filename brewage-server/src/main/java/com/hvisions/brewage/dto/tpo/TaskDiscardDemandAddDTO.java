package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDiscardDemandAddDTO
 * @description:
 * @date 2025/7/18 11:15
 */
@ApiModel(value="丢糟需求新增DTO")
@Data
public class TaskDiscardDemandAddDTO {
    @ApiModelProperty(value="中心id",required = true)
    @NotNull(message = "中心id不能为空")
    private Integer centerId;

    @ApiModelProperty(value="车间id",required = true)
    @NotNull(message = "车间id不能为空")
    private Integer locationId;

    @ApiModelProperty(value="预估丢糟甑口",required = true)
    @NotNull(message = "预估丢糟甑口不能为空")
    private Integer estimatedBatches;

    @ApiModelProperty(value="需求日期",required = true)
    @NotNull(message = "需求日期不能为空")
    private Date demandDate;
}
