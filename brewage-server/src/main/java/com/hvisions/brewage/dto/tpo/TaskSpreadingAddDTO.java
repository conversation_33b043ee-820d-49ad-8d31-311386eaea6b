package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingAddDTO
 * @description: 摊晾任务新增DTO
 * @date 2025/7/7 14:16
 */
@ApiModel(value="摊晾任务新增DTO")
@Data
public class TaskSpreadingAddDTO {
    @ApiModelProperty(value="中心id",required = true)
    @NotNull(message = "中心id不能为空")
    private Integer centerId;

    @ApiModelProperty(value="车间id",required = true)
    @NotNull(message = "车间id不能为空")
    private Integer locationId;

    @ApiModelProperty(value="窖池号",required = true)
    @NotEmpty(message = "窖池号不能为空")
    private String pitNo;

    @ApiModelProperty(value="糟源类别",required = true)
    @NotEmpty(message = "糟源类别不能为空")
    private String materialType;

    @ApiModelProperty(value="窖池订单号",required = true)
    @NotEmpty(message = "窖池订单号不能为空")
    private String pitOrder;
}
