///**
// * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
// * <p>
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// * <p>
// * http://www.apache.org/licenses/LICENSE-2.0
// * <p>
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//package com.hvisions.brewage.plan.dao;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.hvisions.brewage.plan.entity.GwfDetail;
//import org.apache.ibatis.annotations.Mapper;
//
///**
// * 等级酒换算配置详情 Mapper 接口
// *
// * <AUTHOR>
// * @date 2022-03-23
// */
//@Mapper
//public interface GwfDetailMapper extends BaseMapper<GwfDetail> {
//}