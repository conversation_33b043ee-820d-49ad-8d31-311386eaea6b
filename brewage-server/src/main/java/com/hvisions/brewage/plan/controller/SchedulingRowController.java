///**
// * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
// * <p>
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// * <p>
// * http://www.apache.org/licenses/LICENSE-2.0
// * <p>
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//package com.hvisions.brewage.plan.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.hvisions.brewage.plan.dto.SchedulingRowQueryReq;
//import com.hvisions.brewage.plan.entity.SchedulingRow;
//import com.hvisions.brewage.plan.service.ISchedulingRowService;
//import com.hvisions.brewage.plan.vo.SchedulingRowVO;
//import com.hvisions.brewage.utils.BaseWrapper;
//import com.hvisions.common.dto.PageInfo;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * 排程排次管理相关接口
// *
// * <AUTHOR>
// * 2022-03-23
// */
//@RestController
//@RequestMapping("/system/SchedulingRow")
//@Api(value = "排程排次管理相关接口", tags = "排程排次管理相关接口")
//public class SchedulingRowController {
//
//    @Autowired
//    ISchedulingRowService baseService;
//    @Autowired
//    BaseWrapper baseWrapper;
//
//    /**
//     * 查询分页排程排次
//     *
//     * @param req      查询条件
//     * @param pageInfo 分页对象
//     * @return
//     */
//    @GetMapping("/page")
//    @ApiOperation(value = "查询分页排程排次")
////    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
//    public Page<SchedulingRowVO> queryPagedSchedulingRow(SchedulingRowQueryReq req, PageInfo pageInfo) {
//        LambdaQueryWrapper<SchedulingRow> wrapper = new LambdaQueryWrapper<SchedulingRow>()
//                .eq(SchedulingRow::getSchedulingId, req.getPlanId());
//        IPage<SchedulingRow> page = baseService.page(new Page<>(pageInfo.getPage(), pageInfo.getPageSize()), wrapper);
//        return baseWrapper.convertToPage(page, SchedulingRowVO.class);
//    }
//}