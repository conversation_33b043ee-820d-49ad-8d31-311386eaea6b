/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.brewage.plan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @classname CleanDays
 * @description 打扫天数实体类
 * @date 2022-03-23
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
//@TableName(value = "t_pp_clean_days")
@ApiModel("CleanDays对象")
public class CleanDays extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("生产基地id")
    private String productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("大于天数")
    private Integer greaterDays;

    @ApiModelProperty("大于天数打扫时间")
    private BigDecimal greaterCleanDays;

    @ApiModelProperty("等于天数")
    private Integer equalDays;

    @ApiModelProperty("等于天数")
    private BigDecimal equalCleanDays;

    @ApiModelProperty("小于天数")
    private Integer lessDays;

    @ApiModelProperty("小于天数")
    private BigDecimal lessCleanDays;

    @ApiModelProperty("生效时间")
    private LocalDateTime effectTime;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

}