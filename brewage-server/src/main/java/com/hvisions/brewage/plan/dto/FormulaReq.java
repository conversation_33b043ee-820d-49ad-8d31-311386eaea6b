/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.brewage.plan.dto;

import com.hvisions.brewage.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @classname FormulaReq
 * @description 糟源配方数据传输对象实体类
 * @date 2022-03-23
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("Formula数据传输对象实体类")
public class FormulaReq extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("配方编号")
    private String code;

    @ApiModelProperty("配方名称")
    private String name;

    @ApiModelProperty("中心")
    private Integer[] centers;

    @ApiModelProperty("糟源ID")
    private Integer sourceId;

    @ApiModelProperty("糟源代码")
    private String sourceCode;

    @ApiModelProperty("单位;z-每/甑、g-每/窖")
    private String unit;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

    @ApiModelProperty("生效开始日期")
    private String beginTime;

    @ApiModelProperty("生效结束日期")
    private String endTime;

    @ApiModelProperty("指定拷贝配方的主键")
    private Integer copyId;
}