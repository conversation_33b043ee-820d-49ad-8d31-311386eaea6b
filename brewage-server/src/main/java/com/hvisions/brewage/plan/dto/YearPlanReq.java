package com.hvisions.brewage.plan.dto;

import com.hvisions.brewage.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname YearPlanReq
 * @description 年度计划数据传输对象实体类
 * @date 2022-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("YearPlan数据传输对象实体类")
public class YearPlanReq extends SysBase {

    @ApiModelProperty("计划编码")
    private String code;

    @ApiModelProperty("计划名称")
    private String name;

    @ApiModelProperty("排程id")
    private Integer schedulingId;

    @ApiModelProperty("基地id")
    private Integer plantId;

    @ApiModelProperty("基地代码")
    private String plantCode;

    @ApiModelProperty("基地名称")
    private String plantName;

    @ApiModelProperty("中心id")
    private Integer centerId;

    @ApiModelProperty("中心代码")
    private String centerCode;

    @ApiModelProperty("中心名称")
    private String centerName;

    @ApiModelProperty("车间id")
    private Integer locationId;

    @ApiModelProperty("车间编码")
    private String locationCode;

    @ApiModelProperty("车间名称")
    private String locationName;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("计划投粮")
    private BigDecimal feeding;

    @ApiModelProperty("计划耗粮")
    private BigDecimal consumption;

    @ApiModelProperty("计划基酒产出")
    private BigDecimal winOutput;

    @ApiModelProperty("实际投粮")
    private BigDecimal actualFeeding;

    @ApiModelProperty("实际耗粮")
    private BigDecimal actualConsumption;

    @ApiModelProperty("实际基酒产出")
    private BigDecimal actualWinOutput;

    @ApiModelProperty("状态;0-执行中、1-已完成、2-归档")
    private String state;

}