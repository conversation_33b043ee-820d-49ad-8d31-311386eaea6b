package com.hvisions.brewage.repository;

import com.hvisions.brewage.entity.HvUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: DemoEntityRepository</p>
 * <p>Description: 使用Jpa进行操作</p>
 * <p>如果不需要使用Sepcification查询方式。可以不用实现JpaSpecificationExecutor接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface UserRepository extends BaseRepository<HvUser, Integer> {
    /**
     * 根据名称查询
     *
     * @param name 名称
     * @return 列表
     */
    List<HvUser> findByUserNameContains(String name);

    /**
     * 根据名称和描述查询
     *
     * @param name        名称
     * @param description 描述
     * @param pageable    分页
     * @return brewage
     */
    Page<HvUser> findByUserNameContainsAndDescriptionContains(String name, String description, Pageable pageable);

}
