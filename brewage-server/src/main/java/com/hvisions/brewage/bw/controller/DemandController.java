package com.hvisions.brewage.bw.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.bw.dto.DemandQueryReq;
import com.hvisions.brewage.bw.dto.DemandReq;
import com.hvisions.brewage.bw.service.IDemandDetailService;
import com.hvisions.brewage.bw.service.IDemandService;
import com.hvisions.brewage.bw.vo.DemandVO;
import com.hvisions.brewage.consts.ApiMessage;
import com.hvisions.brewage.consts.CommonConsts;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 回酒日需求单管理相关接口
 *
 * <AUTHOR>
 * 2022-04-13
 */
@RestController
@RequestMapping("/system/Demand")
@Api(value = "回酒日需求单管理相关接口", tags = "回酒日需求单管理相关接口")
public class DemandController {

    @Resource
    IDemandService baseService;
    @Resource
    IDemandDetailService demandDetailService;
    @Resource
    BaseWrapper baseWrapper;

    /**
     * 查询分页回酒日需求单
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页回酒日需求单")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<DemandVO> queryPagedDemand(DemandQueryReq req, PageInfo pageInfo) {
        return baseService.queryPagedDemand(req, pageInfo);
    }

    /**
     * 日计划新增回酒日需求单
     *
     * @param req 提报入参
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/4/19 14:42
     */
    @PostMapping("/add")
    @ApiOperation(value = "日计划新增回酒日需求单")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String add(@RequestBody DemandReq req) {
        baseService.addInfo(req);
        return ApiMessage.ADD_SUCCESS;
    }

    /**
     * 接收回酒日需求单
     *
     * @param demandIdList 需求单id列表
     * @return java.lang.String
     */
    @PutMapping("/receive")
    @ApiOperation(value = "接收回酒日需求单")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String receive(@RequestBody List<Integer> demandIdList) {
        baseService.receiveInfo(demandIdList);
        return ApiMessage.SAVE_SUCCESS;
    }
}