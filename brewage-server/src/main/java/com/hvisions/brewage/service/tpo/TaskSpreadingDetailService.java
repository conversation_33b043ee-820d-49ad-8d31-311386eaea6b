package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskLoadingMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingDetailParameterMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.TaskLoading;
import com.hvisions.brewage.entity.tpo.TaskSpreading;
import com.hvisions.brewage.entity.tpo.TaskSpreadingDetail;
import com.hvisions.brewage.entity.tpo.TaskSpreadingDetailParameter;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.WorkshopFullPitMapper;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderDTO;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.vo.tpo.TaskSpreadingDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingDetailService
 * @description: 摊晾任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSpreadingDetailService {
    @Resource
    private TaskSpreadingDetailMapper taskSpreadingDetailMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private BaseWrapper baseWrapper;

    @Resource
    private TaskSpreadingMapper taskSpreadingMapper;

    @Resource
    private LogService logService;

    @Resource
    private TaskLoadingMapper taskLoadingMapper;

    @Resource
    private TaskSpreadingDetailParameterMapper taskSpreadingDetailParameterMapper;

    @Autowired
    public WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private WorkshopFullPitMapper workshopFullPitMapper;

    @Resource
    private TaskLoadingService taskLoadingService;

    @Resource
    private TaskSealingService taskSealingService;

    @Resource
    private TaskLoadingDetailService taskLoadingDetailService;

    @Resource
    private RouteParameterWarnService routeParameterWarnService;

    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    private MaintainTaskService maintainTaskService;


    /**
     * 查询摊晾任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskSpreadingDetailVO> findPageList(TaskSpreadingDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskSpreadingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreadingDetail::getDeleted, 0)
                .eq(TaskSpreadingDetail::getTaskSpreadingId, queryDTO.getTaskSpreadingId());
        IPage<TaskSpreadingDetail> page = taskSpreadingDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskSpreadingDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskSpreadingDetailVO.class);

        if (!CollectionUtils.isEmpty(detailVOPage.getRecords())) {
            detailVOPage.getRecords().forEach(v -> {
                LambdaQueryWrapper<TaskSpreadingDetailParameter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskSpreadingDetailParameter::getTaskSpreadingDetailId, v.getId())
                        .eq(TaskSpreadingDetailParameter::getDeleted, 0);
                List<TaskSpreadingDetailParameter> parameterList = taskSpreadingDetailParameterMapper.selectList(queryWrapper);
                if (!CollectionUtils.isEmpty(parameterList)) {
                    //摊晾机输送链板左侧糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorLeftGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORLEFTGRAINTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorLeftGrainTempConvert = DtoMapper.convertList(conveyorLeftGrainTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorLeftGrainTemp(conveyorLeftGrainTempConvert);

                    //摊晾机输送链板右侧糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorRightGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORRIGHTGRAINTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorRightGrainTempConvert = DtoMapper.convertList(conveyorRightGrainTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorRightGrainTemp(conveyorRightGrainTempConvert);

                    //摊晾机输送链板前端糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorFrontTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONSUME)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorFrontTempConvert = DtoMapper.convertList(conveyorFrontTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorFrontTemp(conveyorFrontTempConvert);

                    //摊晾机输送链板中端糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorMidTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORMIDTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorMidTempConvert = DtoMapper.convertList(conveyorMidTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorMidTemp(conveyorMidTempConvert);

                    //摊晾机输送链板后端糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorRearTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORREARTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorRearTempConvert = DtoMapper.convertList(conveyorRearTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorRearTemp(conveyorRearTempConvert);

                    //每台风机运行时长
                    List<TaskSpreadingDetailParameter> fanRuntimePerUnit = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.FANRUNTIMEPERUNIT)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> fanRuntimePerUnitConvert = DtoMapper.convertList(fanRuntimePerUnit, TaskSpreadingDetailParameterAddDTO.class);
                    v.setFanRuntimePerUnit(fanRuntimePerUnitConvert);

                    //风机运行转速
                    List<TaskSpreadingDetailParameter> fanSpeed = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.FANSPEED)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> fanSpeedConvert = DtoMapper.convertList(fanSpeed, TaskSpreadingDetailParameterAddDTO.class);
                    v.setFanSpeed(fanSpeedConvert);
                }
            });
        }

        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    public String add(TaskSpreadingDetailAddDTO addDTO) {
        log.info("调用新增摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增摊晾任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskSpreadingDetail spreadingDetail = DtoMapper.convert(addDTO, TaskSpreadingDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskSpreadingMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskSpreading> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreading::getDeleted, 0)
                .eq(TaskSpreading::getPitNo, pitNo)
                .eq(TaskSpreading::getMaterialType, addDTO.getOutMaterialType())
                .orderByDesc(TaskSpreading::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskSpreading taskSpreading = taskSpreadingMapper.selectOne(wrapper);

        Integer num=0;

        if (null != taskSpreading) {
            //判断任务是否是待执行
            if (taskSpreading.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
                taskSpreading.setStatus(TaskStatus.IN_PROGRESS.getName());
                taskSpreading.setStartTime(new Date());
                taskSpreadingMapper.updateById(taskSpreading);
            }

            //判断是不是第一甑
            LambdaQueryWrapper<TaskSpreadingDetail> wrapperDetail = new LambdaQueryWrapper<>();
            wrapperDetail.eq(TaskSpreadingDetail::getDeleted, 0)
                    .eq(TaskSpreadingDetail::getTaskSpreadingId, taskSpreading.getId());
            num = taskSpreadingDetailMapper.selectCount(wrapperDetail);
        }

        spreadingDetail.setRecordTime(new Date());
        spreadingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        spreadingDetail.setCreateTime(new Date());
        spreadingDetail.setCreatorId(userId);

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskSpreadingDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskSpreadingDetail::getDeleted, 0)
                .eq(TaskSpreadingDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskSpreadingDetail taskSpreadingDetail = taskSpreadingDetailMapper.selectOne(wrapperDetail);
        if (null == taskSpreadingDetail) {
            spreadingDetail.setTaskSpreadingId(null!=taskSpreading?taskSpreading.getId():null);
            taskSpreadingDetailMapper.insert(spreadingDetail);
        } else {
            spreadingDetail.setId(taskSpreadingDetail.getId());
            taskSpreadingDetailMapper.updateById(spreadingDetail);
        }


        //插入参数信息
        //摊晾机输送链板左侧糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorLeftGrainTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORLEFTGRAINTEMP, addDTO.getConveyorLeftGrainTemp());
        }

        //摊晾机输送链板右侧糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorRightGrainTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORRIGHTGRAINTEMP, addDTO.getConveyorRightGrainTemp());
        }

        //摊晾机输送链板前端糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorFrontTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONSUME, addDTO.getConveyorFrontTemp());
        }

        //摊晾机输送链板中端糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorMidTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORMIDTEMP, addDTO.getConveyorMidTemp());
        }

        //摊晾机输送链板后端糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorRearTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORREARTEMP, addDTO.getConveyorRearTemp());
        }

        //每台风机运行时长
        if (!CollectionUtils.isEmpty(addDTO.getFanRuntimePerUnit())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.FANRUNTIMEPERUNIT, addDTO.getFanRuntimePerUnit());
        }

        //风机运行转速
        if (!CollectionUtils.isEmpty(addDTO.getFanSpeed())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.FANSPEED, addDTO.getFanSpeed());
        }


        //任务存在，明细没有的时候才能说明是第一甑
        if (null != taskSpreading && num==0) {
            log.info("当前摊晾明细为第一甑数据，iot传递的参数为：-----》{}",JSONObject.toJSONString(addDTO));
            //入窖窖号：根据iot传的窖号查询连窖号
            String targetPitNo = taskSpreadingMapper.selectPitNo(addDTO.getTargetPitNo());

            //查询是否创建了入窖任务和封窖任务，创建了则不管，没创建则创建,有两种方式：
            //第一次：入窖窖号+糟源类别为空+订单状态等于-1 查出来有就用，没有执行第二次
            // 第二次：入窖窖号+糟源类别+订单状态等于0，查出来有就用，没有就新增

            //执行第一次查询
            String orderCode = taskSpreadingDetailMapper.selectWorkshopPitOrderOne(targetPitNo);

            if (StringUtils.isEmpty(orderCode)) {
                //第一次查询出来没有，执行第二次查询
                orderCode = taskSpreadingDetailMapper.selectWorkshopPitOrderTwo(targetPitNo, addDTO.getEnterMaterialType());
            }


            //入窖任务id
            Integer taskLoadingId = 0;
            if (!StringUtils.isEmpty(orderCode)) {
                log.info("根据窖池号、糟源类型、订单状态：新建或入窖，查询出来已存在窖池订单，无需在创建，窖池号：{}，糟源类型：{}", taskSpreading.getPitNo(), addDTO.getEnterMaterialType());

                //查询入窖任务是否有数据
                LambdaQueryWrapper<TaskLoading> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskLoading::getPitOrder, orderCode)
                        .eq(TaskLoading::getDeleted, 0);
                TaskLoading taskLoading = taskLoadingMapper.selectOne(queryWrapper);
                if (null == taskLoading) {
                    log.info("根据连窖窖号查询出来没有入窖任务，连窖窖号：{}", orderCode);
                    //创建入窖任务
                    taskLoadingId = addTaskLoading(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);

                    //关闭养护任务
                    maintainTaskService.endMaintainTask(orderCode);


                    //空窖时长有工艺管控要求，需要进行工艺告警
                    BigDecimal cellarTime = emptyCellarTime(orderCode, targetPitNo);
                    routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.LOADING,
                            RouteParameterWarnParamEnum.RJ_EMPTY_TIME.getParamCode(),
                            taskSpreading.getPitOrder(),
                            addDTO.getOutMaterialType(),
                            addDTO.getEnterMaterialType(),
                            cellarTime
                    );

                    //创建封窖任务
                    addTaskSealing(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);
                } else {
                    log.info("根据连窖窖号查询出来有入窖任务，入窖任务号：{}", taskLoading.getTaskNo());
                    taskLoadingId = taskLoading.getId();
                }

            } else {
                log.info("根据窖池号、糟源类型、订单状态：新建或入窖，未查询出来已存在窖池订单，需要在创建，窖池号：{}，糟源类型：{}", taskSpreading.getPitNo(), addDTO.getEnterMaterialType());
                //创建窖池订单
                orderCode = addWorkshopPitOrder(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, userId, addDTO.getEnterMaterialType());


                //创建入窖任务
                taskLoadingId = addTaskLoading(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);

                //关闭养护任务
                maintainTaskService.endMaintainTask(orderCode);

                //空窖时长有工艺管控要求，需要进行工艺告警
                BigDecimal cellarTime = emptyCellarTime(null, targetPitNo);
                routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.LOADING,
                        RouteParameterWarnParamEnum.RJ_EMPTY_TIME.getParamCode(),
                        taskSpreading.getPitOrder(),
                        addDTO.getOutMaterialType(),
                        addDTO.getEnterMaterialType(),
                        cellarTime
                );

                //创建封窖任务
                addTaskSealing(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);

            }

            //添加明细数据，进行数据关联
            addTaskLoadingDetail(addDTO,taskLoadingId);


            //校验参数是否有工艺异常，有则告警
            //1.摊晾斗内糟醅温度
            if(!StringUtils.isEmpty(addDTO.getHopperTemp())){
                routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SPREADING,
                        RouteParameterWarnParamEnum.TL_TEMPERATURE.getParamCode(),
                        taskSpreading.getPitOrder(),
                        addDTO.getOutMaterialType(),
                        addDTO.getEnterMaterialType(),
                        new BigDecimal(addDTO.getHopperTemp())
                );
            }

            //2.单甑加曲量
            if(!StringUtils.isEmpty(addDTO.getYeastAmount())){
                routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SPREADING,
                        RouteParameterWarnParamEnum.TL_DZJQL.getParamCode(),
                        taskSpreading.getPitOrder(),
                        addDTO.getOutMaterialType(),
                        addDTO.getEnterMaterialType(),
                        new BigDecimal(addDTO.getYeastAmount())
                );
            }
        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 新增窖池订单
     *
     * @param centerId   中心id
     * @param locationId 车间ID
     * @param pitNo      连窖的窖池号
     * @param userId     创建人
     * @param materialType 糟源类别
     * @return 窖池订单号
     */
    public String addWorkshopPitOrder(Integer centerId, Integer locationId, String pitNo, Integer userId,String materialType) {
        log.info("开始调用新增窖池订单，传入参数-----》中心id：{}，车间ID:{},连窖的窖池号:{},创建人：{}", centerId, locationId, pitNo, userId);
        WorkshopPitOrderDTO workshopPitOrderDTO = new WorkshopPitOrderDTO();

        //根据连窖号查询窖池id
        LambdaQueryWrapper<TPoWorkshopFullPit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TPoWorkshopFullPit::getFullPitId, pitNo)
                .eq(TPoWorkshopFullPit::getIsDeleted,0);
        TPoWorkshopFullPit workshopFullPit = workshopFullPitMapper.selectOne(wrapper);

        //查询层次:入窖窖号+订单状态（-1和0和1），查出来有就+1，没有就是2
        Integer layer = taskSpreadingDetailMapper.selectWorkshopPitOrderLayer(pitNo);
        log.info("根据入窖窖号：{}，查询出来的层次为：{}", pitNo, layer);
        if (null == layer) {
            layer = 2;
        } else {
            layer = layer + 1;
        }

        //糟源类别id
        Integer vinasseId = taskSpreadingDetailMapper.selectVinasseId(materialType);

        workshopPitOrderDTO.setCenterId(centerId);
        workshopPitOrderDTO.setCreateId(userId);
        workshopPitOrderDTO.setLayer(layer);
        workshopPitOrderDTO.setLocationId(locationId);
        workshopPitOrderDTO.setOrderCategoryId(1);
        workshopPitOrderDTO.setOrderStatus(5);
        workshopPitOrderDTO.setPitId(workshopFullPit.getId());
        workshopPitOrderDTO.setPitStatus(0);
        workshopPitOrderDTO.setUpperOutVinasseId(vinasseId);
        workshopPitOrderDTO.setVinasseId(vinasseId);
        return workshopPitOrderService.addWorkshopPitOrderRJ(workshopPitOrderDTO);
    }


    /**
     * 查询空窖时长小时差值（保留1位小数）
     * @param pitOrder 窖池订单号
     * @param pitNo 窖池号
     * @return
     */
    public BigDecimal emptyCellarTime(String pitOrder,String pitNo){
        //根据窖池订单和糟源类别去查询是否有空窖时长，如果有则取，没有则查询滴二层订单,状态为空窖的
        if(!StringUtils.isEmpty(pitOrder)){
            LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TPoWorkshopPitOrder::getOrderCode,pitOrder)
                    .eq(TPoWorkshopPitOrder::getIsDeleted,0);
            TPoWorkshopPitOrder tPoWorkshopPitOrder = workshopPitOrderMapper.selectOne(wrapper);
            if(null==tPoWorkshopPitOrder || null== tPoWorkshopPitOrder.getEmptyStartTime()){
                return BigDecimal.ZERO;
            }

            Date emptyStartTime = tPoWorkshopPitOrder.getEmptyStartTime();

            //计算差值
            return DateUtil.calculateHoursDifference(emptyStartTime, new Date());
        }else {
            //根据连窖号查询窖池id
            LambdaQueryWrapper<TPoWorkshopFullPit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TPoWorkshopFullPit::getFullPitId, pitNo)
                    .eq(TPoWorkshopFullPit::getIsDeleted,0);
            TPoWorkshopFullPit workshopFullPit = workshopFullPitMapper.selectOne(wrapper);

            //根据窖池号、订单状态（空窖）、层次2，查询窖池订单空窖时长
            LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TPoWorkshopPitOrder::getPitId,workshopFullPit.getId())
                    .eq(TPoWorkshopPitOrder::getIsDeleted,0)
                    .eq(TPoWorkshopPitOrder::getPitStatus,0)
                    .eq(TPoWorkshopPitOrder::getLayer,2)
                    .last("limit 1");
            TPoWorkshopPitOrder tPoWorkshopPitOrder = workshopPitOrderMapper.selectOne(wrapper1);
            if(null==tPoWorkshopPitOrder || null== tPoWorkshopPitOrder.getEmptyStartTime()){
                return BigDecimal.ZERO;
            }

            Date emptyStartTime = tPoWorkshopPitOrder.getEmptyStartTime();

            //计算差值
            return DateUtil.calculateHoursDifference(emptyStartTime, new Date());
        }
    }

    /**
     * 创建入窖任务
     *
     * @param centerId     中心id
     * @param locationId   车间ID
     * @param pitNo        连窖的窖池号
     * @param materialType 糟源类别
     * @param pitOrder     窖池订单号
     * @return
     */
    public Integer addTaskLoading(Integer centerId, Integer locationId, String pitNo, String materialType, String pitOrder) {
        TaskLoadingAddDTO addDTO = new TaskLoadingAddDTO();
        addDTO.setCenterId(centerId);
        addDTO.setLocationId(locationId);
        addDTO.setPitNo(pitNo);
        addDTO.setMaterialType(materialType);
        addDTO.setPitOrder(pitOrder);
        return taskLoadingService.add(addDTO);
    }

    /**
     * 创建封窖任务
     *
     * @param centerId     中心id
     * @param locationId   车间ID
     * @param pitNo        连窖的窖池号
     * @param materialType 糟源类别
     * @param pitOrder     窖池订单号
     * @return
     */
    public String addTaskSealing(Integer centerId, Integer locationId, String pitNo, String materialType, String pitOrder) {
        TaskSealingAddDTO addDTO = new TaskSealingAddDTO();
        addDTO.setCenterId(centerId);
        addDTO.setLocationId(locationId);
        addDTO.setPitNo(pitNo);
        addDTO.setMaterialType(materialType);
        addDTO.setPitOrder(pitOrder);
        return taskSealingService.add(addDTO);
    }

    /**
     * 创建入窖详情任务
     *
     * @return
     */
    public String addTaskLoadingDetail(TaskSpreadingDetailAddDTO addDTO,Integer taskLoadingId) {
        TaskLoadingDetailAddDTO detailAddDTO = DtoMapper.convert(addDTO, TaskLoadingDetailAddDTO.class);
        return taskLoadingDetailService.add(detailAddDTO,taskLoadingId);
    }

    /**
     * 插入详情参数
     *
     * @param taskSpreadingDetailId 详情id
     * @param metricName            采集项名称
     * @param addDTOList            采集数据
     */
    private void addSpreadingDetailParameter(Integer taskSpreadingDetailId, String metricName, List<TaskSpreadingDetailParameterAddDTO> addDTOList) {
        addDTOList.forEach(v -> {
            TaskSpreadingDetailParameter spreadingDetailParameter = new TaskSpreadingDetailParameter();
            spreadingDetailParameter.setCollectionTime(v.getCollectionTime());
            spreadingDetailParameter.setMeasuredValue(v.getMeasuredValue());
            spreadingDetailParameter.setTaskSpreadingDetailId(taskSpreadingDetailId);
            spreadingDetailParameter.setMetricName(metricName);
            taskSpreadingDetailParameterMapper.insert(spreadingDetailParameter);
        });
    }

    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSpreadingDetailAddDTO> addDTOList) {
        log.info("调用批量新增摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增摊晾任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.SPREADING_IOT_TO_MES);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除摊晾任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskSpreadingDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 入窖任务更改入窖窖池-改入
     * @param iotTaskNo 工序任务号(IOT任务号)
     * @param enterMaterialType 入窖糟源类别
     * @param targetPitNo 入窖窖号
     * @return
     */
    public String updateTaskSpreadingDetailIn(String iotTaskNo, String enterMaterialType, String targetPitNo) {
        log.info("入窖任务更改入窖窖池-改入，刷新摊晾任务明细的数据，传入参数------》工序任务号(IOT任务号)：{}，入窖糟源类别：{}，入窖窖号：{}",iotTaskNo,enterMaterialType,targetPitNo);
        Integer userId = userAuditorAware.getUserId();
        log.info("入窖任务更改入窖窖池-改入，刷新摊晾任务明细的数据，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<TaskSpreadingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreadingDetail::getIotTaskNo,iotTaskNo)
                .eq(TaskSpreadingDetail::getDeleted,0);
        TaskSpreadingDetail spreadingDetail = taskSpreadingDetailMapper.selectOne(wrapper);
        spreadingDetail.setEnterMaterialType(enterMaterialType);
        spreadingDetail.setTargetPitNo(targetPitNo);
        spreadingDetail.setUpdaterId(userId);
        spreadingDetail.setUpdateTime(new Date());
        taskSpreadingDetailMapper.updateById(spreadingDetail);

        return OperationResult.UPDATE_FAILED.getDescription();
    }

    /**
     * 入窖任务更改入窖窖池-改出
     * @param iotTaskNo 工序任务号(IOT任务号)
     * @param outMaterialType 出窖糟源类别
     * @param sourcePitNo 出窖窖号
     * @return
     */
    public String updateTaskSpreadingDetailOut(String iotTaskNo, String outMaterialType, String sourcePitNo) {
        log.info("入窖任务更改入窖窖池-改出，刷新摊晾任务明细的数据，传入参数------》工序任务号(IOT任务号)：{}，出窖糟源类别：{}，出窖窖号：{}",iotTaskNo,outMaterialType,sourcePitNo);
        Integer userId = userAuditorAware.getUserId();
        log.info("入窖任务更改入窖窖池-改出，刷新摊晾任务明细的数据，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<TaskSpreadingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreadingDetail::getIotTaskNo,iotTaskNo)
                .eq(TaskSpreadingDetail::getDeleted,0);
        TaskSpreadingDetail spreadingDetail = taskSpreadingDetailMapper.selectOne(wrapper);
        spreadingDetail.setOutMaterialType(outMaterialType);
        spreadingDetail.setSourcePitNo(sourcePitNo);
        spreadingDetail.setUpdaterId(userId);
        spreadingDetail.setUpdateTime(new Date());
        taskSpreadingDetailMapper.updateById(spreadingDetail);

        return OperationResult.UPDATE_FAILED.getDescription();
    }
}
