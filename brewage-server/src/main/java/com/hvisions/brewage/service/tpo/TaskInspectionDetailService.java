package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskInspectionDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskInspectionMapper;
import com.hvisions.brewage.dto.tpo.TaskInspectionDetailAbnormalDTO;
import com.hvisions.brewage.dto.tpo.TaskInspectionDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskInspectionDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskInspection;
import com.hvisions.brewage.entity.tpo.TaskInspectionDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.WorkshopFullPitMapper;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.vo.tpo.TaskInspectionDetailVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskInspectionDetailService
 * @description: 窖池检查任务明细Service
 * @date 2025/7/15 10:46
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskInspectionDetailService {
    @Resource
    private TaskInspectionMapper inspectionMapper;

    @Resource
    private TaskInspectionDetailMapper inspectionDetailMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private WorkshopFullPitMapper workshopFullPitMapper;

    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;


    /**
     * 查询窖池检查任务明细列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskInspectionDetailVO> findPageList(TaskInspectionDetailQueryDTO queryDTO) {
        Page<TaskInspectionDetailVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskInspectionDetailVO> inspectionVOList = inspectionDetailMapper.selectPageList(page, queryDTO);

        page.setRecords(inspectionVOList);
        return page;

    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    public String add(TaskInspectionDetailAddDTO addDTO) {
        log.info("调用新增窖池检查任务明细列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增窖池检查任务明细列表，获取到当前登录用户，用户id：{}", userId);

        TaskInspectionDetail inspectionDetail = DtoMapper.convert(addDTO, TaskInspectionDetail.class);

        //查询明细窖池号数据是否已创建
        LambdaQueryWrapper<TaskInspectionDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskInspectionDetail::getTaskInspectionId, addDTO.getTaskInspectionId())
                .eq(TaskInspectionDetail::getPitNo, addDTO.getPitNo())
                .eq(TaskInspectionDetail::getDeleted, 0);
        Integer count = inspectionDetailMapper.selectCount(wrapper);
        if (count > 0) {
            FailureCode addFailedDataExists = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailedDataExists.getCode(), addFailedDataExists.getDescription(addDTO.getPitNo()));
        }

        inspectionDetail.setCreateTime(new Date());
        inspectionDetail.setCreatorId(userId);
        inspectionDetailMapper.insert(inspectionDetail);

        //修改窖池订单密封水检测时间
        updatePitOrder(addDTO.getPitNo());

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 结束任务详情
     *
     * @param taskInspectionId 窖池检查任务主键id
     * @return
     */
    public String closeTaskDetail(Integer taskInspectionId) {
        log.info("调用窖池检查任务明细-结束任务详情，传入参数-------》窖池检查任务主键id:{}", taskInspectionId);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用窖池检查任务明细-结束任务详情，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskInspectionDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskInspectionDetail::getTaskInspectionId, taskInspectionId)
                .eq(TaskInspectionDetail::getIsInspection, false)
                .eq(TaskInspectionDetail::getDeleted, 0);
        List<TaskInspectionDetail> detailList = inspectionDetailMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(detailList)) {
            detailList.forEach(v -> {
                v.setIsInspection(true);
                v.setInspectorId(userId);
                v.setInspectionTime(new Date());
                v.setUpdaterId(userId);
                v.setUpdateTime(new Date());
                inspectionDetailMapper.insert(v);

                //修改窖池订单密封水检测时间
                updatePitOrder(v.getPitNo());
            });
        }

        return OperationResult.OTHER_SUCCESS.getDescription("结束任务详情");
    }

    /**
     * 修改窖池订单密封水检测时间
     *
     * @param pitNo 窖池号
     */
    public void updatePitOrder(String pitNo) {
        log.info("修改窖池订单密封水检测时间,传入参数：窖池号----》{}", pitNo);
        //根据连窖号查询连窖id
        LambdaQueryWrapper<TPoWorkshopFullPit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TPoWorkshopFullPit::getFullPitId, pitNo)
                .eq(TPoWorkshopFullPit::getIsDeleted,0);
        TPoWorkshopFullPit workshopFullPit = workshopFullPitMapper.selectOne(wrapper);

        LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TPoWorkshopPitOrder::getPitId,workshopFullPit.getId())
                .eq(TPoWorkshopPitOrder::getPitStatus,1)
                .eq(TPoWorkshopPitOrder::getIsDeleted,0);
        TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(wrapper1);
        if(null!=workshopPitOrder){
            workshopPitOrder.setSealWaterTestTime(new Date());
            workshopPitOrderMapper.updateById(workshopPitOrder);
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除窖池检查任务明细列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除窖池检查任务明细列表，获取到当前登录用户，用户id：{}", userId);

        inspectionDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 异常处理
     *
     * @param abnormalDTO
     * @return
     */
    public String abnormal(TaskInspectionDetailAbnormalDTO abnormalDTO) {
        log.info("调用窖池检查任务明细-异常处理，传入参数-------》{}", JSONObject.toJSONString(abnormalDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用窖池检查任务明细-异常处理，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskInspectionDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskInspectionDetail::getTaskInspectionId, abnormalDTO.getTaskInspectionId())
                .in(TaskInspectionDetail::getPitNo, abnormalDTO.getPitNo())
                .eq(TaskInspectionDetail::getIsInspection, false)
                .eq(TaskInspectionDetail::getDeleted, 0);
        List<TaskInspectionDetail> detailList = inspectionDetailMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(detailList)) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_EXISTS_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("窖池号已完成处理"));
        }

        detailList.forEach(v -> {
            v.setWaterLevelOk(abnormalDTO.getWaterLevelOk());
            v.setIsContaminated(abnormalDTO.getIsContaminated());
            v.setTreatmentMeasures(abnormalDTO.getTreatmentMeasures());
            v.setRemarks(abnormalDTO.getRemarks());
            v.setIsInspection(true);
            v.setInspectorId(userId);
            v.setInspectionTime(new Date());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            inspectionDetailMapper.insert(v);

            //修改窖池订单密封水检测时间
            updatePitOrder(v.getPitNo());
        });

        return OperationResult.OTHER_SUCCESS.getDescription("异常处理");
    }
}
