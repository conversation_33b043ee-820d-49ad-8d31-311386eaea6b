# 停产物料清仓管理系统设计文档

## 概述

停产物料清仓管理系统是一个用于一年两次集中检修期间的物料管理功能。系统通过计算各筒仓的剩余发放量，指导原辅料发放以达到清空筒仓的目的。系统支持高粱和稻壳两种物料类型，采用分层架构设计，与现有的原辅料生产系统集成。

## 架构

### 系统架构图

```mermaid
graph TB
    A[前端界面] --> B[Controller层]
    B --> C[Service层]
    C --> D[Repository/Mapper层]
    D --> E[数据库]
    
    C --> F[中控系统集成]
    C --> G[车间需求系统集成]
    C --> H[物料发放记录系统]
    
    subgraph "外部系统"
        F
        G
        H
    end
    
    subgraph "核心业务层"
        I[高粱清仓计算服务]
        J[稻壳清仓计算服务]
        K[筒仓数据聚合服务]
        L[实时库存服务]
    end
    
    C --> I
    C --> J
    C --> K
    C --> L
```

### 技术栈

- **后端框架**: Spring Boot
- **数据访问**: MyBatis Plus + JPA
- **数据库**: MySQL/Oracle (支持多数据库)
- **API文档**: Swagger
- **日志**: SLF4J + Logback
- **缓存**: Redis (可选)
- **验证**: Hibernate Validator

## 组件和接口

### 1. Controller层

#### ShutdownMaterialClearanceController
负责处理停产物料清仓相关的HTTP请求。

**主要接口:**
- `GET /api/rawmaterial/shutdown-clearance/sorghum` - 获取高粱清仓统计
- `GET /api/rawmaterial/shutdown-clearance/rice-husk` - 获取稻壳清仓统计
- `POST /api/rawmaterial/shutdown-clearance/calculate` - 重新计算清仓数据
- `GET /api/rawmaterial/shutdown-clearance/export` - 导出清仓统计数据

### 2. Service层

#### ShutdownMaterialClearanceService
核心业务服务接口，定义清仓管理的主要业务方法。

**主要方法:**
```java
// 获取高粱清仓统计数据
SiloRemainingDispenseDTO getSorghumClearanceStatistics(Date statisticsTime);

// 获取稻壳清仓统计数据
SiloRemainingDispenseDTO getRiceHuskClearanceStatistics(Date statisticsTime);

// 重新计算清仓数据
void recalculateClearanceData(String materialType, Date statisticsTime);

// 导出清仓统计数据
List<SiloClearanceExportDTO> exportClearanceData(String materialType, Date statisticsTime);
```

#### SorghumClearanceCalculationService
高粱物料清仓计算服务，实现高粱物料的筒仓剩余发放量计算逻辑。

**计算流程:**
1. 中心碎料斗筒仓 → 中心碎料仓 → 中心缓存仓 → 后处理暂存仓 → 前处理存储仓
2. 每个层级的剩余发放量 = 上一层级剩余发放量 - 当前层级库存

#### RiceHuskClearanceCalculationService
稻壳物料清仓计算服务，实现稻壳物料的筒仓剩余发放量计算逻辑。

**计算流程:**
1. 中心缓存仓 → 熟稻壳缓存仓 → 后处理暂存仓 → 前处理存储仓
2. 支持A/B线分别计算（709-714中心 和 715-719中心）
3. 熟稻壳缓存仓显示汇总和三个筒仓分别的库存

#### SiloDataAggregationService
筒仓数据聚合服务，负责从各个数据源聚合筒仓相关数据。

**主要功能:**
- 获取实时库存数据
- 聚合车间需求数据
- 计算已发放量
- 数据缓存和更新

#### RealTimeInventoryService
实时库存服务，负责与中控系统集成获取实时库存数据。

### 3. Repository/Mapper层

#### ShutdownMaterialDemandRepository
停产物料需求数据访问层，基于JPA Repository。

#### SiloInventoryMapper
筒仓库存数据访问层，基于MyBatis。

#### MaterialDispenseRecordMapper
物料发放记录数据访问层，基于MyBatis。

## 数据模型

### 1. 核心DTO

#### SiloRemainingDispenseDTO
筒仓剩余发放量主DTO，包含完整的清仓统计信息。

```java
public class SiloRemainingDispenseDTO {
    private String materialType;           // 物料类型
    private Date statisticsTime;           // 统计时间
    private Date shutdownStartTime;        // 停产统计开始时间
    private List<WorkshopDemandDTO> workshopDemands;  // 车间需求列表
    private List<SiloInfoDTO> siloInfos;   // 筒仓信息列表
}
```

#### WorkshopDemandDTO
车间需求信息DTO。

```java
public static class WorkshopDemandDTO {
    private String workshopName;           // 车间名称
    private BigDecimal demandQuantity;     // 需求量
    private BigDecimal dispensedQuantity;  // 已发放量
    private BigDecimal stockQuantity;      // 库存量
    private BigDecimal remainingDispenseQuantity; // 剩余发放量
    private BigDecimal dispenseProgress;   // 发放进度
    private Date demandReportTime;         // 需求提报时间
    private String unit;                   // 单位
}
```

#### SiloInfoDTO
筒仓信息DTO。

```java
public static class SiloInfoDTO {
    private String siloType;               // 筒仓类型
    private String siloCode;               // 筒仓编码
    private String siloName;               // 筒仓名称
    private BigDecimal stockQuantity;      // 库存量
    private BigDecimal remainingDispenseQuantity; // 剩余发放量
    private String calculationDescription; // 计算说明
    private String unit;                   // 单位
    private Integer sortOrder;             // 排序序号
    private Boolean enabled;               // 是否启用
}
```

### 2. 查询DTO

#### SiloClearanceQueryDTO
清仓查询条件DTO。

```java
public class SiloClearanceQueryDTO {
    private String materialType;           // 物料类型
    private Date statisticsTime;           // 统计时间
    private Date shutdownStartTime;        // 停产开始时间
    private List<String> workshopNames;   // 车间名称列表
    private List<String> siloTypes;       // 筒仓类型列表
}
```

### 3. 导出DTO

#### SiloClearanceExportDTO
清仓数据导出DTO。

```java
public class SiloClearanceExportDTO {
    private String materialType;           // 物料类型
    private String siloType;               // 筒仓类型
    private String siloName;               // 筒仓名称
    private String workshopName;           // 车间名称
    private BigDecimal demandQuantity;     // 需求量
    private BigDecimal dispensedQuantity;  // 已发放量
    private BigDecimal stockQuantity;      // 库存量
    private BigDecimal remainingDispenseQuantity; // 剩余发放量
    private BigDecimal dispenseProgress;   // 发放进度
    private Date statisticsTime;           // 统计时间
}
```

### 4. 实体类

#### TMpdShutdownMaterialDemand
停产物料需求实体类（已存在）。

#### TMpdSiloInventory
筒仓库存实体类。

```java
@Entity
@Table(name = "t_mpd_silo_inventory")
public class TMpdSiloInventory extends SysBase {
    private String siloCode;               // 筒仓编码
    private String siloName;               // 筒仓名称
    private String siloType;               // 筒仓类型
    private String materialCode;           // 物料编码
    private String materialName;           // 物料名称
    private BigDecimal stockQuantity;      // 库存量
    private String unit;                   // 单位
    private Date updateTime;               // 更新时间
    private String dataSource;             // 数据来源
}
```

#### TMpdMaterialDispenseRecord
物料发放记录实体类。

```java
@Entity
@Table(name = "t_mpd_material_dispense_record")
public class TMpdMaterialDispenseRecord extends SysBase {
    private String dispenseNo;             // 发放单号
    private String workshopCode;           // 车间编码
    private String workshopName;           // 车间名称
    private String siloCode;               // 筒仓编码
    private String siloName;               // 筒仓名称
    private String materialCode;           // 物料编码
    private String materialName;           // 物料名称
    private BigDecimal dispenseQuantity;   // 发放量
    private String unit;                   // 单位
    private Date dispenseTime;             // 发放时间
    private String operatorName;           // 操作人
}
```

## 错误处理

### 异常类型

1. **数据获取异常**
   - `SiloDataNotFoundException`: 筒仓数据未找到
   - `RealTimeInventoryException`: 实时库存获取失败
   - `WorkshopDemandException`: 车间需求数据异常

2. **计算异常**
   - `ClearanceCalculationException`: 清仓计算异常
   - `InvalidMaterialTypeException`: 无效的物料类型
   - `DataInconsistencyException`: 数据不一致异常

3. **系统集成异常**
   - `CentralControlIntegrationException`: 中控系统集成异常
   - `WorkshopSystemIntegrationException`: 车间系统集成异常

### 错误处理策略

1. **数据验证**: 在Controller层进行参数验证
2. **业务异常**: 在Service层处理业务逻辑异常
3. **系统异常**: 统一异常处理器处理系统级异常
4. **日志记录**: 记录详细的错误日志便于排查
5. **用户友好**: 返回用户友好的错误信息

## 测试策略

### 1. 单元测试

- **Service层测试**: 测试业务逻辑和计算算法
- **Repository层测试**: 测试数据访问逻辑
- **工具类测试**: 测试计算工具和数据转换

### 2. 集成测试

- **Controller集成测试**: 测试API接口
- **数据库集成测试**: 测试数据持久化
- **外部系统集成测试**: 测试与中控系统和车间系统的集成

### 3. 性能测试

- **数据量测试**: 测试大数据量下的性能
- **并发测试**: 测试多用户并发访问
- **响应时间测试**: 测试接口响应时间

### 4. 测试数据

- **模拟数据**: 创建完整的测试数据集
- **边界测试**: 测试边界条件和异常情况
- **数据一致性**: 测试数据计算的准确性

## 部署和配置

### 1. 配置项

```yaml
# 停产物料清仓配置
shutdown:
  material:
    clearance:
      # 数据刷新间隔（分钟）
      refresh-interval: 5
      # 缓存过期时间（分钟）
      cache-expire-time: 10
      # 支持的物料类型
      supported-materials:
        - 高粱
        - 稻壳
      # 筒仓类型配置
      silo-types:
        sorghum:
          - 中心碎料斗
          - 中心碎料仓
          - 中心缓存仓
          - 后处理暂存仓
          - 前处理存储仓
        rice-husk:
          - 中心缓存仓
          - 熟稻壳缓存仓
          - 后处理暂存仓
          - 前处理存储仓
```

### 2. 数据库配置

- 支持MySQL和Oracle数据库
- 使用连接池管理数据库连接
- 配置读写分离（可选）

### 3. 缓存配置

- Redis缓存配置（可选）
- 本地缓存配置
- 缓存策略配置

### 4. 监控和日志

- 应用性能监控
- 业务指标监控
- 详细的业务日志记录
- 错误告警配置

## 安全考虑

### 1. 数据安全

- 敏感数据加密存储
- 数据传输加密
- 数据访问权限控制

### 2. 接口安全

- API访问认证
- 请求频率限制
- 输入参数验证

### 3. 系统安全

- 防SQL注入
- XSS防护
- CSRF防护

## 扩展性设计

### 1. 物料类型扩展

- 支持新增物料类型
- 可配置的计算规则
- 灵活的筒仓层级定义

### 2. 计算规则扩展

- 插件化的计算引擎
- 可配置的计算公式
- 支持自定义计算逻辑

### 3. 数据源扩展

- 支持多种数据源集成
- 可配置的数据获取策略
- 数据源故障转移

### 4. 界面扩展

- 响应式设计
- 多语言支持
- 主题定制