# 停产物料清仓管理系统实施任务

- [x] 1. 创建核心数据传输对象和验证

  - 实现 SiloClearanceQueryDTO 查询条件 DTO，包含物料类型、统计时间等查询参数
  - 实现 SiloClearanceExportDTO 导出数据 DTO，用于数据导出功能
  - 为所有 DTO 添加 JSR-303 验证注解，确保数据完整性
  - 创建自定义验证器用于业务规则验证（如物料类型、时间范围等）
  - _需求: 1.1, 1.2, 2.1, 2.2, 4.1, 4.2_

- [x] 2. 实现数据访问层和实体映射

  - 创建 TMpdSiloInventory 筒仓库存实体类，继承 SysBase 基类
  - 创建 TMpdMaterialDispenseRecord 物料发放记录实体类
  - 实现 SiloInventoryMapper 筒仓库存数据访问接口，包含库存查询方法
  - 实现 MaterialDispenseRecordMapper 物料发放记录数据访问接口
  - 创建对应的 MyBatis XML 映射文件，实现复杂查询逻辑
  - _需求: 1.3, 1.4, 2.3, 2.4, 5.1, 5.2_

- [ ] 3. 实现实时库存服务和数据聚合

  - 创建 RealTimeInventoryService 实时库存服务接口
  - 实现 RealTimeInventoryServiceImpl，集成中控系统获取实时库存数据
  - 创建 SiloDataAggregationService 筒仓数据聚合服务
  - 实现数据聚合逻辑，包括车间需求数据和发放记录数据的聚合
  - 添加数据缓存机制，提高查询性能
  - 实现异常处理和重试机制，确保数据获取的可靠性
  - _需求: 1.3, 1.4, 2.3, 2.4, 3.1, 3.2, 5.1, 5.3_

- [ ] 4. 实现高粱物料清仓计算服务

  - 创建 SorghumClearanceCalculationService 高粱清仓计算服务接口
  - 实现高粱物料的筒仓层级计算逻辑：中心碎料斗 → 中心碎料仓 → 中心缓存仓 → 后处理暂存仓 → 前处理存储仓
  - 实现中心碎料斗筒仓的需求量、已发放量、剩余发放量和发放进度计算
  - 实现其他筒仓的剩余发放量计算（上一层级剩余发放量-当前层级库存）
  - 添加计算结果验证和异常处理
  - 编写单元测试验证计算逻辑的正确性
  - _需求: 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 1.10, 1.11, 1.12, 1.13, 1.14_

- [ ] 5. 实现稻壳物料清仓计算服务

  - 创建 RiceHuskClearanceCalculationService 稻壳清仓计算服务接口
  - 实现稻壳物料的筒仓层级计算逻辑：中心缓存仓 → 熟稻壳缓存仓 → 后处理暂存仓 → 前处理存储仓
  - 实现中心缓存仓的需求量、已发放量、剩余发放量和发放进度计算
  - 实现熟稻壳缓存仓的 A/B 线分别计算（709-714 中心和 715-719 中心）
  - 实现熟稻壳缓存仓库存的汇总和三个筒仓分别显示
  - 实现其他筒仓的剩余发放量计算逻辑
  - 编写单元测试验证稻壳特有的计算逻辑
  - _需求: 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 2.10, 2.11, 2.12, 2.13_

- [ ] 6. 实现核心业务服务层

  - 创建 ShutdownMaterialClearanceService 核心业务服务接口
  - 实现 ShutdownMaterialClearanceServiceImpl 业务服务实现类
  - 实现 getSorghumClearanceStatistics 方法，返回高粱清仓统计数据
  - 实现 getRiceHuskClearanceStatistics 方法，返回稻壳清仓统计数据
  - 实现 recalculateClearanceData 方法，支持重新计算清仓数据
  - 实现 exportClearanceData 方法，支持数据导出功能
  - 添加事务管理和异常处理
  - _需求: 1.1, 2.1, 3.3, 3.4, 3.5, 4.3, 4.4, 5.4, 5.5_

- [ ] 7. 实现 REST API 控制器

  - 创建 ShutdownMaterialClearanceController 控制器类
  - 实现 GET /api/rawmaterial/shutdown-clearance/sorghum 接口，获取高粱清仓统计
  - 实现 GET /api/rawmaterial/shutdown-clearance/rice-husk 接口，获取稻壳清仓统计
  - 实现 POST /api/rawmaterial/shutdown-clearance/calculate 接口，重新计算清仓数据
  - 实现 GET /api/rawmaterial/shutdown-clearance/export 接口，导出清仓统计数据
  - 添加 Swagger API 文档注解
  - 实现请求参数验证和异常处理
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 8. 实现异常处理和错误管理

  - 创建业务异常类：SiloDataNotFoundException、ClearanceCalculationException 等
  - 创建系统集成异常类：CentralControlIntegrationException、WorkshopSystemIntegrationException
  - 实现全局异常处理器，统一处理各种异常类型
  - 添加详细的错误日志记录，便于问题排查
  - 实现用户友好的错误信息返回
  - 编写异常处理的单元测试
  - _需求: 3.4, 5.4, 5.5_

- [ ] 9. 实现数据导出功能

  - 创建 Excel 导出工具类，支持清仓数据导出
  - 实现 SiloClearanceExportDTO 到 Excel 的数据转换
  - 添加导出数据的格式化和样式设置
  - 实现大数据量导出的分页处理
  - 添加导出权限控制和操作日志记录
  - 编写导出功能的集成测试
  - _需求: 4.4, 4.5_

- [ ] 10. 编写服务层单元测试

  - 编写 SorghumClearanceCalculationService 的单元测试，验证高粱计算逻辑
  - 编写 RiceHuskClearanceCalculationService 的单元测试，验证稻壳计算逻辑
  - 编写 SiloDataAggregationService 的单元测试，验证数据聚合逻辑
  - 编写 RealTimeInventoryService 的单元测试，模拟中控系统集成
  - 编写 ShutdownMaterialClearanceService 的单元测试，验证业务逻辑
  - 使用 Mock 对象模拟外部依赖，确保测试的独立性
  - _需求: 3.3, 3.4, 3.5_

- [ ] 11. 编写控制器集成测试

  - 编写 ShutdownMaterialClearanceController 的集成测试
  - 测试所有 REST API 接口的正常流程和异常情况
  - 验证请求参数验证和响应数据格式
  - 测试并发访问和性能表现
  - 验证异常处理和错误响应
  - 使用 TestContainers 进行数据库集成测试
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 12. 实现系统配置和部署支持
  - 创建 application.yml 配置文件，添加停产物料清仓相关配置
  - 实现配置属性类，支持物料类型、筒仓类型等配置
  - 添加数据库迁移脚本，创建必要的数据表
  - 实现健康检查接口，监控系统运行状态
  - 添加应用启动时的数据初始化逻辑
  - 创建部署文档和运维指南
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_
