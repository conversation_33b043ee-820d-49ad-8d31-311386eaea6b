# 停产物料清仓管理系统需求文档

## 介绍

停产物料清仓管理系统是一个用于一年两次集中检修期间的物料管理功能。该系统基于车间提报的物料需求和筒仓的发放数据，计算各筒仓的剩余发放量，以指导原辅料发放达到清空筒仓的目的。系统支持高粱和稻壳两种物料类型的管理。

## 需求

### 需求 1 - 高粱物料清仓管理

**用户故事：** 作为生产管理员，我希望能够查看高粱物料各筒仓的剩余发放量统计，以便指导停产期间的物料清仓工作。

#### 验收标准

1. WHEN 用户访问高粱物料清仓页面 THEN 系统应显示固定的统计信息表头
2. WHEN 系统计算中心碎料斗筒仓信息 THEN 需求量应等于各车间提报的最新停产物料需求量加上提报更新时间之前到统计时间之间的已发放量
3. WHEN 系统计算中心碎料斗筒仓信息 THEN 已发放量应为该车间在停产统计开始时间之后中心碎料斗仓的发放量汇总
4. WHEN 系统计算中心碎料斗筒仓信息 THEN 库存量应为采集的中控实时库存量
5. WHEN 系统计算中心碎料斗筒仓信息 THEN 剩余发放量应等于需求量减去已发放量
6. WHEN 系统计算中心碎料斗筒仓信息 THEN 发放进度应等于已发放量除以需求量
7. WHEN 系统计算中心碎料仓信息 THEN 库存应为采集的中控实时库存量
8. WHEN 系统计算中心碎料仓信息 THEN 剩余发放量应等于中心碎料斗剩余发放量减去中心碎料斗库存
9. WHEN 系统计算中心缓存仓信息 THEN 库存应为采集的中控实时库存量
10. WHEN 系统计算中心缓存仓信息 THEN 剩余发放量应等于中心碎料仓剩余发放量和减去中心碎料仓库存
11. WHEN 系统计算后处理暂存仓信息 THEN 库存应为采集的中控实时库存量
12. WHEN 系统计算后处理暂存仓信息 THEN 剩余发放量应等于中心缓存仓剩余发放量减去中心缓存仓库存
13. WHEN 系统计算前处理存储仓信息 THEN 库存应为采集的中控实时库存量
14. WHEN 系统计算前处理存储仓信息 THEN 剩余发放量应等于后处理暂存仓剩余发放量和减去后处理暂存仓库存

### 需求 2 - 稻壳物料清仓管理

**用户故事：** 作为生产管理员，我希望能够查看稻壳物料各筒仓的剩余发放量统计，以便指导停产期间的物料清仓工作。

#### 验收标准

1. WHEN 用户访问稻壳物料清仓页面 THEN 系统应显示固定的统计信息表头
2. WHEN 系统计算中心缓存仓信息 THEN 需求量应等于各车间提报的最新停产物料需求量加上提报更新时间之前到统计时间之间的已发放量
3. WHEN 系统计算中心缓存仓信息 THEN 已发放量应为该车间在停产统计开始时间之后中心缓存仓的发放量汇总
4. WHEN 系统计算中心缓存仓信息 THEN 库存量应为采集的中控实时库存量
5. WHEN 系统计算中心缓存仓信息 THEN 剩余发放量应等于需求量减去已发放量
6. WHEN 系统计算中心缓存仓信息 THEN 发放进度应等于已发放量除以需求量
7. WHEN 系统计算熟稻壳缓存仓信息 THEN 库存应为采集的中控实时库存量，展示汇总和三个筒仓分别的库存
8. WHEN 系统计算熟稻壳缓存仓A/B线剩余发放量 THEN 应分别计算709到714中心和715到719中心的熟稻壳缓存仓剩余发放量和
9. WHEN 系统计算熟稻壳缓存仓信息 THEN 剩余发放量应等于中心缓存仓剩余发放量减去中心缓存仓库存
10. WHEN 系统计算后处理暂存仓信息 THEN 库存应为采集的中控实时库存量
11. WHEN 系统计算后处理暂存仓信息 THEN 剩余发放量应等于熟稻壳缓存仓剩余发放量和减去熟稻壳缓存仓库存
12. WHEN 系统计算前处理存储仓信息 THEN 库存应为采集的中控实时库存量
13. WHEN 系统计算前处理存储仓信息 THEN 剩余发放量应等于后处理暂存仓剩余发放量和减去后处理暂存仓库存

### 需求 3 - 数据实时性和准确性

**用户故事：** 作为生产管理员，我希望系统提供准确和实时的数据，以便做出正确的清仓决策。

#### 验收标准

1. WHEN 系统获取库存数据 THEN 应从中控系统采集实时库存量
2. WHEN 系统计算已发放量 THEN 应基于停产统计开始时间进行准确的时间范围过滤
3. WHEN 系统计算需求量 THEN 应获取各车间提报的最新停产物料需求量
4. WHEN 系统计算发放进度 THEN 应处理除零异常情况
5. WHEN 系统进行数据计算 THEN 应确保所有数值计算的精度和准确性

### 需求 4 - 用户界面和交互

**用户故事：** 作为生产管理员，我希望有一个清晰直观的界面来查看物料清仓信息。

#### 验收标准

1. WHEN 用户访问系统 THEN 应提供高粱和稻壳两个物料类型的选择
2. WHEN 用户查看统计信息 THEN 应显示固定格式的表头信息
3. WHEN 用户查看数据 THEN 应按照筒仓层级结构展示信息
4. WHEN 用户查看稻壳数据 THEN 应显示A/B线的分别统计信息
5. WHEN 用户查看数据 THEN 应支持统计时间的设置和查询

### 需求 5 - 系统集成和数据源

**用户故事：** 作为系统管理员，我希望系统能够与现有的中控系统和车间管理系统集成。

#### 验收标准

1. WHEN 系统获取实时库存 THEN 应与中控系统进行数据集成
2. WHEN 系统获取需求数据 THEN 应与车间物料需求管理系统集成
3. WHEN 系统获取发放数据 THEN 应与物料发放记录系统集成
4. WHEN 系统处理数据异常 THEN 应提供适当的错误处理和日志记录
5. WHEN 系统进行数据同步 THEN 应确保数据的一致性和完整性