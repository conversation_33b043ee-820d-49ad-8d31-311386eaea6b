package com.hvisions.powder.dto.qudou.demand;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

/**
 * @Description: 曲粉需求详情分页查询条件dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@ApiModel(description = "曲粉需求详情分页查询条件dto")
public class DemandDetailQuery {

    @ApiModelProperty("使用日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date useTime;

    @ApiModelProperty("中心编码")
    private String centerCode;

    @ApiModelProperty("车间")
    private String location;

    @ApiModelProperty("跨")
    private String lineName;

}
